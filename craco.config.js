const webpack = require('webpack');

module.exports = {
    webpack: {
        configure: (webpackConfig, { env, paths }) => {
            webpackConfig.module.rules.push({
                test: /\.mjs$/,
                include: /node_modules/,
                type: 'javascript/auto',
            });

            webpackConfig.resolve.fallback = {
                "stream": require.resolve("stream-browserify"),
                "path": require.resolve("path-browserify"),
                "crypto": require.resolve("crypto-browserify"),
                "timers": require.resolve("timers-browserify"),
                "http": require.resolve("stream-http"),
                "https": require.resolve("https-browserify"),
                "fs": false,
            };

            webpackConfig.plugins.push(
                new webpack.ProvidePlugin({
                    Buffer: ['buffer', 'Buffer'],
                    process: 'process/browser',
                })
            );

            return webpackConfig;
        },
    },
};
