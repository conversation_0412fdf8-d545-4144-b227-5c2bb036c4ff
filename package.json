{"name": "chat-app", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/x": "^1.0.2", "@chatui/core": "^2.4.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fortawesome/fontawesome-free": "^6.5.2", "@msgpack/msgpack": "^3.0.0-beta2", "@mui/material": "^5.15.15", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf/renderer": "^3.4.2", "@reduxjs/toolkit": "^2.2.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.18.3", "antd-style": "^3.7.1", "axios": "^1.6.8", "buffer": "^6.0.3", "crypto-browserify": "^3.12.0", "fs": "^0.0.1-security", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "https-browserify": "^1.0.0", "jspdf": "^3.0.0", "katex": "^0.16.11", "lodash": "^4.17.21", "lucide-react": "^0.452.0", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "minio": "^7.1.3", "msgpack-lite": "^0.1.26", "openai": "^4.53.2", "path-browserify": "^1.0.1", "pdfjs-dist": "^4.1.392", "process": "^0.11.10", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-icons": "^5.0.1", "react-markdown": "^9.0.1", "react-medium-image-zoom": "^5.2.9", "react-pdf": "^9.2.1", "react-quill": "^2.0.0", "react-redux": "^9.1.0", "react-resizable": "^3.0.5", "react-router-dom": "^6.22.3", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-textarea-autosize": "^8.5.3", "react-toastify": "^10.0.5", "reactflow": "^11.11.0", "recharts": "^2.12.7", "rehype-highlight": "^7.0.0", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "slate": "^0.103.0", "slate-history": "^0.109.0", "slate-hyperscript": "^0.100.0", "slate-react": "^0.110.1", "socket.io-client": "^4.7.5", "source-map-loader": "^5.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "throttle-debounce": "^5.0.2", "timers-browserify": "^2.0.12", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "ws": "^8.16.0", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && node build.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^5.9.0", "tailwindcss": "^3.4.11", "webpack-node-externals": "^3.0.0"}}