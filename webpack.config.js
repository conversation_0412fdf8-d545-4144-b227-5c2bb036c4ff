module.exports = {
  module: {
    rules: [
      {
        test: /\.(js|mjs)$/,
        enforce: "pre",
        use: ["source-map-loader"],
        resolve: {
          fullySpecified: false, // 对于 Node.js v14+ 的 ES 模块，需要关闭这个选项
        },
      },
      // 确保你的其他规则不会排除 mjs 文件
      {
        test: /\.mjs$/,
        include: /node_modules/,
        type: "javascript/auto",
      },
    ],
  },
  resolve: {
    extensions: [".js", ".mjs"], // 确保 .mjs 在解析扩展名中得到支持
  },
};
