import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Drawer, <PERSON><PERSON>, <PERSON>u } from 'antd';
import { useSelector } from 'react-redux';
import { 
  MenuOutlined, HomeOutlined, UserOutlined, FileOutlined, 
  SettingOutlined, AppstoreOutlined, FileTextOutlined, 
  FileImageOutlined, LogoutOutlined, DatabaseOutlined,
  ClusterOutlined, MessageOutlined,HistoryOutlined,RobotOutlined,
  Bar<PERSON><PERSON>Outlined
} from '@ant-design/icons';
import { 
  LLM_DIALOGUES_NAME, USER_ASSOCIATIONS_NAME, KNOWLEDGE_BASES_NAME, 
  LLMS_NAME, WORKFLOWS_NAME, PROMPTS_NAME, FILE_MANAGERS_NAME, 
  ARTICLE_NAME, LOGOUT_NAME, LOGIN_API_NAME,DASHBOARD_API_NAME,AGENTS_NAME,MCP_SERVER_NAME
} from './Configs/Config';
import styles from './SidebarList.module.css'; // 使用 CSS Modules

// 定义指标 Schema 路径常量
const INDICATOR_SCHEMA_NAME = '/indicator-schema';

const SidebarList = () => {
  const [visible, setVisible] = useState(false); // 控制侧边栏的显示/隐藏
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);

  // 展开侧边栏
  const showDrawer = () => {
    setVisible(true);
  };

  // 隐藏侧边栏
  const closeDrawer = () => {
    setVisible(false);
  };

  // 处理页面跳转和登出逻辑
  const handleNavigate = (path) => {
    if (path === LOGOUT_NAME) {
      if (window.confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        navigate(`${LOGIN_API_NAME}`);
      }
    } else if (path === ARTICLE_NAME) {
      // 导航到 ARTICLE_NAME 路径并传递 username 和 is_published 参数
      // navigate(`${ARTICLE_NAME}?username=${user_info.username}&is_published=true`);
      navigate(`${ARTICLE_NAME}?username=${user_info.username}`);
    } else {
      navigate(path);
    }
    setVisible(false);
  };
  

  // 根据 key 动态生成图标
  const getIconByKey = (key) => {
    switch (key) {
      case '/':
        return <HomeOutlined />;
      case DASHBOARD_API_NAME:
        return <MessageOutlined />;
      case USER_ASSOCIATIONS_NAME:
        return <UserOutlined />;
      case KNOWLEDGE_BASES_NAME:
        return <DatabaseOutlined />;
      case AGENTS_NAME:
        return <RobotOutlined />;
      case PROMPTS_NAME:
        return <FileTextOutlined />;
      case LLMS_NAME:
        return <AppstoreOutlined />;
      case WORKFLOWS_NAME:
        return <ClusterOutlined />;
      case FILE_MANAGERS_NAME:
        return <FileOutlined />;
      case ARTICLE_NAME:
        return <FileImageOutlined />;
      case INDICATOR_SCHEMA_NAME:
        return <BarChartOutlined />;
      case LOGOUT_NAME:
        return <LogoutOutlined />;
      case LLM_DIALOGUES_NAME:
        return <HistoryOutlined />;
      case MCP_SERVER_NAME:
          return <RobotOutlined />;
      default:
        return <SettingOutlined />;
    }
  };

  // 菜单项
  const menuItems = [
    {
      label: '首页',
      key: DASHBOARD_API_NAME,
    },
    {
      label: '用户关联',
      key: USER_ASSOCIATIONS_NAME,
    },
    {
      label: '知识库',
      key: KNOWLEDGE_BASES_NAME,
    },
    {
      label: '大模型',
      key: LLMS_NAME,
    },
    {
      label: '工作流',
      key: WORKFLOWS_NAME,
    },
    {
      label: '智能体',
      key: AGENTS_NAME,
    },
    {
      label: '提示词',
      key: PROMPTS_NAME,
    },
    {
      label: '对话历史',
      key: LLM_DIALOGUES_NAME,
    },
    {
      label: '文件管理',
      key: FILE_MANAGERS_NAME,
    },
    {
      label: '文章管理',
      key: ARTICLE_NAME,
    },
    {
      label: '指标Schema',
      key: INDICATOR_SCHEMA_NAME,
    },
    {
      label: 'MCP服务',
      key: MCP_SERVER_NAME,
    },
    {
      label: '退出登录',
      key: LOGOUT_NAME,
    },
  ];

  return (
    <>
      {/* 侧边栏展开按钮 */}
      <Button
        type="primary"
        onClick={showDrawer}
        icon={<MenuOutlined />}
        className={styles.sidebarButton} // 使用 CSS Module 中的类名
      >
        工具栏
      </Button>

      {/* 侧边栏 */}
      <Drawer
        title="导航"
        placement="right"
        onClose={closeDrawer}
        open={visible}
        width={250}
      >
        <Menu
          mode="inline"
          onClick={({ key }) => handleNavigate(key)} // 点击菜单项后导航
        >
          <Menu.Item key="/" icon={getIconByKey('/')}>
            首页
          </Menu.Item>
          {/* <Menu.Item key={DASHBOARD_API_NAME} icon={getIconByKey(DASHBOARD_API_NAME)}>
            会话
          </Menu.Item> */}
          <Menu.Item key={USER_ASSOCIATIONS_NAME} icon={getIconByKey(USER_ASSOCIATIONS_NAME)}>
            用户关联
          </Menu.Item>
          <Menu.Item key={KNOWLEDGE_BASES_NAME} icon={getIconByKey(KNOWLEDGE_BASES_NAME)}>
            知识库
          </Menu.Item>
          <Menu.Item key={AGENTS_NAME} icon={getIconByKey(AGENTS_NAME)}>
            智能体
          </Menu.Item>

          <Menu.Item key={PROMPTS_NAME} icon={getIconByKey(PROMPTS_NAME)}>
            提示词
          </Menu.Item>
          <Menu.Item key={LLMS_NAME} icon={getIconByKey(LLMS_NAME)}>
            模型
          </Menu.Item>
          <Menu.Item key={WORKFLOWS_NAME} icon={getIconByKey(WORKFLOWS_NAME)}>
            工作流
          </Menu.Item>
          <Menu.Item key={FILE_MANAGERS_NAME} icon={getIconByKey(FILE_MANAGERS_NAME)}>
            文件管理
          </Menu.Item>
          <Menu.Item key={ARTICLE_NAME} icon={getIconByKey(ARTICLE_NAME)}>
            内容管理
          </Menu.Item>
          <Menu.Item key={LLM_DIALOGUES_NAME} icon={getIconByKey(LLM_DIALOGUES_NAME)}>
            会话记录
          </Menu.Item>
          <Menu.Item 
            key={INDICATOR_SCHEMA_NAME} 
            icon={getIconByKey(INDICATOR_SCHEMA_NAME)}
            onClick={() => handleNavigate(INDICATOR_SCHEMA_NAME)}
          >
            指标 Schema
          </Menu.Item>
          <Menu.Item 
            key={MCP_SERVER_NAME} 
            icon={getIconByKey(MCP_SERVER_NAME)}
            onClick={() => handleNavigate(MCP_SERVER_NAME)}
          >
            MCP服务
          </Menu.Item>
          <Menu.Item key={LOGOUT_NAME} icon={getIconByKey(LOGOUT_NAME)}>
            退出
          </Menu.Item>
        </Menu>
      </Drawer>
    </>
  );
};

export default SidebarList;
