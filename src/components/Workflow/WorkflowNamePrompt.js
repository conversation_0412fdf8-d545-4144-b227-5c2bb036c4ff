// import React, { useState } from 'react';
// import { Modal, Form, Input, Select, Button, message } from 'antd';
// import { CloseCircleOutlined } from '@ant-design/icons';

// const { TextArea } = Input;
// const { Option } = Select;

// const WorkflowNamePrompt = ({ isOpen, onClose, onSave, workflow_name, database, domain, workflow_type, description, isEditMode }) => {
//   const [form] = Form.useForm();

//   const databaseOptions = [
//     { value: "literature", label: "文献" },
//     { value: "liter", label: "文" }
//   ];

//   const domainOptions = [
//     { value: "literature", label: "文献" },
//     { value: "liter", label: "文" }
//   ];

//   const workflowTypeOptions = [
//     { value: "文档阅读", label: "文档阅读" },
//     { value: "科普", label: "科普" },
//     { value: "科普仿写", label: "科普仿写" },
//     { value: "科普仿写_V2", label: "科普仿写_V2" },
//   ];

//   const handleSave = (values) => {
//     if (values.database.length === 0) {
//       message.error('必须选择至少一个数据库');
//       return;
//     }
//     if (!values.domain) {
//       message.error('必须选择一个领域');
//       return;
//     }
//     if (!values.workflow_type) {
//       message.error('必须选择一个工作流类型');
//       return;
//     }
//     onSave(values.workflow_name, values.database, values.domain, values.workflow_type, values.description);
//     onClose();
//   };

//   return (
//     <Modal
//       title="工作流设置"
//       open={isOpen}
//       onCancel={onClose}
//       footer={null}
//       closeIcon={<CloseCircleOutlined />}
//     >
//       <Form
//         form={form}
//         layout="vertical"
//         onFinish={handleSave}
//         initialValues={{
//           workflow_name: workflow_name || '',
//           database: database || [],
//           domain: domain || undefined,
//           workflow_type: workflow_type || undefined,
//           description: description || '',
//         }}
//       >
//         <Form.Item
//           name="database"
//           label="数据库"
//           rules={[{ required: true, message: '请选择至少一个数据库' }]}
//         >
//           <Select mode="multiple" placeholder="请选择数据库">
//             {databaseOptions.map(option => (
//               <Option key={option.value} value={option.value}>{option.label}</Option>
//             ))}
//           </Select>
//         </Form.Item>

//         <Form.Item
//           name="domain"
//           label="领域"
//           rules={[{ required: true, message: '请选择领域' }]}
//         >
//           <Select placeholder="请选择领域">
//             {domainOptions.map(option => (
//               <Option key={option.value} value={option.value}>{option.label}</Option>
//             ))}
//           </Select>
//         </Form.Item>

//         <Form.Item
//           name="workflow_type"
//           label="工作流类型"
//           rules={[{ required: true, message: '请选择工作流类型' }]}
//         >
//           <Select placeholder="请选择工作流类型">
//             {workflowTypeOptions.map(option => (
//               <Option key={option.value} value={option.value}>{option.label}</Option>
//             ))}
//           </Select>
//         </Form.Item>

//         <Form.Item
//           name="description"
//           label="描述"
//         >
//           <TextArea rows={4} placeholder="请输入描述" />
//         </Form.Item>

//         <Form.Item
//           name="workflow_name"
//           label="工作流名称"
//           rules={[{ required: true, message: '请输入工作流名称' }]}
//         >
//           <Input placeholder="请输入工作流名称" readOnly={isEditMode} />
//         </Form.Item>

//         <Form.Item>
//           <Button type="primary" htmlType="submit">
//             保存
//           </Button>
//         </Form.Item>
//       </Form>
//     </Modal>
//   );
// };

// export default WorkflowNamePrompt;