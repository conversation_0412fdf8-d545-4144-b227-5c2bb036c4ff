// export function formatDateTimeIntl() {
//     const date = new Date();

//     // 指定日期时间的格式化选项
//     const options = {
//         year: 'numeric', month: '2-digit', day: '2-digit',
//         hour: '2-digit', minute: '2-digit', hour12: false,
//         timeZone: 'Asia/Shanghai'  // 使用上海时区
//     };

//     // 使用 Intl.DateTimeFormat 来格式化日期
//     let formatter = new Intl.DateTimeFormat('zh-CN', options);
//     let parts = formatter.formatToParts(date);

//     // 构造所需格式
//     let year = parts.find(part => part.type === 'year').value;
//     let month = parts.find(part => part.type === 'month').value;
//     let day = parts.find(part => part.type === 'day').value;
//     let hour = parts.find(part => part.type === 'hour').value;
//     let minute = parts.find(part => part.type === 'minute').value;

//     return `${year}-${month}-${day} ${hour}:${minute}`;
// }

export function formatDateTimeIntl() {
    const date = new Date();

    // Specifying date and time formatting options, up to milliseconds
    const options = {
        year: 'numeric', month: '2-digit', day: '2-digit',
        hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3,
        hour12: false,
        timeZone: 'Asia/Shanghai' // Using the Shanghai time zone
    };

    // Using Intl.DateTimeFormat to format the date
    const formatter = new Intl.DateTimeFormat('zh-CN', options);
    const parts = formatter.formatToParts(date);

    // Construct the desired format
    const year = parts.find(part => part.type === 'year').value;
    const month = parts.find(part => part.type === 'month').value;
    const day = parts.find(part => part.type === 'day').value;
    const hour = parts.find(part => part.type === 'hour').value;
    const minute = parts.find(part => part.type === 'minute').value;
    const second = parts.find(part => part.type === 'second').value;
    const fractionalSecond = parts.find(part => part.type === 'fractionalSecond').value;

    // Append zeros to simulate a six-digit fractional second (this is not actual microseconds)
    const simulatedMicroseconds = fractionalSecond.padEnd(6, '0');

    // Format the time as an ISO 8601 string, simulating extended precision
    return `${year}-${month}-${day}T${hour}:${minute}:${second}.${simulatedMicroseconds}`;
}
