// AutoDeepResearchNode.js
import React, { memo, useCallback } from 'react';
import { Handle, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import { Select as AntSelect, Switch, InputNumber } from 'antd';
import styles from './DeepResearchNode.module.css';

function AutoDeepResearchNode({
  id,
  data,
  llm_options,
  name,
  database_options = [],
  mcp_server_options = [],
  reflection_prompt = []
}) {
  const { setNodes } = useReactFlow();

  const filteredReflectionOptions = reflection_prompt;
  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);

  // 处理开关变化
  const handleSwitchChange = useCallback((checked, handleId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: checked };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);

  // 处理多选变化
  const handleMultiSelectChange = useCallback((values, handleId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: values };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);

  // 处理数字输入变化
  const handleNumberChange = useCallback((value, handleId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes, id]);




  const selects = data && data.selects ? data.selects : {};

  return (
    <div className={styles.custom_node}>
      <div className={styles.custom_node_header}>{name}</div>
      <div className={styles.handle_with_label_input_top_left_1}>
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className={styles.handle_label_input_top_left_1}>用户输入</span>
      </div>
      <div className={styles.handle_with_label_input_top_left_2}>
        <Handle type="target" position={Position.Left} id="llm_input" />
        <span className={styles.handle_label_input_top_left_2}>模型输入</span>
      </div>
      <div className={styles.custom_node_body}>
        <div className={styles.llm_select_container}>
          <span className={styles.llm_label}>大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
            className={styles.llm_select}
          />
        </div>
        <div className={styles.database_select_container}>
          <span className={styles.database_label}>知识库</span>
          <AntSelect
            mode="multiple"
            placeholder="选择知识库"
            value={selects['kb_names'] || []}
            onChange={(values) => handleMultiSelectChange(values, 'kb_names')}
            options={database_options}    
            className="nodrag"
            style={{
              width: '100%',
              minHeight: '32px'
            }}
            maxTagCount="responsive"
            maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
            removeIcon={<span style={{ fontSize: '10px' }}>×</span>}
            suffixIcon={<span style={{ fontSize: '12px' }}>▼</span>}
          />
        </div>

        <div className={styles.mcp_server_select_container}>
          <span className={styles.mcp_server_label}>MCP服务</span>
          <AntSelect
            mode="multiple"
            placeholder="选择MCP服务"
            value={selects['mcp_server_names'] || []}
            onChange={(values) => handleMultiSelectChange(values, 'mcp_server_names')}
            options={mcp_server_options}
            className="nodrag"
            style={{ 
              width: '100%',
              minHeight: '32px'
            }}
            maxTagCount="responsive"
            maxTagPlaceholder={(omittedValues) => `+${omittedValues.length}...`}
            removeIcon={<span style={{ fontSize: '10px' }}>×</span>}
            suffixIcon={<span style={{ fontSize: '12px' }}>▼</span>}
          />
        </div>

        {filteredReflectionOptions && filteredReflectionOptions.length > 0 && (
          <div className={styles.reflection_prompt_select_container}>
            <span className={styles.reflection_prompt_label}>反思提示词</span>
            <Select
              key="reflection_prompt_name"
              nodeId={id}
              value={selects['reflection_prompt_name']}
              handleId="reflection_prompt_name"
              onChange={handleChange}
              options={filteredReflectionOptions}
              className={styles.reflection_prompt_select}
            />
          </div>
        )}
        <div className={styles.horizontal_controls_container}>
          <div className={styles.max_iter_container}>
            <span className={styles.max_iter_label}>最大循环轮数</span>
            <InputNumber
              min={1}
              max={100}
              value={selects['max_iter'] || 2}
              onChange={(value) => handleNumberChange(value, 'max_iter')}
              className="nodrag"
              style={{
                width: '100%',
                fontSize: '10px'
              }}
              placeholder="请输入最大循环轮数"
            />
          </div>
          <div className={styles.internet_search_container}>
            <span className={styles.internet_search_label}>互联网搜索工具</span>
            <Switch
              checked={selects['internet_search_enabled'] || false}
              onChange={(checked) => handleSwitchChange(checked, 'internet_search_enabled')}
              className={styles.internet_search_switch}
            />
          </div>
        </div>
      </div>
      
      <div className={styles.handle_with_label_output_top_right_1}>
        <Handle type="source" position={Position.Right} id="attachment_output" style={{ background: '#555' }} />
        <span className={styles.handle_label_output_top_right_1}>附件输出</span>
      </div>
      <div className={styles.outputHandle} style={{ top: '60px' }}>
        <Handle type="source" position={Position.Right} id="llm_output" style={{ background: '#555' }} />
        <span className={styles.outputHandleLabel}>模型输出</span>
      </div>
    </div>
  );
}

export default memo(AutoDeepResearchNode);