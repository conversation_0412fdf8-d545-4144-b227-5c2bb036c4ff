.parameterDisplay {
    display: inline-flex;
    align-items: center;
    margin-left: 6px;
    vertical-align: middle;
  }
  
  .parameterButton {
    background-color: transparent;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    height: 20px;
    width: 20px;
  }
  
  .parameterButton:hover {
    background-color: #f0f0f0;
    border-color: #999;
  }
  
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
  }
  
  .modalContent {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .closeButton {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
  }
  
  .closeButton:hover {
    color: #000;
  }
  
  .parameterList {
    margin-top: 15px;
  }
  
  .parameterItem {
    border-bottom: 1px solid #eee;
    padding: 12px 0;
  }
  
  .parameterItem:last-child {
    border-bottom: none;
  }
  
  .parameterHeader {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 8px;
  }
  
  .parameterName {
    font-weight: bold;
    margin-right: 8px;
    font-size: 14px;
  }
  
  .parameterType {
    background-color: #e1f5fe;
    color: #0277bd;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 12px;
    margin-right: 8px;
  }
  
  .parameterRequired {
    background-color: #ffebee;
    color: #c62828;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 12px;
  }
  
  .parameterDesc {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
  }