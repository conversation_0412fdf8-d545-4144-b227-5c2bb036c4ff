import React, { memo, useState, useCallback } from 'react';
import { Handle, Position } from 'reactflow';
import './CustomNodeWithMultipleHandles.css';

function StartNode({ id, name, isDebugMode = false }) {
  const [userInput, setUserInput] = useState('');

  const handleInputChange = useCallback((event) => {
    setUserInput(event.target.value);
  }, []);

  return (
    <div className="custom-node-start">
      <div className="custom-node__header">{name}</div>
      <div className="handle-with-label-input-top-left-1">
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className="handle-label-input-top-left-1">用户输入</span>
      </div>
      <div className="handle-with-label-output-top-right-1">
        <Handle type="source" position={Position.Right} id="query_output" style={{ background: '#555' }} />
        <span className="handle-label-output-top-right-1">用户问题</span>
      </div>
      <div className="handle-with-label-output-top-right-2">
        <Handle type="source" position={Position.Right} id="attachment_output" style={{ background: '#555' }} />
        <span className="handle-label-output-top-right-2">附件</span>
      </div>
      <div className="custom-node__body_start">
        用户起始节点
      </div>
      <div className="input-block">
        <div className="input-block__header">用户输入</div>
        <textarea
          value={userInput}
          onChange={handleInputChange}
          className="input-block__textarea"
          disabled={!isDebugMode}
          placeholder="在此输入用户问题..."
        />
      </div>
    </div>
  );
}

export default memo(StartNode);
