// LLMNODE.js
import React, { memo, useCallback, useMemo } from 'react';
import { Handle, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import styles from './LLMNode.module.css';
import { AGENT_ROUTER_NAME } from '../../Configs/Config';

function RouterNode({ id, data, llm_options }) {
  const { setNodes } = useReactFlow();


  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);




  const selects = data && data.selects ? data.selects : {};

  // 判断是否显示默认的模型输入和输出
  const showModelInput = true;
  const showModelOutput = true;
  
  // 计算最后一个输入handle的位置，用于动态调整body的位置
  const lastHandlePosition = useMemo(() => {
    // 基础handle数量（固定的）
    // 动态参数handle的数量
    const paramHandleCount = 0;
    
    const lastStaticHandlePos = 90; // 附件输入位置
    const modelInputPos = 120; // 模型输入位置
    const dynamicStartPos = 120; // 动态参数起始位置
    
    if (paramHandleCount > 0) {
      // 如果有动态参数，最后一个handle是最后一个参数
      return dynamicStartPos + (paramHandleCount - 1) * 30 + 30; // 额外加30px作为间距
    } else if (showModelInput) {
      // 如果没有动态参数但有模型输入
      return modelInputPos + 30;
    } else {
      // 如果只有固定handle
      return lastStaticHandlePos + 30;
    }
  }, [showModelInput]);
  
  // 计算节点的总高度
  const nodeHeight = useMemo(() => {
    // 表单区域的基本高度 (包括内边距、选择器等)
    const formBaseHeight = 200; // 预估表单区域的基本高度
    
    // 根据选择器动态调整
    let formHeight = formBaseHeight;
  
    
    // 计算节点的总高度 = 最后一个handle的位置 + 表单高度 + 额外的间距
    return lastHandlePosition + formHeight + 30; // 额外加30px作为底部间距
  }, [lastHandlePosition]);

  return (
    <div className={styles.customNode} style={{ minHeight: `${nodeHeight}px` }}>
      <div className={styles.customNodeHeader}>{AGENT_ROUTER_NAME}</div>
      
      {/* 输入连接点区域 */}
      <div className={styles.inputHandle} style={{ top: '60px' }}>
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className={styles.handleLabel}>用户输入</span>
      </div>
      
      <div className={styles.inputHandle} style={{ top: '90px' }}>
        <Handle type="target" position={Position.Left} id="attachment_input" />
        <span className={styles.handleLabel}>附件输入</span>
      </div>
      
      {/* 仅当参数为空时显示模型输入 */}
      {/* {showModelInput && ( */}
        <div className={styles.inputHandle} style={{ top: '120px' }}>
          <Handle type="target" position={Position.Left} id="llm_input" />
          <span className={styles.handleLabel}>模型输入</span>
        </div>
      {/* )} */}
      
      
      {/* 仅当输出参数为空时显示模型输出 */}
      {showModelOutput && (
        <div className={styles.outputHandle} style={{ top: '60px' }}>
          <Handle type="source" position={Position.Right} id="llm_output" style={{ background: '#555' }} />
          <span className={styles.outputHandleLabel}>模型输出</span>
        </div>
      )}
      
      
      {/* 表单内容区域 - 动态设置marginTop */}
      <div className={styles.customNodeBody} style={{ marginTop: `${lastHandlePosition}px` }}>
        <div className={styles.llmSelectContainer}>
          <span className={styles.llmLabel}>大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
          />
        </div>
      </div>
      <div className={styles.nodeDescription}>
        该节点作为智能路由，允许用户将其连接到多个节点路径，使大模型能够根据用户输入内容和上下文自动分析并决定流程应该沿哪条路径继续执行。这种动态路由机制让工作流能够根据不同情况智能选择最合适的处理路径，无需用户手动干预，从而创建出更灵活、更智能的对话流程。
      </div>
    </div>
  );
}

export default memo(RouterNode);
