/* FileParseNode.module.css */
.customNode {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    min-width: 280px;
    min-height: 250px;
    position: relative;
    overflow: visible;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
  }
  
  .customNode:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }
  
  .customNodeHeader {
    background: linear-gradient(90deg, #4a90e2 0%, #5ca2ff 100%);
    color: white;
    padding: 12px 15px;
    text-align: center;
    font-weight: 600;
    font-size: 16px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
  }
  
  .customNodeHeader::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, rgba(255,255,255,0.5) 0%, rgba(255,255,255,0) 100%);
  }
  
  .customNodeBody {
    padding: 18px;
  }
  
  .inputHandle, .outputHandle {
    position: absolute;
    display: flex;
    align-items: center;
    height: 24px;
  }
  
  .inputHandle {
    left: 4px;
  }
  
  .outputHandle {
    right: 4px;
    justify-content: flex-end;
  }
  
  .handleLabel {
    margin-left: 16px;
    font-size: 12px;
    color: #555;
    white-space: nowrap;
  }
  .outputHandleLabel {
    margin-right: 16px;
    font-size: 12px;
    color: #555;
    white-space: nowrap;
  }
  .selectContainer {
    margin-bottom: 16px;
  }
  
  .selectLabel {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
    color: #333;
  }
  
  .emptyMethodMessage {
    padding: 8px;
    background: rgba(245, 245, 245, 0.7);
    border-radius: 4px;
    font-size: 13px;
    color: #666;
    text-align: center;
  }
  
  .methodDetails {
    background: linear-gradient(135deg, #f8f9fa 0%, #e2e8f0 100%);
    padding: 14px;
    border-radius: 6px;
    font-size: 13px;
    color: #555;
    margin-top: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .methodProperty {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
  }
  
  .propertyLabel {
    font-weight: 500;
    width: 85px;
    flex-shrink: 0;
    color: #444;
  }
  
  .propertyValue {
    color: #2563eb;
    font-family: 'Consolas', monospace;
    background: rgba(37, 99, 235, 0.08);
    padding: 2px 6px;
    border-radius: 3px;
  }
  
  .methodDescription {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.07);
    font-style: normal;
    line-height: 1.5;
    color: #555;
  }
  
  /* Fix for handle alignment */
  .handle-with-label-output-top-right-1 {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
  }
  
  .handle-label-output-top-right-1 {
    margin-right: 16px;
    font-size: 12px;
    color: #555;
    white-space: nowrap;
  }
  
  /* Enhanced select styles */
  select {
    width: 100%;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #ddd;
    background-color: white;
    font-size: 14px;
    color: #333;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23555555' d='M6 9L1 4h10z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    transition: border-color 0.2s;
  }
  
  select:focus {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
  }
  
  select:hover {
    border-color: #bbb;
  }