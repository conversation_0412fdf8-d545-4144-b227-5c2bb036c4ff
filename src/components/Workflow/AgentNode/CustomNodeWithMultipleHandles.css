
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* Dimmed background */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* High z-index to be on top of other content */
}

.modal-content {
  position: relative;
  max-width: 90%; /* Adjust as needed */
  max-height: 90%; /* Adjust as needed */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); /* Optional: for better visibility */
}

.modal-close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  border: none;
  background: none;
  cursor: pointer;
  z-index: 1001; /* Above the image to ensure it's clickable */
}

.modal-close-btn svg {
  width: 24px;
  height: 24px;
}

.add-node {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 100;
}

.add-node-button {
  background-color: #4CAF50;
  border: none;
  color: white;
  padding: 10px 20px; /* Slightly larger for better touch area */
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.add-node-button:hover {
  background-color: #45a049; /* Slightly darker on hover for feedback */
}
.node-options {
  background-color: #f1f1f1;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-top: 20px; /* Increases spacing for clarity */
}

.node-options button {
  display: block;
  width: 100%; /* Makes buttons full width */
  margin-bottom: 10px; /* Increases spacing */
  background-color: #4CAF50;
  border: none;
  color: white;
  padding: 10px; /* Larger padding for a better touch area */
  text-align: center;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.node-options button:hover {
  background-color: #45a049; /* Slightly darker on hover */
}
.react-flow__node-custom .react-flow__handle {
  top: 5px;
  right: 0px;
  width: 6px;
  height: 10px;
  border-radius: 2px;
  background-color: #042924;
}
.custom-node {
  padding: 10px;
  position: relative;
  background: aqua;
  width: 260px;
  height: 520px; /* 改为最小高度 */
}
.custom-node-start {
  padding: 10px;
  position: relative;
  background: aqua;
  width: 260px;
  height: 400px; /* 改为最小高度 */
}

.custom-node__body {
  position: absolute; /* 改为相对定位 */
  top: 17%;
  padding: 10px;
  background: #e8f0f3;
  width: 240px;
  height: 400px;
}

.custom-node__header {
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0px 0px;
  border-bottom: 2px solid #e2e8f0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  font-size: 10;
  font-weight: bold;
}


.custom-node__select {
  position: relative;
  margin-bottom: 10px; /* 增加底部边距 */
  width: 260px;
}
/* 左边第一个 */
.handle-with-label-input-top-left-1 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 20px;
  left: 0;
  margin-top: 1px;
  height: 20px;
  width: 8px;
}
.handle-label-input-top-left-1 {
  position: absolute;
  top: 4px;
  left: 6px;
  margin-right: 8px;
  font-size: 8px;
  width: 40px;
}
.handle-with-label-input-top-left-2 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 40px;
  left: 0;
  margin-top: 1px;
  height: 20px;
  width: 8px;
}
.handle-label-input-top-left-2 {
  position: absolute;
  top: 4px;
  left: 6px;
  margin-right: 8px;
  font-size: 8px;
  width: 40px;
}

.handle-with-label-input-top-left-3 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 60px;
  left: 0;
  margin-top: 1px;
  height: 20px;
  width: 8px;
}
.handle-label-input-top-left-3 {
  position: absolute;
  top: 4px;
  left: 6px;
  margin-right: 8px;
  font-size: 8px;
  width: 40px;
}


/* output */

.handle-with-label-output-top-right-1 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 18px;
  right: 0;
  margin-bottom: 1px;
  height: 20px;
  width: 8px;
  /* padding: 5px; */
}
.handle-label-output-top-right-1 {
  position: absolute;
  bottom: 4px;
  right: 8px;
  margin-left: 8px;
  font-size: 8px;
  width: 40px;
}
.handle-with-label-output-top-right-2 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 38px;
  right: 0;
  margin-bottom: 1px;
  height: 20px;
  width: 8px;
  /* padding: 5px; */
}
.handle-label-output-top-right-2 {
  position: absolute;
  bottom: 4px;
  right: 8px;
  margin-left: 8px;
  font-size: 8px;
  width: 40px;
}
.handle-with-label-output-top-right-3 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 58px;
  right: 0;
  margin-bottom: 1px;
  height: 20px;
  width: 8px;
  /* padding: 5px; */
}
.handle-label-output-top-right-3 {
  position: absolute;
  bottom: 4px;
  right: 8px;
  margin-left: 8px;
  font-size: 8px;
  width: 40px;
}

.database-select-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.database-label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}

.database-select {
  width: 100%;
}

.custom-node__select select,
.database-select select {
  width: 100%;
  margin-top: 5px;
  font-size: 10px;
  max-height: 100px; /* 限制最大高度 */
  overflow-y: auto; /* 允许垂直滚动 */
}


.llm-select-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.llm-label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}

.llm-select {
  width: 100%;
}

.llm-select select {
  width: 100%;
  margin-top: 5px;
  font-size: 10px;
  max-height: 100px; /* 限制最大高度 */
  overflow-y: auto; /* 允许垂直滚动 */
}

.reflection-prompt-select-container,
.positive-prompt-select-container,
.tool-select-container,
.negative-prompt-select-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.reflection-prompt-label,
.positive-prompt-label,
.negative-prompt-label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}

.reflection-prompt-select,
.positive-prompt-select,
.negative-prompt-select {
  width: 100%;
}

.reflection-prompt-select select,
.positive-prompt-select select,
.negative-prompt-select select {
  width: 100%;
  margin-top: 5px;
  font-size: 10px;
  max-height: 100px; /* Limit maximum height */
  overflow-y: auto; /* Allow vertical scrolling */
}

.result-input-container {
  margin-top: 10px;
}

.result-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.result-textarea {
  width: 95%;
  min-height: 160px;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
}
.input-block {
  margin-top: 200px;
  width: 95%;
  /* background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px;
  width: 90%; */
}

.custom-node__body_start {
  position: absolute;
  top: 30%; /* 调整top值，使其不与.input-block重叠 */
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background: #e8f0f3;
  width: 160px;
  height: 50px;
}


.input-block__header {
  font-weight: bold;
  margin-bottom: 5px;
}

.input-block__textarea {
  width: 100%;
  min-height: 100px;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
}

.tool-description-content {
  font-size: 12px;
}
