/* Core node styling */
.customNode {
    background: #e1f5fe;
    border: 1px solid #b3e5fc;
    border-radius: 8px;
    padding: 10px 15px;
    width: 300px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

.customNodeHeader {
    font-weight: bold;
    font-size: 16px;
    color: #0277bd;
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 5px;
}

.customNodeBody {
    background: white;
    border-radius: 6px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Handle 相关样式 */
.handleContainer {
    position: relative;
    margin: 10px 0;
}

.inputHandle {
    position: absolute;
    left: 0;
    height: 24px;
    display: flex;
    align-items: center;
}

.outputHandle {
    position: absolute;
    right: 0;
    text-align: right;
    margin-bottom: 10px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.handleLabel {
    font-size: 12px;
    color: #333;
    margin-left: 20px;
}

.outputHandleLabel {
    font-size: 12px;
    color: #333;
    margin-right: 20px;
}

/* 选择器样式 */
.llmSelectContainer,
.actionNameSelectContainer,
.positivePromptSelectContainer,
.reflectionPromptSelectContainer,
.negativePromptSelectContainer {
    margin-bottom: 18px;
}

.llmLabel,
.actionNameLabel,
.positivePromptLabel,
.reflectionPromptLabel,
.negativePromptLabel {
    display: block;
    font-size: 13px;
    color: #333;
    margin-bottom: 8px;
}

.positivePromptLabel,
.reflectionPromptLabel {
    cursor: pointer;
}

.smallRedText {
    font-size: 10px;
    color: red;
    margin-left: 4px;
}

/* 动态参数样式 */
.parameterHandle {
    position: absolute;
    left: 0;
    height: 24px;
    display: flex;
    align-items: center;
}

.parameterOutputHandle {
    position: absolute;
    right: 0;
    text-align: right;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

/* Handle styling for input/output connections */
.handle-with-label-input-top-left-1,
.handle-with-label-input-top-left-2,
.handle-with-label-input-top-left-3 {
    position: absolute;
    display: flex;
    align-items: center;
    left: 0;
    height: 20px;
}

.handle-with-label-input-top-left-1 {
    top: 20px;
}

.handle-with-label-input-top-left-2 {
    top: 40px;
}

.handle-with-label-input-top-left-3 {
    top: 60px;
}

.handle-label-input-top-left-1,
.handle-label-input-top-left-2,
.handle-label-input-top-left-3 {
    position: absolute;
    left: 12px;
    font-size: 8px;
    color: #00796b;
    background: white;
    padding: 0 4px;
    border-radius: 3px;
    border: 1px solid #b2ebf2;
}

.handle-with-label-output-top-right-1 {
    position: absolute;
    display: flex;
    align-items: center;
    top: 20px;
    right: 0;
    height: 20px;
}

.handle-label-output-top-right-1 {
    position: absolute;
    right: 12px;
    font-size: 8px;
    color: #00796b;
    background: white;
    padding: 0 4px;
    border-radius: 3px;
    border: 1px solid #b2ebf2;
}

/* Select styling for dropdowns */
.llm-select-container,
.positive-prompt-select-container,
.reflection-prompt-select-container,
.negative-prompt-select-container {
    margin-bottom: 10px;
}

.llm-label,
.positive-prompt-label,
.reflection-prompt-label,
.negative-prompt-label {
    display: block;
    font-size: 10px;
    margin-bottom: 4px;
    color: #00796b;
    font-weight: 500;
}

/* Parameters section styling */
.parameters-section {
    margin-top: 10px;
    border-top: 1px dashed #b2ebf2;
    padding-top: 8px;
}

.parameters-group {
    margin-bottom: 12px;
}

.parameters-group-title {
    font-size: 10px;
    color: #00796b;
    margin: 5px 0;
    padding-bottom: 3px;
    border-bottom: 1px solid #e0f7fa;
}

.parameters-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.parameter-item {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    margin: 2px 0;
    background: #f5f5f5;
    border-radius: 3px;
    font-size: 9px;
    cursor: help;
    transition: background 0.2s;
}

.parameter-item:hover {
    background: #e0f7fa;
}

.parameter-name {
    flex: 1;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.parameter-type {
    width: 40px;
    color: #00796b;
    font-family: monospace;
    text-align: right;
    margin-left: 8px;
}

.parameter-required {
    width: 30px;
    text-align: center;
    margin-left: 8px;
    font-size: 8px;
    border-radius: 3px;
    padding: 1px 3px;
}

.parameter-required.required {
    background: #ffcdd2;
    color: #c62828;
}

.parameter-required.optional {
    background: #c8e6c9;
    color: #2e7d32;
}

/* Tooltip styling */
.parameter-tooltip {
    position: fixed;
    z-index: 1000;
    background-color: rgba(0, 105, 92, 0.9);
    color: white;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 10px;
    max-width: 200px;
    word-wrap: break-word;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, 5px); }
    to { opacity: 1; transform: translate(-50%, 0); }
}

/* Parameters View Button */
.parameters-view-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-left: 4px;
    color: #00796b;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 3px;
    transition: all 0.2s;
}

.parameters-view-button:hover {
    color: #004d40;
    background: rgba(0, 121, 107, 0.1);
}

.handle-with-label-input-top-left-1 .parameters-view-button,
.handle-with-label-input-top-left-2 .parameters-view-button,
.handle-with-label-input-top-left-3 .parameters-view-button {
    position: absolute;
    left: 60px;
}

.handle-with-label-output-top-right-1 .parameters-view-button {
    position: absolute;
    right: 60px;
}

/* Modal Styling */
.parameters-modal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 500px;
    width: 80%;
    max-height: 80vh;
    overflow-y: auto;
    outline: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #b2ebf2;
}

.parameters-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.parameters-modal h2 {
    color: #00796b;
    margin-top: 0;
    border-bottom: 1px solid #b2ebf2;
    padding-bottom: 10px;
}

.modal-close-button {
    background: #00796b;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 15px;
    transition: background 0.2s;
}

.modal-close-button:hover {
    background: #004d40;
}

.nodeDescription {
    font-size: 12px;
    padding: 8px 12px;
    color: #666;
    line-height: 1.4;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin: 8px 12px;
  }