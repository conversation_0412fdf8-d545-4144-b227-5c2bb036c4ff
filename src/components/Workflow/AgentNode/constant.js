/* 
  下面是一些内联样式对象
*/
export const toolNodeStyle = {
    background: 'white',
    border: '1px solid #ddd',
    borderRadius: '8px',
    minWidth: '200px',
    position: 'relative',
    paddingBottom: '20px',
  };
  
export const nodeHeaderStyle = {
    background: '#f5f5f5',
    padding: '8px 12px',
    borderBottom: '1px solid #ddd',
    borderRadius: '8px 8px 0 0',
    fontWeight: 500,
  };
  
export const nodeBodyStyle = {
    padding: '12px',
  };
  
export const toolSelectionStyle = {
    marginBottom: '16px',
  };
  
export  const toolLabelStyle = {
    display: 'block',
    marginBottom: '8px',
    fontSize: '14px',
    color: '#666',
  };
  
export const loadingOrErrorStyle = {
    padding: '8px',
    textAlign: 'center',
    color: '#666',
  };
  
export const toolInfoStyle = {
    marginTop: '16px',
    paddingTop: '16px',
    borderTop: '1px solid #eee',
  };
  
export const debugInfoStyle = {
    marginTop: '12px',
    padding: '8px',
    background: '#f5f5f5',
    borderRadius: '4px',
    fontSize: '12px',
  };
  
export const handleContainerStyle = {
    position: 'absolute',
    display: 'flex',
    alignItems: 'center',
    width: '100%',
  };
  
export  const handleStyle = {
    width: 8,
    height: 8,
    background: '#555',
    borderRadius: '50%',
  };
  
export const parameterLabelStyle = {
    fontSize: 12,
    color: '#666',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  };
  