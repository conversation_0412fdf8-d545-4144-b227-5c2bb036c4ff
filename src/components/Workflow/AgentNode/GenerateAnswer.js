import React, { memo, useCallback } from 'react';
import { <PERSON>le, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import './CustomNodeWithMultipleHandles.css';

function GenerateAnswer({ id, data, llm_options, name }) {
  const { setNodes } = useReactFlow();

  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);

  const selects = data && data.selects ? data.selects : {};

  return (
    <div className="custom-node">
      <div className="custom-node__header">{name}</div>
      <div className="handle-with-label-input-top-left-1">
        <Handle type="target" position={Position.Left} id="llm_input" />
        <span className="handle-label-input-top-left-1">模型输入</span>
      </div>
      <div className="handle-with-label-input-top-left-2">
        <Handle type="target" position={Position.Left} id="attachment_input" />
        <span className="handle-label-input-top-left-2">知识库输入</span>
      </div>
      <div className="custom-node__body">
        <div className="llm-select-container">
          <span className="llm-label">大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
          />
        </div>
      </div>
      <div className="handle-with-label-output-top-right-1">
        <Handle type="source" position={Position.Right} id="llm_output" style={{ background: '#555' }} />
        <span className="handle-label-output-top-right-1">模型输出</span>
      </div>
      <div className="handle-with-label-output-top-right-2">
        <Handle type="source" position={Position.Right} id="attachment_output" style={{ background: '#555' }} />
        <span className="handle-label-output-top-right-2">知识库输出</span>
      </div>
    </div>
  );
}

export default memo(GenerateAnswer);