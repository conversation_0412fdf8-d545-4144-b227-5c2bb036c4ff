import React from 'react';
import { useReactFlow, useStoreApi } from 'reactflow';
import { Select as AntSelect } from 'antd';

export function Select({ value, handleId, nodeId, options}) {
  const { setNodes } = useReactFlow();
  const store_api = useStoreApi();
  console.log(handleId,value,'valuevaluevalue')
  const onChange = (newValue) => {
    const { nodeInternals } = store_api.getState();
    setNodes(
      Array.from(nodeInternals.values()).map((node) => {
        if (node.id === nodeId) {
          node.data = {
            ...node.data,
            selects: {
              ...node.data.selects,
              [handleId]: newValue,
            },
          };
        }
        return node;
      })
    );
  };

  return (
    <div className="custom-node__select">
      <AntSelect
        className="nodrag"
        onChange={onChange}
        value={value}
        style={{ width: '75%' }}
        options={options}
      />
    </div>
  );
}