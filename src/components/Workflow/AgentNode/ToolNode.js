import React, { memo, useCallback, useState, useEffect } from 'react';
import { Handle, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import './CustomNodeWithMultipleHandles.css';
import { TOOL_ENDPOINT, TOOL_EDIT_NAME, MCP_SERVER_ENDPOINT, MCP_SERVER_NAME } from '../../Configs/Config';
import { fetchData } from '../../Routers/Router';
import { useNavigate } from 'react-router-dom';

function ToolNode({
  id,
  data,
  llm_options,
  name,
  tools = [],
  mcpServers = [],
  isDebugMode = false
}) {
  const { setNodes, getNodes } = useReactFlow();
  const navigate = useNavigate();

  // Store and display the current tool/MCP description
  const [itemDescription, setItemDescription] = useState('');
  
  // Track whether we're using a tool or MCP server
  const [serviceType, setServiceType] = useState('tool'); // Default to 'tool', could be 'mcp'

  // Get selects from node data (avoiding undefined)
  const selects = data && data.selects ? data.selects : {};

  // Handle service type change (tool vs MCP)
  const handleServiceTypeChange = useCallback(
    (event) => {
      const newValue = event.target.value;
      setServiceType(newValue);
      
      // 找到当前节点以获取当前状态
      const nodes = getNodes();
      const currentNode = nodes.find(node => node.id === id);
      const currentSelects = currentNode?.data?.selects || {};
      
      // console.log('Changing service type to:', newValue);
      // console.log('Before change:', {
      //   tool_name: currentSelects.tool_name,
      //   mcp_server_name: currentSelects.mcp_server_name
      // });
      
      // 更新状态，显式设置不相关的字段为null
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === id) {
            // 为工具选择时，MCP服务名设为null；为MCP服务选择时，工具名设为null
            const updatedSelects = {
              ...node.data.selects,
              service_type: newValue,
              tool_name: newValue === 'tool' ? (currentSelects.tool_name || '') : null,
              mcp_server_name: newValue === 'mcp' ? (currentSelects.mcp_server_name || '') : null
            };
            
            // console.log('After change:', {
            //   tool_name: updatedSelects.tool_name,
            //   mcp_server_name: updatedSelects.mcp_server_name
            // });
            
            return {
              ...node,
              data: {
                ...node.data,
                selects: updatedSelects
              }
            };
          }
          return node;
        })
      );
      
      // Clear description
      setItemDescription('');
    },
    [setNodes, getNodes, id]
  );

  /**
   * Handle dropdown change for any select
   */
  const handleChange = useCallback(
    (event, handleId, nodeId) => {
      const newValue = event.target.value;
      
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === nodeId) {
            const newData = { ...node.data };
            // 更新选择项
            newData.selects = {
              ...newData.selects,
              [handleId]: newValue,
            };
            
            // 如果更改的是tool_name或mcp_server_name，确保另一个值为null
            if (handleId === 'tool_name' && newData.selects.service_type === 'tool') {
              newData.selects.mcp_server_name = null;
              console.log('Setting mcp_server_name to null because tool was selected');
            } else if (handleId === 'mcp_server_name' && newData.selects.service_type === 'mcp') {
              newData.selects.tool_name = null;
              console.log('Setting tool_name to null because mcp was selected');
            }
            
            return { ...node, data: newData };
          }
          return node;
        })
      );
    },
    [setNodes]
  );

  /**
   * When tool_name or mcp_server_name changes, fetch description
   */
  useEffect(() => {
    // Initialize service type from stored data if available
    const storedServiceType = selects['service_type'] || 'tool';
    if (serviceType !== storedServiceType) {
      setServiceType(storedServiceType);
    }
    
    // 记录当前状态，帮助调试
    console.log('Current state:', {
      serviceType,
      tool_name: selects['tool_name'],
      mcp_server_name: selects['mcp_server_name']
    });
    
    // Determine which item to look up based on service type
    const itemName = serviceType === 'tool' ? selects['tool_name'] : selects['mcp_server_name'];

    // If no selection, clear description
    if (!itemName) {
      setItemDescription('');
      // Sync update to node.data
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === id) {
            return {
              ...node,
              data: {
                ...node.data,
                itemDescription: ''
              }
            };
          }
          return node;
        })
      );
      return;
    }

    // If tool or MCP is selected, fetch description
    (async () => {
      try {
        // Use appropriate endpoint based on service type
        const endpoint = serviceType === 'tool' 
          ? `${TOOL_ENDPOINT}/query_by_name/${itemName}`
          : `${MCP_SERVER_ENDPOINT}/query_by_name/${itemName}`; // Adjust this endpoint for MCPs
          
        const res = await fetchData(endpoint);
        const fetchedDescription = res?.description || '(无描述)';

        // Update local state
        setItemDescription(fetchedDescription);
        
        // Sync update to node.data
        setNodes((nodes) =>
          nodes.map((node) => {
            if (node.id === id) {
              return {
                ...node,
                data: {
                  ...node.data,
                  itemDescription: fetchedDescription,
                },
              };
            }
            return node;
          })
        );
      } catch (error) {
        console.error(`Failed to fetch ${serviceType} description:`, error);
      }
    })();
  }, [selects['tool_name'], selects['mcp_server_name'], serviceType, setNodes, id, selects]);

  // 组件初始化时，确保根据service_type设置另一个值为null
  useEffect(() => {
    const currentServiceType = selects['service_type'] || 'tool';
    
    // 确保不相关的字段为null
    if (currentServiceType === 'tool' && selects['mcp_server_name'] !== null) {
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === id) {
            return {
              ...node,
              data: {
                ...node.data,
                selects: {
                  ...node.data.selects,
                  mcp_server_name: null
                }
              }
            };
          }
          return node;
        })
      );
      console.log('Initialized: set mcp_server_name to null for tool type');
    } else if (currentServiceType === 'mcp' && selects['tool_name'] !== null) {
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === id) {
            return {
              ...node,
              data: {
                ...node.data,
                selects: {
                  ...node.data.selects,
                  tool_name: null
                }
              }
            };
          }
          return node;
        })
      );
      console.log('Initialized: set tool_name to null for mcp type');
    }
  }, [id, selects, setNodes]);

  // Handle click on label to edit tool/MCP
  const handleLabelClick = async () => {
    if (serviceType === 'tool') {
      const toolName = selects['tool_name'];
      if (toolName) {
        try {
          const res = await fetchData(`${TOOL_ENDPOINT}/query_by_name/${toolName}`);
          const toolId = res?.id;
          const workspaceId = res?.workspace_id;
          if (toolId) {
            const currentPath = window.location.pathname;
            navigate(`${TOOL_EDIT_NAME}/${workspaceId}/${toolId}`, {
              state: { from: currentPath },
            });
          } else {
            console.error('No tool ID found for the selected tool.');
          }
        } catch (error) {
          console.error('Failed to fetch the tool info:', error);
        }
      }
    } else {
      // Handle MCP server click - adjust according to your routing needs
      const mcpName = selects['mcp_server_name'];
      if (mcpName) {
        try {
          const res = await fetchData(`${MCP_SERVER_ENDPOINT}/query_by_name/${mcpName}`);
          const mcpId = res?.id;
          // const workspaceId = res?.workspace_id;
          if (mcpId) {
            const currentPath = window.location.pathname;
            navigate(`${MCP_SERVER_NAME}`, {
              state: { from: currentPath },
            });
          } else {
            console.error('No MCP ID found for the selected MCP server.');
          }
        } catch (error) {
          console.error('Failed to fetch the MCP info:', error);
        }
      }
    }
  };

  return (
    <div className="custom-node">
      {/* Node title */}
      <div className="custom-node__header">{name}</div>

      {/* Left input handle(1): User input */}
      <div className="handle-with-label-input-top-left-1">
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className="handle-label-input-top-left-1">用户输入</span>
      </div>

      {/* Left input handle(2): Model input */}
      <div className="handle-with-label-input-top-left-2">
        <Handle type="target" position={Position.Left} id="llm_input" />
        <span className="handle-label-input-top-left-2">模型输入</span>
      </div>

      {/* Main body: LLM, Tool/MCP, etc. */}
      <div className="custom-node__body">
        {/* LLM select */}
        <div className="llm-select-container">
          <span className="llm-label">大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
            className="llm-select"
          />
        </div>
        
        {/* Service type selector (Tool or MCP) */}
        <div className="service-type-select-container">
          <span className="service-type-label">服务类型</span>
          <Select
            key="service_type"
            nodeId={id}
            value={selects['service_type'] || serviceType}
            handleId="service_type"
            onChange={handleServiceTypeChange}
            options={[
              { value: 'tool', label: '工具' },
              { value: 'mcp', label: 'MCP服务' }
            ]}
            className="service-type-select"
          />
        </div>
        
        {/* Tool or MCP server selector - show based on service type */}
        {serviceType === 'tool' ? (
          <div className="positive-prompt-select-container">
            <span
              className="positive-prompt-label"
              onClick={handleLabelClick}
              style={{ cursor: 'pointer', textDecoration: 'underline' }}
            >
              工具
              <span style={{ fontSize: '8px', color: 'red' }}>（点击修改工具）</span>
            </span>
            <Select
              key="tool"
              nodeId={id}
              value={selects['tool_name']}
              handleId="tool_name"
              onChange={handleChange}
              options={tools}
              className="tool-select-container"
            />
          </div>
        ) : (
          <div className="positive-prompt-select-container">
            <span
              className="positive-prompt-label"
              onClick={handleLabelClick}
              style={{ cursor: 'pointer', textDecoration: 'underline' }}
            >
              MCP服务
              <span style={{ fontSize: '8px', color: 'red' }}>（点击修改MCP服务）</span>
            </span>
            <Select
              key="mcp_server"
              nodeId={id}
              value={selects['mcp_server_name']}
              handleId="mcp_server_name"
              onChange={handleChange}
              options={mcpServers}
              className="tool-select-container"
            />
          </div>
        )}

        {/* Item description display */}
        <div className="tool-description-container">
          <span className="tool-description-label">
            {serviceType === 'tool' ? '工具描述：' : 'MCP服务描述：'}
          </span>
          <p className="tool-description-content">{itemDescription}</p>
        </div>
        
        {/* Debug info - remove in production */}
        {isDebugMode && (
          <div style={{ fontSize: '10px', color: '#999', marginTop: '10px', border: '1px solid #ddd', padding: '4px' }}>
            <div>ServiceType: {serviceType}</div>
            <div>tool_name: {selects['tool_name'] === null ? 'null' : selects['tool_name'] || 'empty'}</div>
            <div>mcp_server_name: {selects['mcp_server_name'] === null ? 'null' : selects['mcp_server_name'] || 'empty'}</div>
          </div>
        )}
      </div>

      {/* Right output handle: Attachment output */}
      <div className="handle-with-label-output-top-right-1">
        <Handle
          type="source"
          position={Position.Right}
          id="attachment_output"
          style={{ background: '#555' }}
        />
        <span className="handle-label-output-top-right-1">附件输出</span>
      </div>
    </div>
  );
}

export default memo(ToolNode);