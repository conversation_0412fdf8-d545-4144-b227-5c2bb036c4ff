/* ToolNode.module.css */
.toolNode {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  min-width: 200px;
  min-height: 150px;
  position: relative;
  padding-bottom: 20px; /* Add padding to accommodate handles */
}

.nodeHeader {
  background: #f5f5f5;
  padding: 8px 12px;
  border-bottom: 1px solid #ddd;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
}

.nodeBody {
  padding: 12px;
  height: 200px;
}

.toolSelection {
  margin-bottom: 16px;
}

.toolLabel {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.toolSelect {
  width: 100%;
  height: 32px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 8px;
}

/* Base styles for handle containers */
.handleContainer {
  position: absolute;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 0px; /* Add padding for handle spacing */
}

/* Input handle container specific styles */
.handleContainer[data-type="input"] {
  justify-content: flex-start;
  padding-right: 10%; /* Leave space for right side */
}

/* Output handle container specific styles */
.handleContainer[data-type="output"] {
  justify-content: flex-end;
  padding-left: 0%; /* Leave space for left side */
}

.handle {
  width: 8px;
  height: 8px;
  background: #555;
  border-radius: 50%;
  flex-shrink: 0;
}

/* Parameter label styles */
.parameterLabel {
  font-size: 12px;
  color: #666;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Input parameter label (left-aligned) */
.handleContainer[data-type="input"] .parameterLabel {
  margin-left: 8px;
  text-align: left;
}

/* Output parameter label (right-aligned) */
.handleContainer[data-type="output"] .parameterLabel {
  margin-right: 8px;
  text-align: right;
}

.loadingState,
.errorState {
  padding: 8px;
  text-align: center;
  color: #666;
}

.errorState {
  color: #ff4d4f;
}

.toolInfo {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.debugInfo {
  margin-top: 12px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
}

.debugInfo pre {
  margin: 0;
  white-space: pre-wrap;
}