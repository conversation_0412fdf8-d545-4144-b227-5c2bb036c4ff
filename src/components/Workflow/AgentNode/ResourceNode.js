import React, { memo, useCallback, useState, useEffect, useMemo } from 'react';
import { Handle, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import './CustomNodeWithMultipleHandles.css';
import { MCP_SERVER_ENDPOINT, MCP_SERVER_NAME } from '../../Configs/Config';
import { fetchData } from '../../Routers/Router';
import { useNavigate } from 'react-router-dom';

function ResourceNode({
  id,
  data,
  name,
  mcpResources = [],
  isDebugMode = false
}) {
  const { setNodes } = useReactFlow();
  const navigate = useNavigate();
  const [itemDescription, setItemDescription] = useState('');
  const selects = data?.selects || {};

  /**
   * Handle MCP resource change
   */
  console.log(mcpResources,'mcpResources')
  const handleChange = useCallback(
    (event, handleId, nodeId) => {
      const newValue = event.target.value;
      
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === nodeId) {
            const newData = { ...node.data };
            newData.selects = {
              ...newData.selects,
              [handleId]: newValue,
            };
            
            return { ...node, data: newData };
          }
          return node;
        })
      );
    },
    [setNodes]
  );

  /**
   * When mcp_resource changes, fetch description
   */
  useEffect(() => {
    const mcpName = selects['mcp_resource_name'];
    console.log(mcpName,'mcpResourcesmcpResources')
    if (!mcpName) {
      setItemDescription('');
      setNodes((nodes) =>
        nodes.map((node) => {
          if (node.id === id) {
            return {
              ...node,
              data: {
                ...node.data,
                itemDescription: ''
              }
            };
          }
          return node;
        })
      );
      return;
    }

    const fetchDescription = async () => {
      try {
        const endpoint = `${MCP_SERVER_ENDPOINT}/query_by_name/${mcpName}`;
        const res = await fetchData(endpoint);
        const fetchedDescription = res?.description || '(无描述)';

        setItemDescription(fetchedDescription);
        
        setNodes((nodes) =>
          nodes.map((node) => {
            if (node.id === id) {
              return {
                ...node,
                data: {
                  ...node.data,
                  itemDescription: fetchedDescription,
                },
              };
            }
            return node;
          })
        );
      } catch (error) {
        console.error('Failed to fetch MCP description:', error);
      }
    };

    fetchDescription();
  }, [selects['mcp_resource_name'], setNodes, id]);

  // Handle click on label to edit MCP
  const handleLabelClick = async () => {
    const mcpName = selects['mcp_resource_name'];
    if (!mcpName) return;

    try {
      const res = await fetchData(`${MCP_SERVER_ENDPOINT}/query_by_name/${mcpName}`);
      const mcpId = res?.id;
      if (mcpId) {
        const currentPath = window.location.pathname;
        navigate(`${MCP_SERVER_NAME}`, {
          state: { from: currentPath },
        });
      }
    } catch (error) {
      console.error('Failed to fetch the MCP info:', error);
    }
  };

  // Transform resources for select options
  // const resourceOptions = useMemo(() => {
  //   return mcpResources.map(resource => ({
  //     value: resource.name,
  //     label: resource.name
  //   }));
  // }, [mcpResources]);

  return (
    <div className="custom-node">
      {/* Node title */}
      <div className="custom-node__header">{name}</div>

      {/* Main body */}
      <div className="custom-node__body">
        {/* MCP Resource selector */}
        <div className="positive-prompt-select-container">
          <span
            className="positive-prompt-label"
            onClick={handleLabelClick}
            style={{ cursor: 'pointer', textDecoration: 'underline' }}
          >
            MCP资源
            <span style={{ fontSize: '8px', color: 'red' }}>（点击修改）</span>
          </span>
          <Select
            nodeId={id}
            value={selects['mcp_resource_name'] || ''}
            handleId="mcp_resource_name"
            onChange={handleChange}
            options={mcpResources}
            className="tool-select-container"
          />
        </div>

        {/* Item description display */}
        <div className="tool-description-container">
          <span className="tool-description-label">MCP资源描述：</span>
          <p className="tool-description-content">{itemDescription}</p>
        </div>
        
        {/* Debug info */}
        {isDebugMode && (
          <div className="debug-info">
            <div>mcp_resource_name: {selects['mcp_resource_name'] || 'empty'}</div>
            <div>Available options: {mcpResources.length}</div>
          </div>
        )}
      </div>

      {/* Right output handle */}
      <div className="handle-with-label-output-top-right-1">
        <Handle
          type="source"
          position={Position.Right}
          id="attachment_output"
          style={{ background: '#555' }}
        />
        <span className="handle-label-output-top-right-1">附件输出</span>
      </div>
    </div>
  );
}

export default memo(ResourceNode);