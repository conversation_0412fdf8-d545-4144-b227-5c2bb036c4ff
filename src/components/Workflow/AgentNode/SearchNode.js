// SearchNode.js
import React, { memo, useCallback, useState } from 'react';
import { Handle, useReactFlow, Position } from 'reactflow';
import { Select } from './utils';
import styles from './SearchNode.module.css';
import { PROMPT_ENDPOINT,PROMPT_EDIT_NAME,MCP_RESOURCE_NAME,MCP_SERVER_ENDPOINT } from '../../Configs/Config';
import { fetchData } from '../../Routers/Router';
import { useNavigate } from 'react-router-dom';

function SearchNode({
  id, 
  data, 
  llm_options, 
  name, 
  database_options, 
  positive_prompt_options, 
  agent_type, 
  negative_prompt_options = null, 
  mcp_server_options = [],
  isDebugMode = false
}) {
  const { setNodes } = useReactFlow();
  const [result, setResult] = useState('');
  const navigate = useNavigate();

  const filteredPositiveOptions = positive_prompt_options;
  const filteredNegativeOptions = negative_prompt_options;
  const handleChange = useCallback((event, handleId, nodeId) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          const newData = { ...node.data };
          newData.selects = { ...newData.selects, [handleId]: event.target.value };
          return { ...node, data: newData };
        }
        return node;
      })
    );
  }, [setNodes]);

  const handleResultChange = (event) => {
    setResult(event.target.value);
  };

  const handleLabelClick = async () => {
    const positivePrompt = selects['positive_prompt'];
    if (positivePrompt) {
      try {
        const data = await fetchData(`${PROMPT_ENDPOINT}/query_by_name/${positivePrompt}`);
        const promptId = data?.id;
        if (promptId) {
          const currentPath = window.location.pathname;
          navigate(`${PROMPT_EDIT_NAME}/${promptId}`, { state: { from: `${currentPath}` } });
        } else {
          console.error('No prompt ID found for the selected positive prompt.');
        }
      } catch (error) {
        console.error('Failed to fetch the prompt ID:', error);
      }
    }
  };
  const selects = data && data.selects ? data.selects : {};

  return (
    <div className={styles.custom_node}>
      <div className={styles.custom_node_header}>{name}</div>
      <div className={styles.handle_with_label_input_top_left_1}>
        <Handle type="target" position={Position.Left} id="query_input" />
        <span className={styles.handle_label_input_top_left_1}>用户输入</span>
      </div>
      <div className={styles.handle_with_label_input_top_left_2}>
        <Handle type="target" position={Position.Left} id="llm_input" />
        <span className={styles.handle_label_input_top_left_2}>模型输入</span>
      </div>
      <div className={styles.custom_node_body}>
        <div className={styles.llm_select_container}>
          <span className={styles.llm_label}>大模型</span>
          <Select
            key="llm_name"
            nodeId={id}
            value={selects['llm_name']}
            handleId="llm_name"
            onChange={handleChange}
            options={llm_options}
            className={styles.llm_select}
          />
        </div>
        <div className={styles.database_select_container}>
          <span className={styles.database_label}>数据库</span>
          <Select 
            key="kb_name"
            nodeId={id}
            value={selects['kb_name']}
            handleId="kb_name"
            onChange={handleChange}
            options={database_options}
            className={styles.database_select}
          />
        </div>
        <div className={styles.mcp_server_select_container}>
          <span className={styles.mcp_server_label}>MCP服务</span>
          <Select 
            key="mcp_server_name"
            nodeId={id}
            value={selects['mcp_server_name']}
            handleId="mcp_server_name"
            onChange={handleChange}
            options={mcp_server_options}
            className={styles.mcp_server_select}
          />
        </div>
        <div className={styles.positive_prompt_select_container}>
          <span 
            className={styles.positive_prompt_label}
            onClick={handleLabelClick}
            style={{ cursor: 'pointer', textDecoration: 'underline' }}
          >
            正向提示词
            <span style={{ fontSize: '8px', color: 'red' }}>
              (点击修改提示词)
            </span>
          </span>
          <Select 
            key="positive_prompt"
            nodeId={id}
            value={selects['positive_prompt']}
            handleId="positive_prompt"
            onChange={handleChange}
            options={filteredPositiveOptions}
            className={styles.positive_prompt_select}
          />
        </div>
        {filteredNegativeOptions && filteredNegativeOptions.length > 0 && (
          <div className={styles.negative_prompt_select_container}>
            <span className={styles.negative_prompt_label}>负向提示词</span>
            <Select
              key="negative_prompt"
              nodeId={id}
              value={selects['negative_prompt']}
              handleId="negative_prompt"
              onChange={handleChange}
              options={filteredNegativeOptions}
              className={styles.negative_prompt_select}
            />
          </div>
        )}
      </div>
      <div className={styles.handle_with_label_output_top_right_1}>
        <Handle type="source" position={Position.Right} id="attachment_output" style={{ background: '#555' }} />
        <span className={styles.handle_label_output_top_right_1}>附件输出</span>
      </div>
    </div>
  );
}

export default memo(SearchNode);