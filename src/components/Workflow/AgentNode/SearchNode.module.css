.custom_node {
  padding: 10px;
  position: relative;
  background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(178, 235, 242, 0.3)' fill-rule='evenodd'/%3E%3C/svg%3E"),
    linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
  background-size: 80px 80px, 100% 100%;
  background-position: center center;
  width: 260px;
  height: 520px;
  border: 1px solid #80deea;
  box-shadow: 0 2px 8px rgba(0, 172, 193, 0.1);
  transition: all 0.3s ease;
}

.custom_node:hover {
  box-shadow: 0 4px 12px rgba(0, 172, 193, 0.2);
  transform: translateY(-1px);
}

.custom_node::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2380deea' fill-opacity='0.2' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3Ccircle cx='13' cy='13' r='1'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 20px 20px;
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
}

.custom_node_body {
  position: absolute;
  top: 17%;
  padding: 10px;
  background: rgba(255, 255, 255, 0.9);
  width: 240px;
  height: 400px;
  border-radius: 4px;
  backdrop-filter: blur(4px);
  z-index: 1;
}

.custom_node_header {
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 0;
  border-bottom: 2px solid #00acc1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  font-size: 12px;
  font-weight: bold;
  color: #006064;
  background: rgba(178, 235, 242, 0.6);
  z-index: 1;
}

.handle_with_label_input_top_left_1 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 20px;
  left: 0;
  margin-top: 1px;
  height: 20px;
  width: 8px;
}

.handle_label_input_top_left_1 {
  position: absolute;
  top: 4px;
  left: 6px;
  margin-right: 8px;
  font-size: 8px;
  width: 40px;
}

.handle_with_label_input_top_left_2 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 40px;
  left: 0;
  margin-top: 1px;
  height: 20px;
  width: 8px;
}

.handle_label_input_top_left_2 {
  position: absolute;
  top: 4px;
  left: 6px;
  margin-right: 8px;
  font-size: 8px;
  width: 40px;
}

.handle_with_label_output_top_right_1 {
  position: absolute;
  display: flex;
  align-items: center;
  top: 18px;
  right: 0;
  margin-bottom: 1px;
  height: 20px;
  width: 8px;
}

.handle_label_output_top_right_1 {
  position: absolute;
  bottom: 4px;
  right: 8px;
  margin-left: 8px;
  font-size: 8px;
  width: 40px;
}

.database_select_container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.database_label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}

.database_select {
  width: 100%;
}

.database_select select {
  width: 100%;
  margin-top: 5px;
  font-size: 10px;
  max-height: 100px;
  overflow-y: auto;
}

.llm_select_container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.llm_label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}

.llm_select {
  width: 100%;
}

.llm_select select {
  width: 100%;
  margin-top: 5px;
  font-size: 10px;
  max-height: 100px;
  overflow-y: auto;
}

.mcp_server_select_container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.mcp_server_label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}

.mcp_server_select {
  width: 100%;
}

.mcp_server_select select {
  width: 100%;
  margin-top: 5px;
  font-size: 10px;
  max-height: 100px;
  overflow-y: auto;
}

.positive_prompt_select_container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 10px;
}

.positive_prompt_label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
}

.positive_prompt_select {
  width: 100%;
}

.positive_prompt_select select {
  width: 100%;
  margin-top: 5px;
  font-size: 10px;
  max-height: 100px;
  overflow-y: auto;
} 