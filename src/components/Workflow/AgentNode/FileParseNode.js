import React, { memo, useState, useEffect } from 'react';
import { Hand<PERSON>, Position } from 'reactflow';
import { Tag, Divider } from 'antd';
import styles from './FileParseNode.module.css';
import { Select } from './utils';

const PARSE_TYPES = [
  { value: 'image', label: '图片解析' },
  { value: 'audio', label: '语音解析' },
  { value: 'video', label: '视频解析' }
];

const PARSE_METHODS = {
  image: [
    { value: '', label: '不使用解析' },
    {
      value: 'rapidOcr解析',
      label: 'rapidOcr解析',
      paramKey: 'image_url',
      description: '解析图片中的文字内容',
      method: 'post'
    }
  ],
  audio: [
    { value: '', label: '不使用解析' },
    {
      value: 'sensevoicesmall',
      label: 'sensevoicesmall',
      paramKey: 'audio_url',
      description: '将音频内容转换为文字',
      method: 'post'
    }
  ],
  video: [
    { value: '', label: '不使用解析' },
    // {
    //   value: 'videoFrame',
    //   label: '视频帧提取',
    //   paramKey: 'video_url',
    //   description: '从视频中提取关键帧',
    //   method: 'post'
    // }
  ]
};

const TYPE_COLORS = {
  image: 'blue',
  audio: 'green',
  video: 'purple'
};

function FileParseNode({ id, data }) {
  const [currentTab, setCurrentTab] = useState('image');
  const selects = data?.selects || {};
  
  const methodKeys = {
    image: 'image_method_name',
    audio: 'audio_method_name',
    video: 'video_method_name'
  };
  
  const selectedMethods = {
    image: selects[methodKeys.image] || '',
    audio: selects[methodKeys.audio] || '',
    video: selects[methodKeys.video] || ''
  };
  
  const selectedMethodObjects = {
    image: PARSE_METHODS.image.find(m => m.value === selectedMethods.image),
    audio: PARSE_METHODS.audio.find(m => m.value === selectedMethods.audio),
    video: PARSE_METHODS.video.find(m => m.value === selectedMethods.video)
  };
  
  const currentTypeMethodObj = PARSE_METHODS[currentTab].find(m => m.value === selectedMethods[currentTab]);
  
  const handleTabChange = (value) => {
    setCurrentTab(value);
  };
  
  useEffect(() => {
    for (const type of ['image', 'audio', 'video']) {
      if (selectedMethods[type]) {
        setCurrentTab(type);
        break;
      }
    }
  }, []);

  return (
    <div className={styles.customNode}>
      <div className={styles.customNodeHeader}>文件解析</div>

      {/* Input Handle */}
      <div className={styles.inputHandle} style={{ top: '50px' }}>
        <Handle type="target" position={Position.Left} id="attachment_input" />
        <span className={styles.handleLabel}>附件输入</span>
      </div>

      {/* Output Handle */}
      <div className={styles.outputHandle} style={{ top: '50px' }}>
        <span className={styles.outputHandleLabel}>附件输出</span>
        <Handle type="source" position={Position.Right} id="attachment_output" />
      </div>

      {/* Form content area */}
      <div className={styles.customNodeBody} style={{ marginTop: '80px' }}>
        {/* 已选的解析方法概览 */}
        <div className={styles.selectedMethodsOverview}>
          <div className={styles.overviewTitle}>已选择的解析方法:</div>
          <div className={styles.tagsContainer}>
            {Object.keys(selectedMethodObjects).map(type => (
              selectedMethodObjects[type] && selectedMethodObjects[type].value && (
                <Tag 
                  key={type} 
                  color={TYPE_COLORS[type]}
                  className={styles.methodTag}
                >
                  {PARSE_TYPES.find(t => t.value === type)?.label}: {selectedMethodObjects[type].label}
                </Tag>
              )
            ))}
            {!Object.values(selectedMethods).some(v => v) && (
              <div className={styles.noMethodsSelected}>未选择任何解析方法</div>
            )}
          </div>
        </div>
        
        <Divider style={{ margin: '12px 0' }} />
        
        {/* Parse Type Selection - 只用于界面显示切换，不保存到数据中 */}
        <div className={styles.selectContainer}>
          <span className={styles.selectLabel}>解析类型</span>
          <div className={styles.tabSelector}>
            {PARSE_TYPES.map(type => (
              <Tag 
                key={type.value}
                color={currentTab === type.value ? TYPE_COLORS[type.value] : 'default'}
                onClick={() => handleTabChange(type.value)}
                className={styles.typeTag}
                style={{ cursor: 'pointer' }}
              >
                {type.label}
              </Tag>
            ))}
          </div>
        </div>

        {/* 当前选择的解析类型的方法 */}
        <div className={styles.selectContainer}>
          <span className={styles.selectLabel}>
            <Tag color={TYPE_COLORS[currentTab]} style={{ margin: 0 }}>
              {PARSE_TYPES.find(t => t.value === currentTab)?.label}
            </Tag>
            解析方法
          </span>
          <Select
            nodeId={id}
            handleId={methodKeys[currentTab]}
            value={selectedMethods[currentTab]}
            options={PARSE_METHODS[currentTab]}
          />
        </div>

        {/* 当前选中方法的详情 */}
        {currentTypeMethodObj && currentTypeMethodObj.value && (
          <div className={styles.methodDetails}>
            <div className={styles.methodProperty}>
              <span className={styles.propertyLabel}>参数Key:</span>
              <span className={styles.propertyValue}>{currentTypeMethodObj.paramKey}</span>
            </div>
            <div className={styles.methodDescription}>
              {currentTypeMethodObj.description}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default memo(FileParseNode);