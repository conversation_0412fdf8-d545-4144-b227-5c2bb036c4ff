import React, { useState, useEffect } from 'react';
import styles from './ParameterDisplay.module.css';
import { fetchData } from '../../Routers/Router';
import { PROMPT_ENDPOINT } from '../../Configs/Config';

// Simple icon component for the parameter button
const ParameterIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M4 13h5"></path>
    <path d="M4 17h10"></path>
    <path d="M4 9h13"></path>
    <path d="M15 5L12 8L15 11"></path>
    <path d="M17 15L20 18L17 21"></path>
  </svg>
);

// Modal component for displaying parameters
const ParameterModal = ({ parameters, onClose }) => {
  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    window.addEventListener('keydown', handleEsc);
    
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose]);

  if (!parameters || Object.keys(parameters).length === 0) {
    return (
      <div className={styles.modal} onClick={onClose}>
        <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
          <button className={styles.closeButton} onClick={onClose}>&times;</button>
          <h3>参数列表</h3>
          <p>无可用参数</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.modal} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.closeButton} onClick={onClose}>&times;</button>
        <h3>参数列表</h3>
        <div className={styles.parameterList}>
          {Object.entries(parameters).map(([key, param], index) => (
            <div key={index} className={styles.parameterItem}>
              <div className={styles.parameterHeader}>
                <span className={styles.parameterName}>{param.name}</span>
                <span className={styles.parameterType}>{param.type}</span>
                {param.required && <span className={styles.parameterRequired}>必填</span>}
              </div>
              <div className={styles.parameterDesc}>{param.description}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// ParameterDisplay component
const ParameterDisplay = ({ promptName }) => {
  const [showModal, setShowModal] = useState(false);
  const [parameters, setParameters] = useState({});
  const [hasParameters, setHasParameters] = useState(false);

  useEffect(() => {
    const fetchPromptParameters = async () => {
      if (promptName) {
        try {
          const data = await fetchData(`${PROMPT_ENDPOINT}/query_by_name/${promptName}`);
          if (data?.parameters && Object.keys(data.parameters).length > 0) {
            setParameters(data.parameters);
            setHasParameters(true);
          } else {
            setParameters({});
            setHasParameters(false);
          }
        } catch (error) {
          console.error('Failed to fetch prompt parameters:', error);
          setParameters({});
          setHasParameters(false);
        }
      } else {
        setParameters({});
        setHasParameters(false);
      }
    };

    fetchPromptParameters();
  }, [promptName]);

  const toggleModal = () => {
    setShowModal(!showModal);
  };

  // Don't render anything if there are no parameters
  if (!hasParameters) {
    return null;
  }

  return (
    <span className={styles.parameterDisplay}>
      <button 
        className={styles.parameterButton} 
        onClick={toggleModal}
        title="查看参数"
      >
        <ParameterIcon />
      </button>
      {showModal && (
        <ParameterModal parameters={parameters} onClose={() => setShowModal(false)} />
      )}
    </span>
  );
};

export default ParameterDisplay;