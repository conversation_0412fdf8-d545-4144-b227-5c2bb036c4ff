// import React,{useEffect, useState} from 'react';
// import { useNavigate } from 'react-router-dom';
// import {
//   Table, TableBody, TableCell, TableContainer,
//   TableHead, TableRow, Paper, Button
// } from '@mui/material';
// import {  deleteWorkflow } from '../Dashboard/Chat/utils/apiUtil';
// import { WORKFLOW_API_NAME,DASHBOARD_API_NAME,NEW_WORKFLOW,WORKFLOWS_USER_ENDPOINT } from '../Configs/Config'
// import { useDispatch,useSelector } from 'react-redux';
// import { setWorkflows,setSelectedWorkflowId,updateWorkflow } from './workflowSlice';
// import "./WorkflowNamePrompt.css";
// import { ToastContainer, toast } from 'react-toastify';
// import 'react-toastify/dist/ReactToastify.css';
// import { fetchData } from '../Routers/Router';

// function processWorkflows(workflows) {
//   return workflows.map(workflow => {
//     return {
//       ...workflow,
//       nodes: JSON.parse(workflow.nodes),
//       edges: JSON.parse(workflow.edges),
//       workflow_name: workflow.name,
//       database: workflow.database.split(",")
//     };
//   });
// }

// function WorkflowTable() {
//   const navigate = useNavigate();
//   const dispatch = useDispatch();
//   const { workflows } = useSelector((state) => state.workflow);
//   const [selectedRowId, setSelectedRowId] = useState(null); // State to keep track of the selected row
//   const user_info = useSelector((state) => state.user.user_info);
//   const username = user_info.username;
//   const [toastId, setToastId] = useState(null);
//   useEffect(() => {
//     const handleEscFunction = (event) => {
//         if (event.keyCode === 27 && toastId !== null) { // Check if toastId is not null when ESC is pressed
//             toast.dismiss(toastId);
//         }
//     };

//     document.addEventListener("keydown", handleEscFunction, false);

//     return () => {
//         document.removeEventListener("keydown", handleEscFunction, false);
//     };
// }, [toastId]); // Dependency on toastId to update listener when it changes


//   useEffect(()=>{
//     const fetchWorkflows = async () => {
//       try {
//         const skip=0;
//         const limit = 100;
//         const endpoint_api = `${WORKFLOWS_USER_ENDPOINT}${username}?skip=${skip}&limit=${limit}`;
//         const temp_workflows = await fetchData(endpoint_api);
//         const workflows = processWorkflows(temp_workflows.workflows);
//         localStorage.setItem('workflows', JSON.stringify(workflows));
//         dispatch(setWorkflows(workflows));
//       } catch (error) {
//         console.error("Failed to fetch workflows:", error);
//       }
//     };
//     fetchWorkflows();
//   },[dispatch, username])
  
//   useEffect(() => {
//     dispatch(setWorkflows(workflows));
//   }, [dispatch, workflows]); // 确保依赖项正确，以避免不必要的重复渲染
  
//   const handleAddClick = () => {
//     const selected_workflow_id =`${NEW_WORKFLOW}`;
//     const workflow = {
//         id:selected_workflow_id,
//         name:null,
//         nodes:[],
//         edges:[]
//     };
//     dispatch(updateWorkflow(
//         {
//         id:workflow.id,
//         workflow_name:workflow.workflow_name,
//         nodes:workflow.nodes,
//         edges:workflow.edges,
//         selected_workflow_id:selected_workflow_id
//     }));
//     dispatch(setSelectedWorkflowId(selected_workflow_id));
//     navigate(`${WORKFLOW_API_NAME}/${workflow.id}`, { state: { ...workflow } });
//   };
  
//   const handleEditClick = (workflow) => {
//     setSelectedRowId(workflow.id);
//     navigate(`${WORKFLOW_API_NAME}/${workflow.id}`, { state: { ...workflow } });
//     dispatch(setSelectedWorkflowId(workflow.id));
//   };
  
//   const handleStartSessionClick = (workflow_id) => {
//     dispatch(setSelectedWorkflowId(workflow_id));
//     navigate(`${DASHBOARD_API_NAME}`, { state: { workflow_id } });
//   };

//   const handleRowClick = (id) => {
//     setSelectedRowId(id);
//   };

//   const confirmDelete = (workflowId,workflow_name) => {
//     const id = toast.warn(
//       <div>
//       确定删除工作流 <strong>{workflow_name}</strong>
//       <Button onClick={() => {
//         handleDeleteWorkflow(workflowId);
//         toast.dismiss(id); // Close the toast when the user confirms
//       }} color="secondary">
//         是
//       </Button>
//       <Button onClick={() => toast.dismiss(id)} color="primary">
//         否
//       </Button>
//     </div>,
//       {
//         position: "center",
//         autoClose: 0.1,
//         closeOnClick: false,
//         closeButton: false, // 
//         className: 'center-toast',
//       }
//     );
//     setToastId(id);
//   };
//   // Actual deletion function that gets called when the user confirms deletion
//   const handleDeleteWorkflow = async (workflowId) => {
//     toast.dismiss(workflowId); // Dismiss the confirmation toast
//     try {
//       await deleteWorkflow(workflowId);
//       dispatch(setWorkflows(workflows.filter(workflow => workflow.id !== workflowId))); // Update local state
//       toast.success("Workflow deleted successfully.",{ autoClose: 200 });
//     } catch (error) {
//       toast.error(`Deletion failed: ${error.message}`);
//     }
//   };
//   return (
//     <>
//     <ToastContainer />
//       <Button variant="contained" onClick={handleAddClick} style={{ margin: '20px' }}>
//         新增工作流
//       </Button>
//       <TableContainer component={Paper} className="table-container">
//         <Table aria-label="simple table">
//           <TableHead>
//             <TableRow>
//               <TableCell>序号</TableCell>
//               <TableCell>工作流名称</TableCell>
//               <TableCell>工作流类型</TableCell>
//               <TableCell>数据库</TableCell>
//               <TableCell>领域</TableCell>
//               <TableCell>操作</TableCell>
//               <TableCell>工作流描述</TableCell>
//               <TableCell>修改时间</TableCell>
//             </TableRow>
//           </TableHead>
//           <TableBody>
//             {workflows.map((workflow, index) => (
//               <TableRow 
//                 key={workflow.id}
//                 onClick={() => handleRowClick(workflow.id)}
//                 style={{
//                   backgroundColor: workflow.id === selectedRowId ? '#e0e0e0' : 'inherit',
//                 }}
//               >
//                 <TableCell>{index + 1}</TableCell>
//                 <TableCell>{workflow.workflow_name}</TableCell>
//                 <TableCell>{workflow.workflow_type}</TableCell>
//                 <TableCell>{workflow.database}</TableCell>
//                 <TableCell>{workflow.domain}</TableCell>
//                 <TableCell>
//                   <Button onClick={() => handleEditClick(workflow)} variant="contained" color="primary">
//                     修改工作流
//                   </Button>
//                   <Button onClick={() => handleStartSessionClick(workflow.id)} variant="contained" color="secondary">
//                     开始会话
//                   </Button>
//                   <Button onClick={() => confirmDelete(workflow.id,workflow.workflow_name)} variant="contained" color="error">
//                     删除
//                   </Button>
//                 </TableCell>
//                 <TableCell>{workflow.description}</TableCell>
//                 <TableCell>{workflow.update_time}</TableCell>

//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </TableContainer>
//     </>
//   );
// }

// export default WorkflowTable;