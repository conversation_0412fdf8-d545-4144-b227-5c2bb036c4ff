.modal {
    display: none; /* 隐藏弹窗 */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0); /* 黑色背景 */
    background-color: rgba(0,0,0,0.4); /* 背景半透明 */
  }
  
  .modal.show {
    display: block; /* 显示弹窗 */
  }
  
  .modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 30%;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2), 0 6px 20px 0 rgba(0,0,0,0.19);
  }
  
  .close-icon {
    cursor: pointer;
    float: right;
    font-size: 28px;
  }
  .button-edit {
    color: white;
    background-color: #2196F3;
    margin-right: 8px;
  }
  
  .button-start-session {
    color: white;
    background-color: #F44336;
  }
  
  .add-button {
    margin: 20px;
    background-color: #112512;
  }
  
  .table-container {
    margin-top: 20px;
    overflow-x: auto;
  }
  
  .table {
    min-width: 650px;
  }
  
  .action-buttons {
    margin-right: 10px;
  }
  /* TableContainer.css */
.table-container {
  max-width: 90%; /* Set the maximum width as desired */
  margin: 0 auto; /* Center the TableContainer horizontally */
}

.table-container .MuiTableRow-root.Mui-selected {
  background-color: #e0e0e0; /* Set the background color for the selected row */
}
/* Set different colors for each column */
.tableContainer .MuiTableCell-root:nth-of-type(1) {
  background-color: #f5f5f5;
}

.tableContainer .MuiTableCell-root:nth-of-type(2) {
  background-color: #e8e8e8;
}

.tableContainer .MuiTableCell-root:nth-of-type(3) {
  background-color: #d9d9d9;
}
/* Color for the column containing {workflow.modified_time} */
.tableContainer .MuiTableCell-root:nth-of-type(7) {
  background-color: #c0c0c0; /* Replace with your desired color */
}

/* Custom toast positioning */
.center-toast {
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  position: fixed !important;
  width: 400px; /* Set the width of the toast */
  font-size: 1.2rem; /* Set the font size within the toast */
}

/* Custom styles for the toast itself */
.custom-toast {
  background-color: #d32f2f !important; /* Red background color */
  color: white !important; /* White text color */
  padding: 20px !important; /* Add padding inside the toast */
  border-radius: 8px !important; /* Rounded corners */
}

/* Styles for the buttons inside the toast */
.center-toast button {
  margin: 0 5px; /* Space out buttons */
  color: white; /* Color of button text */
  background-color: #cd1010; /* Background color for "Yes" button */
}

.center-toast button:nth-child(2) {
  background-color: #f4d436; /* Background color for "No" button */
}
