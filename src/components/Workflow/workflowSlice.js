import { createSlice } from '@reduxjs/toolkit';

const workflows = [];

const initialState = {
    workflows:workflows,
    selected_workflow_id:null
};

export const workflowSlice = createSlice({
    name: 'workflow',
    initialState,
    reducers: {
        setSelectedWorkflowId:(state,action) => {
            state.selected_workflow_id = action.payload;
        },
        setWorkflows: (state, action) => {
            state.workflows = action.payload;
            // 更新localStorage中的用户名
        },
        updateSelctedWorkflow: (state, action) => {
            const { selected_workflow_id, ...updatedFields } = action.payload;
            
            // 查找指定的工作流
            const index = state.workflows.findIndex(workflow => workflow.id === selected_workflow_id);
            
            if (index !== -1) {
                // 更新已有的工作流，保留现有字段并更新传入的字段
                state.workflows[index] = {
                    ...state.workflows[index],  // 保留原有的字段
                    ...updatedFields,           // 覆盖传入的字段
                };
            } else {
                // 如果找不到，添加新的工作流
                state.workflows.push({
                    id: selected_workflow_id,  // 使用 selected_workflow_id 作为 id
                    ...updatedFields,           // 添加所有传入的字段
                });
            }
        },
        
        updateWorkflow: (state, action) => {
            const { id, workflow_name, nodes, edges,database, domain,username } = action.payload;
            // Find the index of the workflow to update
            const index = state.workflows.findIndex(workflow => workflow.id === id);
        
            if (index !== -1) {
                // Update the workflow at the found index
                state.workflows[index] = {
                    ...state.workflows[index],
                    workflow_name,
                    nodes,
                    edges,
                    database,
                    domain
                };
            } else {
                // Add a new workflow if not found
                state.workflows.push({
                    id,
                    workflow_name,
                    nodes,
                    edges,
                    database,
                    domain,
                    username
                });
            }
        },
        // setSelectedDomain:(state, action)=>{
        //     state.selected_workflow_id = action.payload;
        // },
    },
});

// Action creators are generated for each case reducer function
export const { setWorkflows,updateWorkflow,setSelectedWorkflowId,updateSelctedWorkflow } = workflowSlice.actions;

export default workflowSlice.reducer;
