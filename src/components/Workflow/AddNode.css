.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  background-color: #f0f0f0;
  transition: width 0.3s ease;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 1000; /* 确保在 ReactFlow 组件之上 */
}

.sidebar.expanded {
  width: 250px;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
}

.menu-items {
  display: flex;
  flex-direction: column;
  padding: 10px;
  overflow-y: auto;  /* 允许垂直滚动 */
  max-height: calc(100vh - 50px); /* 计算菜单项最大高度，确保sidebar header始终可见 */
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 5px;
  border: none;
  background: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #e0e0e0;
}

.icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-weight: bold;
}

.label {
  flex-grow: 1;
}

.add-icon {
  margin-left: auto;
}
