import React from 'react';
import { Tabs } from 'antd';
import PromptList from './Agents/prompt/PromptList';
import WorkflowList from './Agents/workflows/WorkflowList';
import KnowledgeBaseList from './Agents/KnowledgeBase/KnowledgeBaseList';
import './PersonalSpace.css';

const { TabPane } = Tabs;

const PersonalSpace = () => {
  return (
    <Tabs defaultActiveKey="1">
      <TabPane tab="工作流" key="1">
        <WorkflowList />
      </TabPane>
      <TabPane tab="知识库" key="2">
        <KnowledgeBaseList />
      </TabPane>
      <TabPane tab="提示词" key="3">
        <PromptList />
      </TabPane>
    </Tabs>
  );
};

export default PersonalSpace;