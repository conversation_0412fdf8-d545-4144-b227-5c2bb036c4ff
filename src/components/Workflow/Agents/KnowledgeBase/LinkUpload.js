import React, { useState } from 'react';
import { Input, Button, message, notification } from 'antd';
import { LinkOutlined } from '@ant-design/icons';
import { uploadFileFunction } from '../../../Routers/Router';

const LinkUpload = ({ record, onUploadComplete, onClose }) => {
  const [link, setLink] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const handleLinkChange = (e) => {
    setLink(e.target.value);
  };

  const handleSubmit = async () => {
    if (!link) {
      message.error('Please enter a valid link');
      return;
    }

    setIsUploading(true);
    try {
      // Convert the link to a File object
      const linkFile = new File([link], 'link.txt', { type: 'text/plain' });
      const result = await uploadFileFunction(linkFile, record.id);

      if (result.code === 200) {
        notification.success({
          message: 'Link uploaded successfully',
          description: `The link has been uploaded and processed.`,
          duration: 3,
        });
        if (onUploadComplete) {
          onUploadComplete();
        }
        onClose();
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      notification.error({
        message: 'Link upload failed',
        description: `Error: ${error.message}`,
        duration: 3,
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div>
      <Input
        prefix={<LinkOutlined />}
        value={link}
        onChange={handleLinkChange}
        placeholder="Enter your link here"
      />
      <Button
        type="primary"
        onClick={handleSubmit}
        style={{ marginTop: 16 }}
        loading={isUploading}
      >
        Upload Link
      </Button>
    </div>
  );
};

export default LinkUpload;