// import { createSlice } from '@reduxjs/toolkit';

// const loadKnowledgeBases = () => {
//   try {
//     const storedKnowledgeBases = localStorage.getItem('knowledgeBases');
//     return storedKnowledgeBases ? JSON.parse(storedKnowledgeBases) : [];
//   } catch (error) {
//     console.error('Failed to load knowledge bases from localStorage:', error);
//     return [];
//   }
// };

// const saveKnowledgeBases = (knowledgeBases) => {
//   try {
//     localStorage.setItem('knowledgeBases', JSON.stringify(knowledgeBases));
//   } catch (error) {
//     console.error('Failed to save knowledge bases to localStorage:', error);
//   }
// };

// const initialState = {
//   knowledgeBases: loadKnowledgeBases(),
// };

// export const KnowledgeBaseSlice = createSlice({
//   name: 'knowledgeBase',
//   initialState,
//   reducers: {
//     setKnowledgeBases: (state, action) => {
//       state.knowledgeBases = action.payload;
//       saveKnowledgeBases(state.knowledgeBases);
//     },
//     addKnowledgeBase: (state, action) => {
//       state.knowledgeBases.push(action.payload);
//       saveKnowledgeBases(state.knowledgeBases);
//     },
//     updateKnowledgeBase: (state, action) => {
//       const index = state.knowledgeBases.findIndex(base => base.id === action.payload.id);
//       if (index !== -1) {
//         state.knowledgeBases[index] = action.payload;
//         saveKnowledgeBases(state.knowledgeBases);
//       }
//     },
//     removeKnowledgeBase: (state, action) => {
//       state.knowledgeBases = state.knowledgeBases.filter(base => base.id !== action.payload);
//       saveKnowledgeBases(state.knowledgeBases);
//     },
//     clearKnowledgeBases: (state) => {
//       state.knowledgeBases = [];
//       saveKnowledgeBases(state.knowledgeBases);
//     },
//   },
// });

// export const {
//   setKnowledgeBases,
//   addKnowledgeBase,
//   updateKnowledgeBase,
//   removeKnowledgeBase,
//   clearKnowledgeBases,
// } = KnowledgeBaseSlice.actions;

// export default KnowledgeBaseSlice.reducer;