/* FileUploadUI.css */
.file-upload-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .file-upload-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 16px;
    color: #333333;
  }
  
  .file-upload-steps {
    margin-bottom: 32px;
  }
  
  .file-upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 32px;
    text-align: center;
    background-color: #fafafa;
    transition: border-color 0.3s;
  }
  
  .file-upload-area:hover {
    border-color: #1890ff;
  }
  
  .file-upload-button {
    margin-bottom: 16px;
  }
  
  .file-upload-text {
    color: #8c8c8c;
    font-size: 14px;
  }
  
  .file-upload-next-button {
    margin-top: 16px;
  }
  
  /* 覆盖 Ant Design 默认样式 */
  .ant-upload.ant-upload-drag {
    background: none;
    border: none;
  }
  
  .ant-upload.ant-upload-drag .ant-upload {
    padding: 0;
  }
  .ant-upload-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    margin-bottom: 8px;
  }
  
  .ant-upload-list-item-info {
    display: flex;
    flex-direction: column;
  }
  
  .ant-upload-list-item-name {
    font-weight: bold;
  }
  
  .ant-upload-list-item-size {
    color: #8c8c8c;
    font-size: 12px;
  }
  
  .ant-upload-list-item-status {
    color: #52c41a;
  }
  .unit-name-wrap {
    display: flex;
    align-items: center;
  }
  
  .unit-name-input {
    flex: 1;
  }
  
  .upload-status-wrap {
    display: flex;
    align-items: center;
  }