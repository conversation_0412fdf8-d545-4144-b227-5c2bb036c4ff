import React, { useState } from 'react';
import { Upload, Button, Steps, message, Table, Input, Progress,notification } from 'antd';
import { CloudUploadOutlined, DeleteOutlined, FilePdfOutlined, CheckCircleFilled } from '@ant-design/icons';
import { uploadFileFunction, createKBData } from '../../../Routers/Router';
import { useSelector } from 'react-redux';
import { AGENT_KNOWLEDGE_BASE_ENDPOINT } from '../../../Configs/Config';
import './FileUpload.css';

const { Step } = Steps;

const FileUpload = ({ record, onUploadComplete, onClose }) => {
  const [fileList, setFileList] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [fileData, setFileData] = useState([]);
  const [progress, setProgress] = useState(0);
  const { user_info } = useSelector((state) => state.user);

  const handleChange = (info) => {
    setFileList(info.fileList.map(file => ({
      ...file,
      status: file.status === 'done' ? 'success' : file.status,
    })));
  };

  const handleUploadSuccess = (files, newFileData) => {
    notification.success({
        message: '文件上传成功',
        description: `${files.length}文件上传成功`,
        duration: 3, // 保持通知开启直到手动关闭
      })
    setFileData((prevFileData) => {
      const updatedFileData = [...prevFileData];
      newFileData.forEach((data) => {
        if (!updatedFileData.some((item) => item.filename === data.filename)) {
          updatedFileData.push(data);
        }
      });
      setUploadedFiles(updatedFileData);
      return updatedFileData;
    });
    setCurrentStep(1);
    setIsUploading(false);
  };

  const handleUploadError = (error) => {
    notification.error({
        message: '文件上传失败',
        description: `错误信息: ${error.message}`,
        duration: 3, // 保持通知开启直到手动关闭
      });
    setIsUploading(false);
    setFileList([]);
    setCurrentStep(0);
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('Please select at least one file to upload');
      return;
    }

    setIsUploading(true);

    const uploadPromises = fileList.map((file) =>
      uploadFileFunction(file.originFileObj,record.id).then((result) => {
        if (result.code === 200) {
          return result.data;
        } else {
          throw new Error(result.message || 'Upload failed');
        }
      })
    );

    try {
      const results = await Promise.all(uploadPromises);
      const newFileData = results
        .filter((r) => r && r.filename && r.url)
        .map((r) => ({ filename: r.filename, url: r.url, chunk_type: r.chunk_type, doc_id:r.doc_id }));
      if (newFileData.length > 0) {
        handleUploadSuccess(fileList, newFileData);
      } else {
        throw new Error('Failed to get valid file data');
      }
    } catch (error) {
      handleUploadError(error);
    }
  };

  const handleDrop = async (event) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    if (files.length === 0) return;

    setFileList((prevFileList) => [
      ...prevFileList,
      ...files.map((file, index) => ({
        uid: `${Date.now()}-${index}`,
        name: file.name,
        status: 'done',
        originFileObj: file,
      })),
    ]);

    setIsUploading(true);

    const uploadPromises = files.map((file) =>
      uploadFileFunction(file,record.id).then((result) => {
        if (result.code === 200) {
          return result.data;
        } else {
          throw new Error(result.message || 'Upload failed');
        }
      })
    );

    try {
      const results = await Promise.all(uploadPromises);
      const newFileData = results
        .filter((r) => r && r.filename && r.url)
        .map((r) => ({ filename: r.filename, url: r.url, chunk_type: r.chunk_type,doc_id:r.doc_id }));
      if (newFileData.length > 0) {
        handleUploadSuccess(files, newFileData);
      } else {
        throw new Error('Failed to get valid file data');
      }
    } catch (error) {
      handleUploadError(error);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const processFiles = async () => {
    const kb_data_file_infos = uploadedFiles.map((file_info) => ({
      ...record,
      ...file_info,
    }));
    try {
      const agent_insert_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/insert?username=${user_info.username}`;
      await createKBData({
        endpoint_api: agent_insert_endpoint_api,
        kb_data_file_infos: kb_data_file_infos,
      });

      message.success('Files processed successfully');
      setFileList([]);
      setCurrentStep(2);
    //   setUploadedFiles([]);
      if (onUploadComplete) {
        onUploadComplete();
      }
    } catch (error) {
      message.error('File processing failed, please try again.');
    }finally {
      setProgress(100); // Ensure progress reaches 100% after processing
    }
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      // Simulating the progress update
      setProgress(0);
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval);
            processFiles();
            return 90;
          }
          return prev + 1;
        });
      }, 50);
    } else if (currentStep === 2) {
      if (onClose) {
        onClose();
        setUploadedFiles([]);
        setCurrentStep(0);
        setFileList([]);
        setProgress(0);
      }
    }
  };

  const getButtonText = () => {
    if (isUploading) return 'Uploading';
    if (currentStep === 2) return '完成';
    return '下一步';
  };

  const customRequest = async ({ file, onSuccess, onError, onProgress }) => {
    try {
      const result = await uploadFileFunction(file,record.id);
      if (result.code === 200) {
        onSuccess(result.data, file);
      } else {
        onError(new Error(result.message || 'Upload failed'));
      }
    } catch (error) {
      onError(error);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const columns = [
    {
      title: '文档名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div className="unit-name-wrap">
          <FilePdfOutlined style={{ color: '#F54A45', marginRight: 8 }} />
          <div className="unit-name-input">
            <Input
              value={text}
              maxLength={100}
              onChange={(e) => {
                const newFileList = fileList.map(file =>
                  file.uid === record.uid ? { ...file, name: e.target.value } : file
                );
                setFileList(newFileList);
              }}
              suffix={`${text.length}/100`}
            />
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <span className="upload-status-wrap">
          {status === 'done' && (
            <>
              <CheckCircleFilled style={{ color: '#4D53E8', marginRight: 4 }} />
              <span>上传完成</span>
            </>
          )}
          {status === 'uploading' && <span>上传中...</span>}
        </span>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      render: (_, record) => {
        const fileSize = record.originFileObj ? record.originFileObj.size : 0;
        return <span>{formatFileSize(fileSize)}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button
          type="text"
          icon={<DeleteOutlined />}
          onClick={() => {
            const newFileList = fileList.filter(item => item.uid !== record.uid);
            setFileList(newFileList);
          }}
        />
      ),
    },
  ];

  return (
    <div className="file-upload-container">
      <Steps current={currentStep} className="file-upload-steps">
        <Step title="文件上传" />
        <Step title="数据处理" />
        <Step title="完成" />
      </Steps>
      <div 
        className="file-upload-area"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        <Upload
          accept=".pdf,.txt,.doc,.docx,.md"
          multiple
          fileList={fileList}
          onChange={handleChange}
          customRequest={customRequest}
          showUploadList={false}
        >
          <Button icon={<CloudUploadOutlined />} size="large" className="file-upload-button">
            点击上传或拖拽文档到这里
          </Button>
        </Upload>
        <p className="file-upload-text">
          支持 PDF, TXT, DOC, DOCX, MD. 最多支持300个文件, 20MB/文件 , PDFs 不超过250 页
        </p>
      </div>
      <Table
        columns={columns}
        dataSource={fileList}
        pagination={false}
        rowKey="uid"
      />
      {currentStep === 1 && (
        <div className="file-upload-progress">
          <Progress percent={progress} />
        </div>
      )}
      <div className="file-upload-next-button">
        <Button
          type="primary"
          onClick={currentStep === 0 ? handleUpload : handleNextStep}
          disabled={(currentStep !== 2 && fileList.length === 0) || isUploading}
          loading={isUploading}
        >
          {getButtonText()}
        </Button>
      </div>
    </div>
  );
};

export default FileUpload;
