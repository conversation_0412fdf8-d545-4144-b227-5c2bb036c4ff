import React, { useEffect, useState } from 'react';
import { Button, message, Switch, Select, Modal, Pagination } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import SidebarList from '../../../SidebarList';
import {
  KNOWLEDGE_BASE_EDIT_NAME,
  KNOWLEDGE_BASE_NEW_NAME,
  KNOWLEDGE_BASE_ENDPOINT,
  SUPERUSER,
  ENTERPRISE,
  USER,
  AGENT_KNOWLEDGE_BASE_ENDPOINT,
  FILE_MANAGERS_NAME,
} from '../../../Configs/Config';
import { fetchBulk, deleteData,
  //  deleteBulk, 
   updateData } from '../../../Routers/Router';
import FileUpload from './ExcelUpload'; // Import the FileUpload component
// import Logout from '../../../Login/Logout';
import { Card } from '@chatui/core';
import './KnowledgeBase.css';

const { Option } = Select;

const KnowledgeBaseList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const sortField = 'updated_at';
  const sortOrder = 'desc';
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalItems, setTotalItems] = useState(0);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  // const [roleFilter, setRoleFilter] = useState('all');

  useEffect(() => {
    getData(currentPage, pageSize);
  }, [sortField, sortOrder, currentPage, pageSize]);

  const getData = async (page, pageSize) => {
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${user_info.username}`;

      const result = await fetchBulk(endpoint_api);
      setData(result.data);
      setTotalItems(result.count);
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch knowledge base data');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page, newPageSize) => {
    setCurrentPage(page);
    setPageSize(newPageSize);
    getData(page, newPageSize);
  };

  const showFileUploadModal = (record) => {
    setSelectedRecord(record);
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setSelectedRecord(null);
  };

  const handleDelete = async (record) => {
    const endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/${record.id}`;
    const agent_delete_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/${record.agent_kb_name}`;
    try {
      await deleteData(endpoint_api);
      await deleteData(agent_delete_endpoint_api);
      setData(data.filter((item) => item.id !== record.id));
      message.success('知识库删除成功');
    } catch (error) {
      message.error('删除知识库失败');
    }
  };

  const handleIsActiveChange = async (record, isActive) => {
    const endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/${record.id}`;
    const updatedRecord = { ...record, is_active: isActive };
    try {
      const update_data = await updateData(endpoint_api, updatedRecord);
      setData(data.map(item =>
        item.id === record.id ? { ...update_data, is_active: isActive } : item
      ));
      message.success('知识库状态更新成功');
    } catch (error) {
      message.error('更新知识库状态失败');
    }
  };
  const handleIsPublishedChange = async (record, isPublished) => {
    const endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/${record.id}`;
    const updatedRecord = { ...record, is_published: isPublished };
    try {
      const update_data = await updateData(endpoint_api, updatedRecord);
      setData(data.map(item =>
        item.id === record.id ? { ...update_data, is_published: isPublished } : item  
      ));
      message.success('知识库状态更新成功');
    } catch (error) {
      message.error('更新知识库状态失败');
    }
  };
  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">知识库列表</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        <button
          className="custom-button"
          onClick={() => navigate(`${KNOWLEDGE_BASE_NEW_NAME}`)}
        >
          新增
        </button>
      </div>
      <div className="card-container">
        {data.map((record) => (
          <Card 
          key={record.id} 
          title={record.name} 
          className="Card"
          style={{ marginBottom: '16px', cursor: 'pointer' }}
          onClick={(e) => {
            e.stopPropagation(); // Prevent card click event propagation
            if (user_info.username===record.username){
              navigate(`${KNOWLEDGE_BASE_EDIT_NAME}/${record.id}`);
            }
          }}
          >
            <p>名称: {record.name}</p>
            <p>领域: {record.domain}</p>
            <p>描述: {record.description}</p>
            {/* <p>语言: {record.languages}</p> */}
            <p>语言: {record.languages.join(', ')}</p>
            <p>创建者: {record.username}</p>
            <Switch
              checked={record.is_active}
              onChange={(checked,event) => {
                event.stopPropagation(); // Stop event propagation here
                handleIsActiveChange(record, checked);
              }}
              checkedChildren="激活"
              unCheckedChildren="未激活"
              disabled={user_info.role === SUPERUSER && record.username !== user_info.username}
            />
            <Switch
              checked={record.is_published}
              onChange={(checked,event) => {
                event.stopPropagation(); // Stop event propagation here
                handleIsPublishedChange(record, checked);
              }}
              checkedChildren="发布"
              unCheckedChildren="未发布"
              disabled={user_info.role === SUPERUSER && record.username !== user_info.username}
            />
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                handleDelete(record);
              }}
              disabled={record.username !== user_info.username}
            >
              删除
            </Button>
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                showFileUploadModal(record)
              }}
              disabled={user_info.username !== record.username}
            >
              上传文件
            </Button>
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                navigate(`${FILE_MANAGERS_NAME}`, { state: { kb_id: record.id } });
              }}
            >
              知识库文件
            </Button>

          </Card>
        ))}
      </div>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={totalItems}
        showSizeChanger
        onChange={handlePageChange}
        pageSizeOptions={['12', '24', '48', '96']}
        style={{ marginTop: '16px', textAlign: 'center' }}
      />
      <Modal
        title="上传文件"
        open={isModalVisible}
        onCancel={handleModalCancel}
        footer={null}
        width={800}
      >
        <FileUpload 
          record={selectedRecord}
          onUploadSuccess={() => {
            handleModalCancel();
            // getData(roleFilter);
          }}
          onClose={handleModalCancel}
        />
      </Modal>
    </div>
  );
};

export default KnowledgeBaseList;
