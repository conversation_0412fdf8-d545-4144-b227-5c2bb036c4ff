import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Switch, InputNumber, Select, message, Modal, Divider, notification, Spin, Card, Upload, Tooltip } from 'antd';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { fetchData, updateData } from '../../../Routers/Router';
import { uploadImageToServer } from '../workflows/uploadUtils';
import { KNOWLEDGE_BASES_NAME,KNOWLEDGE_BASE_ENDPOINT,MANAGER_API_BASE_URL } from '../../../Configs/Config';
import './KnowledgeBase.css'; // Import the CSS file
import SidebarList from '../../../SidebarList';
import { parseExamplesData, cleanExamplesData, createNewExample, getExampleHelpContent } from './ExampleHelper';
const { Option } = Select;

const KnowledgeBaseEdit = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [searchTask, setSearchTask] = useState('search');
  const navigate = useNavigate();
  const { id } = useParams();
  const [isActive, setIsActive] = useState(true);
  const [isPublished, setIsPublished] = useState(true);
  const [multiTenancy, setMultiTenancy] = useState(false);
  const [examples, setExamples] = useState([]);
  const [debugInfo, setDebugInfo] = useState('');

  const showExampleHelp = () => {
    Modal.info({
      title: '示例配置说明',
      content: getExampleHelpContent(),
      width: 550,
    });
  };

  useEffect(() => {
    const getKnowledgeBase = async () => {
      try {
        const endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/${id}`;
        const data = await fetchData(endpoint_api);
        console.log('获取到的知识库数据:', data);
        form.setFieldsValue(data);
        setSearchTask(data.search_task);
        setIsActive(data.is_active);
        setIsPublished(data.is_published);
        setMultiTenancy(data.multi_tenancy);
        
        // 使用辅助函数处理示例数据
        const formattedExamples = parseExamplesData(data.examples);
        setExamples(formattedExamples);
        setDebugInfo(`加载了 ${formattedExamples.length} 个示例`);
        
      } catch (error) {
        console.error('获取知识库详情失败:', error);
        message.error('获取知识库详情失败');
        setDebugInfo('获取知识库详情失败: ' + error.message);
      } finally {
        setInitialLoading(false);
      }
    };

    getKnowledgeBase();
  }, [id, form]);

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/${id}`;
          
          // 使用辅助函数清理示例数据
          const cleanedExamples = cleanExamplesData(examples);
          console.log('准备提交的 examples 数据:', cleanedExamples);
          
          const valuesWithExamples = {
            ...values,
            examples: cleanedExamples
          };
          
          const response = await updateData(endpoint_api, valuesWithExamples);
          if (response) {
            message.success('知识库更新成功');
            navigate(`${KNOWLEDGE_BASES_NAME}`);
          }
        } catch (error) {
          console.error('更新知识库失败:', error);
          notification.error({
            message: '更新失败',
            description: `更新知识库失败，请重试。错误信息: ${error.message}`,
            duration: 5,
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleSearchFieldsChange = (value) => {
    if (value.length === 1 && value[0] === 'chunk') {
      Modal.confirm({
        title: '推荐选择',
        content: '建议同时选择"标题"和"文本内容"以获得更好的搜索结果。是否要同时选择两者？',
        onOk() {
          form.setFieldsValue({ search_fields: ['title', 'chunk'] });
        },
        onCancel() {
          // 用户选择保持只选择"文本内容"
        },
      });
    }
  };

  const handleSearchTaskChange = (value) => {
    setSearchTask(value);
    if (value === 'long_search') {
      form.setFieldsValue({ retrieval_types: ['bm25'] });
    }
  };

  if (initialLoading) {
    return <Spin tip="Loading..." />;
  }

  return (
    <div className="page-container">
      {/* <h1 className="page-title">编辑知识库</h1> */}
      <div className="header-container">
        <h1 className="page-title">编辑知识库</h1>
        <SidebarList />
      </div>
      <div className="form-container">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={(errorInfo) => {
            console.log('Failed:', errorInfo);
          }}
        >
          <Form.Item
            name="name"
            label="知识库"
            rules={[{ required: true, message: 'Please input the vector store name!' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="description"
            label="知识库描述"
            rules={[{ required: true, message: 'Please input the knowledge_base description!' }]}
            extra="知识库中主要包含的疾病，药物"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="domain"
            label="领域"
            rules={[{ required: true, message: 'Please input the knowledge_base 领域!' }]}
          >
            <Input />
          </Form.Item>

          <Divider orientation="center" className="section-divider">
            示例配置 {examples.length > 0 ? `(${examples.length})` : ''}
            <Tooltip title="查看示例配置说明">
              <QuestionCircleOutlined 
                onClick={showExampleHelp} 
                style={{ marginLeft: '8px', cursor: 'pointer', color: '#1890ff' }}
              />
            </Tooltip>
            {debugInfo && <span style={{ fontSize: '12px', color: '#888', marginLeft: '10px' }}>{debugInfo}</span>}
          </Divider>
          
          <Form.Item label="示例">
            <div style={{ marginBottom: 16 }}>
              <Button 
                type="dashed" 
                onClick={() => {
                  setExamples([...examples, createNewExample()]);
                  setDebugInfo(`已添加新示例，当前共 ${examples.length + 1} 个示例`);
                }}
                icon={<PlusOutlined />}
              >
                添加示例
              </Button>
            </div>
            
            {examples.length === 0 && (
              <div style={{ textAlign: 'center', padding: '20px', color: '#888' }}>
                暂无示例数据，请点击"添加示例"按钮创建
              </div>
            )}
            
            {examples.map((example, index) => (
              <Card 
                key={index} 
                style={{ marginBottom: 16 }}
                title={`示例 ${index + 1}`}
                extra={
                  <Button
                    type="text"
                    danger
                    onClick={() => {
                      setExamples(examples.filter((_, idx) => idx !== index));
                    }}
                  >
                    删除
                  </Button>
                }
              >
                <Form.Item
                  label="示例文本"
                  style={{ marginBottom: 16 }}
                >
                  <Input.TextArea
                    value={example.text}
                    onChange={(e) => {
                      const newExamples = [...examples];
                      newExamples[index].text = e.target.value;
                      setExamples(newExamples);
                    }}
                    rows={4}
                    placeholder="请输入示例文本"
                  />
                </Form.Item>
                
                <Form.Item
                  label="可对话"
                  style={{ marginBottom: 16 }}
                >
                  <Switch
                    checked={example.conversable}
                    onChange={(checked) => {
                      const newExamples = [...examples];
                      newExamples[index].conversable = checked;
                      setExamples(newExamples);
                    }}
                  />
                </Form.Item>
                
                <Form.Item
                  label="附件"
                >
                  <Upload
                    listType="picture-card"
                    fileList={(example.attachments || []).map(att => ({
                      uid: att.url,
                      name: att.filename,
                      status: 'done',
                      url: att.url,
                      thumbUrl: att.url
                    }))}
                    onRemove={(file) => {
                      const newExamples = [...examples];
                      newExamples[index].attachments = (newExamples[index].attachments || []).filter(
                        att => att.url !== file.uid
                      );
                      setExamples(newExamples);
                      return true;
                    }}
                    beforeUpload={async (file) => {
                      try {
                        setLoading(true);
                        const url = await uploadImageToServer(URL.createObjectURL(file), file.name);
                        
                        const newExamples = [...examples];
                        if (!newExamples[index].attachments) {
                          newExamples[index].attachments = [];
                        }
                        
                        newExamples[index].attachments.push({
                          content: file.name,
                          url: url,
                          filename: file.name
                        });
                        
                        setExamples(newExamples);
                        message.success(`${file.name} 上传成功`);
                      } catch (error) {
                        console.error('上传失败:', error);
                        message.error(`${file.name} 上传失败`);
                      } finally {
                        setLoading(false);
                      }
                      return false;
                    }}
                  >
                    <div>
                      <PlusOutlined />
                      <div style={{ marginTop: 8 }}>上传</div>
                    </div>
                  </Upload>
                </Form.Item>
              </Card>
            ))}
          </Form.Item>
          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
            initialValue={isActive}
          >
            <Switch checked={isActive} onChange={(checked) => setIsActive(checked)} />
          </Form.Item>
          <Form.Item
            name="is_published"
            label="是否对外发布"
            valuePropName="checked"
            initialValue={isPublished}
          >
            <Switch checked={isPublished} onChange={(checked) => setIsPublished(checked)} />
          </Form.Item>
          <Form.Item
            name="multi_tenancy"
            label="多租户"
            valuePropName="checked"
            initialValue={multiTenancy}
          >
            <Switch checked={multiTenancy} onChange={(checked) => setMultiTenancy(checked)} disabled={true}/>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存更改
            </Button>
          </Form.Item>
          <Divider orientation="center" className="section-database">
            向量库设置
          </Divider>
          <Form.Item
            name="languages"
            label="搜索字段"
            initialValue={['zh']}
            extra="知识库语言"
          >
            <Select 
            mode="multiple" 
            disabled={true}
            // onChange={handleSearchFieldsChange}
            >
              <Option value="zh">中文</Option>
              <Option value="en">英文</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="search_fields"
            label="搜索字段"
            initialValue={['title','chunk']}
            extra="目标搜索字段，目前只支持搜索'标题'和'文本内容',可多选"
          >
            <Select 
            mode="multiple" 
            disabled={true}
            onChange={handleSearchFieldsChange}>
              <Option value="title">标题</Option>
              <Option value="chunk">文本内容</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="retrieval_types"
            label="搜索方法"
            initialValue={['qq','bm25','hybrid']}
          >
            <Select mode="multiple" disabled={true}>
              <Option value="bm25" disabled={searchTask === 'long_search'}>字符搜索</Option>
              <Option value="qq" disabled={searchTask === 'long_search'}>语义搜索</Option>
              <Option value="hybrid" disabled={searchTask === 'long_search'}>字符和语义联合搜索</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="search_task"
            label="搜索方法"
            initialValue="search"
            extra={
              <>
                <span style={{ fontWeight: 'bold', color: 'blue' }}>搜索</span>方法可以使用
                <span style={{ fontWeight: 'bold', color: 'blue' }}>字符检索</span>,
                <span style={{ fontWeight: 'bold', color: 'blue' }}>语义检索</span>,
                <span style={{ fontWeight: 'bold', color: 'blue' }}>字符和语义组合检索</span>;
                <span style={{ fontWeight: 'bold', color: 'blue' }}>长上下文搜索</span>只支持
                <span style={{ fontWeight: 'bold', color: 'red' }}>字符检索</span>，但是支持
                <span style={{ fontWeight: 'bold', color: 'red' }}>百万上下文</span>
              </>
            }
          >
            <Select onChange={handleSearchTaskChange} disabled={true}>
              <Option value="search">搜索</Option>
              <Option value="long_search">长上下文搜索</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="llm_search_algorithm_url"
            label="向量模型"
            initialValue="bge"
          >
            <Select disabled={true}>
              <Option value="bge" >bge</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="retrieval_topK"
            label="搜索数目限制"
            initialValue={50}
          >
            <InputNumber min={1} max={100}/>
          </Form.Item>
          <Form.Item
            name="qq_retrieval_threshold"
            label="语义搜索的阈值"
            initialValue={0.8}
          >
            <InputNumber step={0.1} min={0} max={1} />
          </Form.Item>
          <Form.Item
            name="rerank_model_name"
            label="排序模型"
            initialValue="bge-reranker-large"
            rules={[{ required: true, message: 'Please select the rerank model name!' }]}
          >
            <Select disabled={true}>
              <Option value="bge-reranker-large">bge-reranker-large</Option>
              <Option value="bge-medium">bge-medium</Option>
              <Option value="bge-small">bge-small</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="rerank_threshold"
            label="重排阈值"
            initialValue={3}
            extra="默认4.6比较合适"
          >
            <InputNumber min={-10} step={0.1} max={10} disabled={true}/>
          </Form.Item>
          <Form.Item
            name="rerank_batch_size"
            label="重排模型的批次大小"
            initialValue={2}
            
          >
            <InputNumber min={1} disabled={true}/>
          </Form.Item>
          <Form.Item
            name="generator_threshold"
            label="Generator Threshold"
            initialValue={3.0}
          >
            <InputNumber step={0.1} />
          </Form.Item>
          <Form.Item
            name="top_chunk"
            label="用于生成答案的文本块数量"
            initialValue={3}
          >
            <InputNumber min={1} />
          </Form.Item>
          <Form.Item
            name="version"
            label="打印日志"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
          <Divider orientation="center" className="section-divider">
            文本分块设置
          </Divider>
          <Form.Item
            name="chunk_max_len"
            label="最大分块长度"
            initialValue={448}
            rules={[{ required: true, message: '请输入最大分块长度！' }]}
          >
            <InputNumber min={100} max={8000} disabled={true} />
          </Form.Item>
          <Form.Item
            name="chunk_min_len"
            label="最小分块长度"
            initialValue={32}
            rules={[{ required: true, message: '请输入最小分块长度！' }]}
          >
            <InputNumber min={1} max={1000} disabled={true}/>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default KnowledgeBaseEdit;
