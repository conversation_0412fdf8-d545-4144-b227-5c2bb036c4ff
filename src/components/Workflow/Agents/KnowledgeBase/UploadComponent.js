import React, { useState } from 'react';
import { Select, Card } from 'antd';
import { UploadOutlined, LinkOutlined, FileExcelOutlined } from '@ant-design/icons';
import FileUpload from './FileUpload';
import LinkUpload from './LinkUpload';
import ExcelUpload from './ExcelUpload';

const { Option } = Select;

const UploadComponent = ({ record, onUploadComplete, onClose }) => {
  const [uploadType, setUploadType] = useState('file');

  const handleUploadTypeChange = (value) => {
    setUploadType(value);
  };

  return (
    <Card
      title="Upload Data"
      extra={
        <Select
          defaultValue="file"
          style={{ width: 120 }}
          onChange={handleUploadTypeChange}
        >
          <Option value="file">
            <UploadOutlined /> PDF/Word/Text
          </Option>
          <Option value="link">
            <LinkOutlined /> Link
          </Option>
          <Option value="excel">
            <FileExcelOutlined /> Excel
          </Option>
        </Select>
      }
    >
      {uploadType === 'file' && (
        <FileUpload
          record={record}
          onUploadComplete={onUploadComplete}
          onClose={onClose}
        />
      )}
      {uploadType === 'link' && (
        <LinkUpload
          record={record}
          onUploadComplete={onUploadComplete}
          onClose={onClose}
        />
      )}
      {uploadType === 'excel' && (
        <ExcelUpload
          record={record}
          onUploadComplete={onUploadComplete}
          onClose={onClose}
        />
      )}
    </Card>
  );
};

export default UploadComponent;