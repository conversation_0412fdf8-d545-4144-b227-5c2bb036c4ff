import React, { useState } from 'react';
import { Upload, Button, message, Table, Input, notification, Tabs,Select } from 'antd';
import { CloudUploadOutlined, DeleteOutlined, FilePdfOutlined, CheckCircleFilled, LinkOutlined } from '@ant-design/icons';
import { uploadFileFunction } from '../../../Routers/Router';
// import { useSelector } from 'react-redux';
import * as XLSX from 'xlsx';
import './FileUpload.css';

const { Option } = Select;

const FileUpload = ({ record, onUploadComplete, onClose }) => {
  const [fileList, setFileList] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  // const { user_info } = useSelector((state) => state.user);

  const [excelData, setExcelData] = useState([]);
  const [headers, setHeaders] = useState([]);
  const [selectedHeaders, setSelectedHeaders] = useState([]);

  const [link, setLink] = useState('');

  const allowedFileTypes = {
    '1': ['pdf', 'txt', 'doc', 'docx', 'md', 'ppt', 'pptx'],
    '2': ['xlsx', 'xls', 'csv'],
  };

  const handleChange = (info) => {
    const currentTab = tabItems[0].key;
    const allowedExtensions = allowedFileTypes[currentTab];
    const newFileList = info.fileList.filter(file => {
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (allowedExtensions.includes(fileExtension)) {
        return true;
      } else {
        notification.error({
          message: '文件类型不支持',
          description: `文件类型不支持: ${file.name}`,
          duration: 3,
        });
        return false;
      }
    });

    setFileList(newFileList.map(file => ({
      ...file,
      status: file.status === 'done' ? 'success' : file.status,
    })));
  };

  const handleUploadSuccess = (files) => {
    notification.success({
      message: '文件上传成功',
      description: `${files.length} 文件上传成功`,
      duration: 3,
    });
    setIsUploading(false);
    setFileList([]);
    if (onUploadComplete) {
      onUploadComplete();
    }
  };

  const handleUploadError = (error) => {
    notification.error({
      message: '文件上传失败',
      description: `错误信息: ${error.message}`,
      duration: 3,
    });
    setIsUploading(false);
    setFileList([]);
  };

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择至少一个文件上传');
      return;
    }

    setIsUploading(true);

    const uploadPromises = fileList.map((file) =>
      uploadFileFunction(file.originFileObj, record.id).then((result) => {
        return result.data;
      })
    );

    try {
      await Promise.all(uploadPromises);
      // console.log(results,'resultsresults')
      handleUploadSuccess(fileList);
    } catch (error) {
      handleUploadError(error);
    }
  };

  const handleExcelUpload = async (file) => {
    const allowedExtensions = ['xlsx', 'xls', 'csv'];
    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (!allowedExtensions.includes(fileExtension)) {
      notification.error({
        message: 'Invalid file type',
        description: '只能上传 .xlsx .xls .csv格式文件',
        duration: 3,
      });
      return false;
    }
    setIsUploading(true);
    try {
      const result = await uploadFileFunction(file, record.id);
      if (result.code === 200) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          const headers = jsonData[0];
          const rows = jsonData.slice(1, 4);

          setHeaders(headers);
          setExcelData(rows);
        };
        reader.readAsArrayBuffer(file);

        notification.success({
          message: 'Excel 文件上传成功',
          description: `${file.name} 已上传.`,
          duration: 3,
        });

        if (onUploadComplete) {
          onUploadComplete();
        }
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      notification.error({
        message: 'Excel 文件上传失败',
        description: `错误信息: ${error.message}`,
        duration: 3,
      });
    } finally {
      setIsUploading(false);
    }
    return false;
  };

  const handleHeaderSelect = (value) => {
    setSelectedHeaders(value);
  };

  const customRequest = async ({ file, onSuccess, onError }) => {
    try {
      console.log(file, "onSuccess")
      const result = await uploadFileFunction(file, record.id);
      if (result.code === 200) {
        onSuccess(result.data, file);
      } else {
        onError(new Error(result.message || 'Upload failed'));
      }
    } catch (error) {
      onError(error);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleLinkChange = (e) => {
    setLink(e.target.value);
  };

  const handleLinkUpload = async () => {
    if (!link) {
      message.error('请输入有效的链接');
      return;
    }

    setIsUploading(true);
    try {
      const linkFile = new File([link], 'link.txt', { type: 'text/plain' });
      const result = await uploadFileFunction(linkFile, record.id);

      if (result.code === 200) {
        notification.success({
          message: '链接上传成功',
          description: `链接已上传并处理.`,
          duration: 3,
        });
        if (onUploadComplete) {
          onUploadComplete();
        }
        onClose();
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      notification.error({
        message: '链接上传失败',
        description: `错误信息: ${error.message}`,
        duration: 3,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const columns = [
    {
      title: '文档名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div className="unit-name-wrap">
          <FilePdfOutlined style={{ color: '#F54A45', marginRight: 8 }} />
          <div className="unit-name-input">
            <Input
              value={text}
              maxLength={100}
              onChange={(e) => {
                const newFileList = fileList.map(file =>
                  file.uid === record.uid ? { ...file, name: e.target.value } : file
                );
                setFileList(newFileList);
              }}
              suffix={`${text.length}/100`}
            />
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <span className="upload-status-wrap">
          {status === 'done' && (
            <>
              <CheckCircleFilled style={{ color: '#4D53E8', marginRight: 4 }} />
              <span>上传完成</span>
            </>
          )}
          {status === 'uploading' && <span>上传中...</span>}
        </span>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      render: (_, record) => {
        const fileSize = record.originFileObj ? record.originFileObj.size : 0;
        return <span>{formatFileSize(fileSize)}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button
          type="text"
          icon={<DeleteOutlined />}
          onClick={() => {
            const newFileList = fileList.filter(item => item.uid !== record.uid);
            setFileList(newFileList);
          }}
        />
      ),
    },
  ];

  const excelColumns = headers.map((header, index) => ({
    title: header,
    dataIndex: index,
    key: index,
  }));

  const tabItems = [
    {
      key: '1',
      label: 'PDF/文档上传',
      children: (
        <>
          <div
            className="file-upload-area"
            onDrop={(e) => {
              e.preventDefault();
              const files = Array.from(e.dataTransfer.files);
              setFileList((prevFileList) => [
                ...prevFileList,
                ...files.map((file, index) => ({
                  uid: `${Date.now()}-${index}`,
                  name: file.name,
                  status: 'done',
                  originFileObj: file,
                })),
              ]);
            }}
            onDragOver={(e) => e.preventDefault()}
          >
            <Upload
              accept=".pdf,.txt,.doc,.docx,.md,.ppt,.pptx"
              multiple
              fileList={fileList}
              onChange={handleChange}
              customRequest={customRequest}
              showUploadList={false}
            >
              <Button icon={<CloudUploadOutlined />} size="large" className="file-upload-button">
                点击上传或拖拽文档到这里
              </Button>
            </Upload>
            <p className="file-upload-text">
              支持 PDF, TXT, DOC, DOCX, MD, PPT, PPTX. 最多支持300个文件, 20MB/文件 , PDFs 不超过250 页
            </p>
          </div>
          <Table
            columns={columns}
            dataSource={fileList}
            pagination={false}
            rowKey="uid"
          />
        </>
      )
    },
    {
      key: '2',
      label: 'Excel上传',
      children: (
        <>
          <div
            className="file-upload-area"
            onDrop={(e) => {
              e.preventDefault();
              const files = Array.from(e.dataTransfer.files);
              setFileList((prevFileList) => [
                ...prevFileList,
                ...files.map((file, index) => ({
                  uid: `${Date.now()}-${index}`,
                  name: file.name,
                  status: 'done',
                  originFileObj: file,
                })),
              ]);
            }}
            onDragOver={(e) => e.preventDefault()}
          >
            <Upload
              accept=".xlsx,.xls,.csv"
              beforeUpload={handleExcelUpload}
              showUploadList={false}
            >
              <Button icon={<CloudUploadOutlined />} size="large" className="file-upload-button">
                点击上传或拖拽文档到这里
              </Button>
            </Upload>
            <p className="file-upload-text">
              支持 xlsx, xls ,csv, 最多支持300个文件, 20MB/文件
            </p>
          </div>
          {excelData.length > 0 && (
            <>
              <Table
                dataSource={excelData.map((row, index) => ({ ...row, key: index }))}
                columns={excelColumns}
                pagination={false}
                style={{ marginTop: 16 }}
              />
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 16 }}
                placeholder="选择上传的列"
                onChange={handleHeaderSelect}
              >
                {headers.map((header, index) => (
                  <Option key={index} value={header}>
                    {header}
                  </Option>
                ))}
              </Select>
            </>
          )}
        </>
      )
    },
    {
      key: '3',
      label: '链接上传',
      children: (
        <>
          <Input
            prefix={<LinkOutlined />}
            value={link}
            onChange={handleLinkChange}
            placeholder="输入您的链接"
          />
          <Button
            type="primary"
            onClick={handleLinkUpload}
            style={{ marginTop: 16 }}
            loading={isUploading}
          >
            上传链接
          </Button>
        </>
      )
    }
  ];

  return (
    <div className="file-upload-container">
      <Tabs items={tabItems} />
      <div className="file-upload-next-button">
        <Button
          type="primary"
          onClick={handleUpload}
          disabled={(fileList.length === 0 && excelData.length === 0 && !link) || isUploading}
          loading={isUploading}
        >
          {isUploading ? '上传中' : '上传'}
        </Button>
      </div>
    </div>
  );
};

export default FileUpload;