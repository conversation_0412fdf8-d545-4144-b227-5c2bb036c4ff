import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { SaveOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import {
  WORKFLOW_ENDPOINT, WORKFLOWS_NAME, LLM_ENDPOINT, KNOWLEDGE_BASE_ENDPOINT, START_NODE_NAME,TOOL_ENDPOINT,
  MCP_SERVER_ENDPOINT
} from '../../../Configs/Config';
import ReactFlow, {
  addEdge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
} from 'reactflow';
import AddNode from '../../AddNode';
import { createData } from '../../../Routers/Router';
import { Form, Input, Button, Modal, message, notification, Switch, Space, Select, Tooltip, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { uploadImageToServer } from './uploadUtils';
import 'reactflow/dist/style.css';

import SidebarList from '../../../SidebarList';
import { getLlmOptions, getPromptOptions,getMcpServerOptions } from '../Utils/utils';
import { updateSelctedWorkflow } from '../../workflowSlice';
import { getNodeTypes } from './utils';

const onInit = (reactFlowInstance) => console.log('flow loaded:', reactFlowInstance);

const CreateWorkflow = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const onConnect = useCallback((params) => {
    const restrictedPrefixes = ['attachment', 'query'];
    const sourceHandle = params.sourceHandle || '';
    const targetHandle = params.targetHandle || '';
    
    // 检查源或目标是否为受限的前缀类型
    const sourceRestricted = restrictedPrefixes.some(prefix => sourceHandle.startsWith(prefix));
    const targetRestricted = restrictedPrefixes.some(prefix => targetHandle.startsWith(prefix));
    
    // 如果源是受限类型，目标必须是相同的受限类型
    if (sourceRestricted) {
      const sourcePrefix = restrictedPrefixes.find(prefix => sourceHandle.startsWith(prefix));
      
      // 如果目标不是受限类型，或者是不同的受限类型，不允许连接
      if (!targetHandle.startsWith(sourcePrefix)) {
        message.error(`${sourcePrefix}类型数据只能传递到相同类型的数据端口!`);
        return;
      }
    }
    
    // 如果目标是受限类型，源必须是相同的受限类型
    if (targetRestricted) {
      const targetPrefix = restrictedPrefixes.find(prefix => targetHandle.startsWith(prefix));
      
      // 如果源不是受限类型，或者是不同的受限类型，不允许连接
      if (!sourceHandle.startsWith(targetPrefix)) {
        message.error(`只有${targetPrefix}类型数据才能传递到${targetPrefix}类型的数据端口!`);
        return;
      }
    }
    
    // 通过所有检查，添加边
    setEdges((eds) => addEdge(params, eds));
  }, [setEdges]);

  const [isPromptOpen, setIsPromptOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [llmOptions, setLlmOptions] = useState([]);
  const [kbOptions, setKbOptions] = useState([]);
  const [negativePrompts, setNegativePrompts] = useState([]);
  const [positivePrompts, setPositivePrompts] = useState([]);
  const [tools, setTools] = useState([]);
  const [mcpServers, setMcpServers] = useState([]);
  const [mcpResources, setMcpResources] = useState([]);
  const [workflowTypes, setWorkflowTypes] = useState([]);
  const [isDebugMode, setIsDebugMode] = useState(false);
  const [examples, setExamples] = useState([]);
  const [currentExample, setCurrentExample] = useState({ 
    text: '', 
    conversable: false,
    attachments: [] 
  });
  const { user_info } = useSelector((state) => state.user);

  useEffect(() => {
    const fetchOptions = async () => {
      const [llmResult, kbResult, toolsResutl,promptsResult,mcpServerResult,mcpResourceResult] = await Promise.all([
        getLlmOptions(LLM_ENDPOINT, user_info.username),
        getLlmOptions(KNOWLEDGE_BASE_ENDPOINT, user_info.username),
        getLlmOptions(TOOL_ENDPOINT, user_info.username),
        getPromptOptions(user_info.username),
        getMcpServerOptions(MCP_SERVER_ENDPOINT, user_info.username,'tool'),
        getMcpServerOptions(MCP_SERVER_ENDPOINT, user_info.username,'resource'),
      ]);

      // 获取工作流类型
      const workflowTypesRes = await fetch(`${WORKFLOW_ENDPOINT}/workflow_types`);
      if (workflowTypesRes.ok) {
        const { data } = await workflowTypesRes.json();
        const workflowTypeOptions = data.map((type) => ({
          value: type.type,
          label: type.type,
          description: type.description
        }));
        setWorkflowTypes(workflowTypeOptions);
      }

      setLlmOptions(llmResult.map(llm => ({ value: llm.name, label: `${llm.name}` })));
      setKbOptions(kbResult.map(kb => ({ value: kb.name, label: kb.name })));
      setTools(toolsResutl.map(tool => ({value: tool.name, label: tool.name})));
      setMcpServers(mcpServerResult.map(mcp_server => ({value: mcp_server.name, label: mcp_server.name})));
      const positive = promptsResult.positivePrompts.map(p => ({ value: p.name, label: p.name,task_type: p.task_type }));
      const negative = promptsResult.negativePrompts.map(p => ({ value: p.name, label: p.name, task_type: p.task_type }));
      setPositivePrompts(positive);
      setNegativePrompts(negative);
      const mcpResources = mcpResourceResult.map(mcp => ({
        value: mcp.name,
        label: mcp.name
      }));
      setMcpResources(mcpResources)
    };

    fetchOptions();
  }, [user_info]);

  const nodeTypes = useMemo(() => getNodeTypes(llmOptions, kbOptions, positivePrompts, negativePrompts, isDebugMode,tools,mcpServers,mcpResources), 
    [llmOptions, kbOptions, positivePrompts, negativePrompts, tools, mcpServers,mcpResources,isDebugMode]);

  useEffect(() => {
    const startNode = {
      id: 'start-node',
      type: START_NODE_NAME,
      position: { x: 250, y: 5 },
      data: { 
        label: 'Start',
        llm_options: [],
        database_options: [],
        positive_prompt_options: [],
        negative_prompt_options: [],
        isDebugMode
      },
      deletable: false 
    };
    setNodes([startNode]);
  }, [llmOptions, kbOptions, positivePrompts, negativePrompts, tools,isDebugMode,setNodes,mcpServers,mcpResources]);

  const handleNodesChange = useCallback((changes) => {
    const filteredChanges = changes.filter(change => {
      if (change.type === 'remove' && change.id === 'start-node') {
        return false;
      }
      return true;
    });
    onNodesChange(filteredChanges);
  }, [onNodesChange]);

  const onAddNode = (newNode) => {
    setNodes((prevNodes) => [...prevNodes, newNode]);
  };

  const toggleDebugMode = () => {
    setIsDebugMode((prevMode) => !prevMode);
    setNodes((prevNodes) =>
      prevNodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isDebugMode: !isDebugMode,
        },
      }))
    );
  };

  const handleSave = () => {
    setIsPromptOpen(true);
  };

  const serializeLabel = (label) => {
    if (typeof label === 'object') {
      if (React.isValidElement(label)) {
        return React.Children.map(label.props.children, child =>
          typeof child === 'string' ? child : serializeLabel(child)
        ).join('');
      } else {
        return label.toString();
      }
    }
    return label;
  };

  const saveWorkflow = async (values) => {
    const serializedNodes = nodes.map((node) => ({
      ...node,
      data: {
        ...node.data,
        label: serializeLabel(node.data.label),
      },
    }));

    const serializedEdges = edges.map((edge) => ({
      ...edge,
      type: nodes.find((node) => node.id === edge.sourceHandle)?.data.selects[edge.sourceHandle],
    }));

    const workflowData = {
      ...values,
      nodes: serializedNodes,
      edges: serializedEdges,
      examples: examples,
    };

    try {
      setLoading(true);
      const workflowPayload = {
        ...workflowData,
        name: workflowData.workflow_name,
        nodes: JSON.stringify(workflowData.nodes),
        edges: JSON.stringify(workflowData.edges),
        database: Array.isArray(workflowData.database) ? workflowData.database.join(", ") : workflowData.database,
        description: workflowData.description,
        examples: workflowData.examples,
      };
      const endpoint_api = `${WORKFLOW_ENDPOINT}/`;
      console.log(workflowPayload,'workflowPayload');
      const response = await createData(endpoint_api, workflowPayload);

      const newWorkflow = {
        id: response.id,
        ...workflowData,
      };
      dispatch(updateSelctedWorkflow(newWorkflow));
      message.success('Workflow created successfully');
      form.resetFields();
      navigate(`${WORKFLOWS_NAME}`);
    } catch (error) {
      notification.error({
        message: '创建失败',
        description: 'Failed to create Workflow. Please try again.',
      });
    } finally {
      setLoading(false);
      setIsPromptOpen(false);
    }
  };

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: () => saveWorkflow(values),
    });
  };

  const addExample = () => {
    if (currentExample.text.trim() !== '') {
      setExamples([...examples, { 
        ...currentExample,
        attachments: currentExample.attachments || [] 
      }]);
      setCurrentExample({ 
        text: '', 
        conversable: false,
        attachments: [] 
      });
    }
  };

  const removeExample = (index) => {
    const newExamples = examples.filter((_, i) => i !== index);
    setExamples(newExamples);
  };

  return (
    <div className="page-container">
      <div className="header-container">
        <h1 className="page-title">创建新工作流</h1>
        <div className="button-group">
          <SidebarList />
          <button className="debug-button" onClick={toggleDebugMode}>
            {isDebugMode ? '关闭调试' : '运行调试'}
          </button>
          <Button className='save-workflow' icon={<SaveOutlined />} onClick={handleSave}>
            发布
          </Button>
        </div>
      </div>
      <div className="workflow">
        <ReactFlowProvider>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={handleNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={onInit}
            fitView
            attributionPosition="top-right"
            nodeTypes={nodeTypes}
          >
            <Controls />
            <Background color="#aaa" gap={16} />
            <AddNode 
              onAdd={onAddNode} 
              kb_name={kbOptions[0]?.label} 
              llm_name={llmOptions[0]?.label}
              positive_prompt={positivePrompts[0]?.label}
              negative_prompt={negativePrompts[0]?.label}
              tool_name={tools[0]?.label}
              mcp_server_name={mcpServers[0]?.label}
              mcp_resource_name={mcpResources[0]?.label}
              internet_search_enabled={true}
            />
          </ReactFlow>
        </ReactFlowProvider>
      </div>
      <Modal
        title="保存工作流"
        open={isPromptOpen}
        onCancel={() => setIsPromptOpen(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          <Form.Item
            name="workflow_name"
            label="工作流名称"
            rules={[{ required: true, message: '请输入工作流名称!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
            initialValue={isActive}
          >
            <Switch checked={isActive} onChange={(checked) => setIsActive(checked)} />
          </Form.Item>
          <Form.Item
            name="is_published"
            label="是否发布"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
          <Form.Item
            name="workflow_type"
            label="工作流类型"
            rules={[{ required: true, message: '请选择工作流类型!' }]}
          >
            <Select
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择工作流类型"
              options={workflowTypes}
              optionRender={(option) => (
                <div>
                  <div>{option.label}</div>
                  <Tooltip title={option.data.description}>
                    <div style={{ 
                      fontSize: 12, 
                      color: '#666',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: '200px'
                    }}>
                      {option.data.description}
                    </div>
                  </Tooltip>
                </div>
              )}
            />
          </Form.Item>
          <Form.Item label="示例">
            <Space direction="vertical" style={{ width: '100%' }}>
              {examples.map((example, index) => (
                <Space key={index} style={{ width: '100%', justifyContent: 'space-between' }}>
                  <div style={{padding: '8px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                    {index + 1}. {example.text} {example.conversable ? '(可对话)' : '(不可对话)'}
                  </div>
                  <Button onClick={() => removeExample(index)} icon={<DeleteOutlined />} danger>
                    删除
                  </Button>
                </Space>
              ))}
              <Space.Compact style={{ width: '100%' }}>
                <Input
                  value={currentExample.text}
                  onChange={(e) => setCurrentExample({ ...currentExample, text: e.target.value })}
                  onPressEnter={addExample}
                  placeholder="输入新的示例"
                  style={{ width: 'calc(100% - 90px)' }}
                />
                <Switch
                  checked={currentExample.conversable}
                  onChange={(checked) => setCurrentExample({ ...currentExample, conversable: checked })}
                  checkedChildren="可对话"
                  unCheckedChildren="不可对话"
                  style={{ marginRight: '10px' }}
                />
                <Button className="add-example-button" type="primary" icon={<PlusOutlined />} onClick={addExample}>
                  添加
                </Button>
              </Space.Compact>
              <div style={{ marginTop: 8 }}>
                <Button 
                  type="dashed" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    const newAttachment = { content: '', url: '', filename: '' };
                    setCurrentExample({
                      ...currentExample,
                      attachments: [...(currentExample.attachments || []), newAttachment]
                    });
                  }}
                >
                  添加附件
                </Button>
                {currentExample.attachments?.map((attachment, idx) => (
                  <div key={idx} style={{ marginTop: 8, padding: 8, border: '1px dashed #d9d9d9', borderRadius: 4 }}>
                    <Form.Item label="内容">
                      <Input
                        value={attachment.content}
                        onChange={(e) => {
                          const updatedAttachments = [...currentExample.attachments];
                          updatedAttachments[idx].content = e.target.value;
                          setCurrentExample({
                            ...currentExample,
                            attachments: updatedAttachments
                          });
                        }}
                        placeholder="附件内容"
                      />
                    </Form.Item>
                    <Form.Item label="URL">
                      <Space.Compact style={{ width: '100%' }}>
                        <Input
                          value={attachment.url}
                          onChange={(e) => {
                            const updatedAttachments = [...currentExample.attachments];
                            updatedAttachments[idx].url = e.target.value;
                            setCurrentExample({
                              ...currentExample,
                              attachments: updatedAttachments
                            });
                          }}
                          placeholder="附件URL"
                        />
                        <Upload.Dragger
                          showUploadList={false}
                          multiple={false}
                          accept="image/*"
                          beforeUpload={async (file) => {
                            try {
                              const url = await uploadImageToServer(URL.createObjectURL(file), file.name);
                              const updatedAttachments = [...currentExample.attachments];
                              updatedAttachments[idx].url = url;
                              updatedAttachments[idx].filename = file.name;
                              setCurrentExample({
                                ...currentExample,
                                attachments: updatedAttachments
                              });
                              return false;
                            } catch (error) {
                              return false;
                            }
                          }}
                          style={{ padding: '8px' }}
                        >
                          <p className="ant-upload-drag-icon">
                            <UploadOutlined />
                          </p>
                          <p className="ant-upload-text">点击或拖拽图片到此处上传</p>
                        </Upload.Dragger>
                      </Space.Compact>
                    </Form.Item>
                    <Form.Item label="文件名">
                      <Input
                        value={attachment.filename}
                        onChange={(e) => {
                          const updatedAttachments = [...currentExample.attachments];
                          updatedAttachments[idx].filename = e.target.value;
                          setCurrentExample({
                            ...currentExample,
                            attachments: updatedAttachments
                          });
                        }}
                        placeholder="附件文件名"
                      />
                    </Form.Item>
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        const updatedAttachments = currentExample.attachments.filter((_, i) => i !== idx);
                        setCurrentExample({
                          ...currentExample,
                          attachments: updatedAttachments
                        });
                      }}
                    >
                      删除附件
                    </Button>
                  </div>
                ))}
              </div>
            </Space>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建工作流
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CreateWorkflow;
