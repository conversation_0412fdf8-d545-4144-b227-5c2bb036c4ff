import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Button } from 'antd';
import { setSelectedWorkflowId } from '../../workflowSlice';
import { setSelectedAgentId } from '../Agents/AgentSlice';
import { setSelectedConversationId, setSelectedConversation, clearSelectedDocs } from '../../../Dashboard/dashboardSlice';
import { toast } from 'react-toastify';
import { setTab } from './tabSlice'; // 引入 setTab action
import 'react-toastify/dist/ReactToastify.css';
import './WorkflowSwitcher.css'; // 引入普通 CSS 文件

const WorkflowSwitcher = () => {
    const dispatch = useDispatch();
    const { selected_workflow_id, workflows } = useSelector((state) => state.workflow);
    const { selected_agent_id, agents } = useSelector((state) => state.agent);
    const { user_info } = useSelector((state) => state.user);
    const { isChat } = useSelector((state) => state.dashboard);
    const { currentTab: tab } = useSelector((state) => state.tab); // 获取当前的 Tab 状态

    // 使用 useMemo 来缓存 userWorkflows 和 userAgents
    const userWorkflows = useMemo(
        () => workflows.filter((workflow) => workflow.username === user_info.username),
        [workflows, user_info.username]
    );

    const userAgents = useMemo(
        () => agents.filter((agent) => agent.username === user_info.username),
        [agents, user_info.username]
    );

    const [is_open, setIsOpen] = useState(false);
    const navigate = useNavigate();
    const cardRefs = useRef([]);
    const switcherRef = useRef(null);

    const toggleDrawer = () => {
        if (isChat) {
            toast.warning('会话中，不能切换工作流', { position: 'top-center' });
            return;
        }
        if (userWorkflows.length === 0 && userAgents.length === 0) {
            toast.error('无可用工作流或Agent', { position: 'top-center' });
        } else {
            setIsOpen(!is_open);
        }
    };

    const handleSelectItem = (id) => {
        if (tab === 'workflows') {
            dispatch(setSelectedWorkflowId(id));
            dispatch(setSelectedAgentId(null));
        } else {
            dispatch(setSelectedAgentId(id));
            dispatch(setSelectedWorkflowId(null));
        }
        dispatch(setSelectedConversationId('new'));
        dispatch(setSelectedConversation({}));
        dispatch(clearSelectedDocs());
    };

    const handleNavigateToWorkflowConfig = (id) => {
        if (tab === 'workflows') {
            navigate(`/workflow/edit/${id}`);
        } else {
            navigate(`/agent/edit/${id}`);
        }
    };

    const handleTabChange = (newTab) => {
        dispatch(setTab(newTab)); // 使用 Redux 更新 Tab 状态
        if (newTab === 'workflows') {
            dispatch(setSelectedWorkflowId(userWorkflows[0]?.id || null));
            dispatch(setSelectedAgentId(null));
        } else {
            dispatch(setSelectedAgentId(userAgents[0]?.id || null));
            dispatch(setSelectedWorkflowId(null));
        }
        dispatch(setSelectedConversationId('new'));
        dispatch(setSelectedConversation({}));
        dispatch(clearSelectedDocs());
    };

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (switcherRef.current && !switcherRef.current.contains(e.target)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    useEffect(() => {
        const handleKeyDown = (e) => {
            if (!is_open) return;
            const currentItems = tab === 'workflows' ? userWorkflows : userAgents;
            const selectedId = tab === 'workflows' ? selected_workflow_id : selected_agent_id;

            let newIndex;
            const currentIndex = currentItems.findIndex((item) => item.id === selectedId);

            if (e.key === 'ArrowUp') {
                newIndex = currentIndex > 0 ? currentIndex - 1 : currentItems.length - 1;
            } else if (e.key === 'ArrowDown') {
                newIndex = currentIndex < currentItems.length - 1 ? currentIndex + 1 : 0;
            } else if (e.key === 'Escape') {
                setIsOpen(false);
                return;
            }

            if (newIndex !== undefined) {
                if (tab === 'workflows') {
                    dispatch(setSelectedWorkflowId(currentItems[newIndex].id));
                } else {
                    dispatch(setSelectedAgentId(currentItems[newIndex].id));
                }
                dispatch(setSelectedConversationId('new'));
                dispatch(setSelectedConversation({}));
                dispatch(clearSelectedDocs());
                if (cardRefs.current[newIndex]) {
                    cardRefs.current[newIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [is_open, tab, userWorkflows, userAgents, selected_workflow_id, selected_agent_id, dispatch]);

    return (
        <div ref={switcherRef} className={`workflow_drawer ${is_open ? 'open' : ''}`}>
            <div className={`workflow_header ${is_open ? '' : 'vertical-text'}`} onClick={toggleDrawer}>
                切换工作流
            </div>
            {is_open && (
                <div className="drawer_content">
                    <div className="drawer_tab_buttons">
                        <button
                            className={`drawer_tab_button ${tab === 'workflows' ? 'active' : ''}`}
                            onClick={() => handleTabChange('workflows')}
                        >
                            工作流
                        </button>
                        <button
                            className={`drawer_tab_button ${tab === 'agents' ? 'active' : ''}`}
                            onClick={() => handleTabChange('agents')}
                        >
                            智能体
                        </button>
                    </div>

                    {tab === 'workflows'
                        ? userWorkflows.map((workflow, index) => (
                              <Card
                                  key={workflow.id}
                                  title={workflow.name}
                                  className={`workflow_card ${workflow.id === selected_workflow_id ? 'selected' : ''}`}
                                  onClick={() => handleSelectItem(workflow.id)}
                                  extra={<Button onClick={() => handleNavigateToWorkflowConfig(workflow.id)}>编辑</Button>}
                                  ref={(el) => (cardRefs.current[index] = el)}
                              >
                                  <p>{workflow.description}</p>
                              </Card>
                          ))
                        : userAgents.map((agent, index) => (
                              <Card
                                  key={agent.id}
                                  title={agent.name}
                                  className={`workflow_card ${agent.id === selected_agent_id ? 'selected' : ''}`}
                                  onClick={() => handleSelectItem(agent.id)}
                                  extra={<Button onClick={() => handleNavigateToWorkflowConfig(agent.id)}>编辑</Button>}
                                  ref={(el) => (cardRefs.current[index] = el)}
                              >
                                  <p>{agent.description}</p>
                              </Card>
                          ))}
                </div>
            )}
        </div>
    );
};

export default WorkflowSwitcher;
