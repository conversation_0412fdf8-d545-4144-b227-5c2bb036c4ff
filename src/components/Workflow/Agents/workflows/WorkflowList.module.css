/* General Styles */
.formContainer {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .pageTitle {
    padding-top: 25px;
    font-size: 24px;
    font-weight: bold;
  }
  
  .buttonContainer {
    margin-bottom: 20px;
  }
  
  /* Header and Button Group */
  .headerContainerWorkflow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 10px;
  }
  
  .buttonGroup {
    display: flex;
    padding-top: 30px;
    align-items: center;
    justify-content: space-between;
    width: 280px;
  }
  
  /* Button Styles */
  .saveWorkflow {
    border: none;
    cursor: pointer;
    border-radius: 2px;
    font-size: 12px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-right: 125px;
    margin-top: 6px;
  }
  
  .debugButton {
    border: none;
    cursor: pointer;
    border-radius: 2px;
    font-size: 12px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-top: 6px;
  }
  
  .customButton,
  .logoutButton,
  .addExampleButton,
  .exampleDelete {
    border: none;
    cursor: pointer;
    border-radius: 2px;
    font-size: 12px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
  
  .customButton {
    background-color: #1890ff;
    color: white;
    padding: 8px 16px;
  }
  
  .customButtonDisabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
  }
  
  .debugButton,
  .logoutButton,
  .saveWorkflow {
    margin-left: 1px;
    width: 100px;
  }
  
  .debugButton {
    background-color: #126cf3b5;
    color: white;
  }
  
  .debugButtonHover {
    background-color: #e67e22;
  }
  
  .debugButtonActive {
    background-color: #d35400;
  }
  
  .saveWorkflow {
    background-color: #4CAF50;
    color: white;
  }
  
  .saveWorkflowHover {
    background-color: #45a049;
  }
  
  .saveWorkflowActive {
    background-color: #3e8e41;
  }
  
  .exampleDelete {
    width: 60px;
    padding: 0 8px;
  }
  
  /* Link Button */
  .linkButton {
    background: none;
    border: none;
    color: #1890ff;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
    font-size: inherit;
  }
  
  .linkButtonHover {
    color: #40a9ff;
  }
  
  /* Table Styles */
  .antTableWrapper {
    padding: 0 20px;
    overflow: auto;
  }
  
  .antTableCell {
    white-space: normal !important;
    word-wrap: break-word;
  }
  
  .selectedRow {
    background-color: #e6f7ff !important;
  }
  .cardContainer {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    padding: 0 20px;
  }
  
  .card {
    border: 1px solid #1890ff;
    border-radius: 8px;
    padding: 16px;
    width:200px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background-color: white;
    transition: transform 0.2s ease-in-out, border-color 0.2s ease-in-out;
  }
  
  .cardHover {
    transform: translateY(-5px);
    border-color: #40a9ff;
  }
  
  .cardAntCardHeadTitle {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .cardAntCardBodyP {
    margin: 0;
    padding: 4px 0;
  }
  
  .cardAntBtnLink {
    color: #1890ff;
    padding: 0;
    font-size: 14px;
  }
  
  .cardAntBtnLinkHover {
    color: #40a9ff;
  }
  
  .cardAntBtnLink,
  .cardCustomButton {
    margin-right: 8px;
  }
  
  .cardAntSwitch {
    margin-right: 8px;
  }
  
  .cardAntBtnDisabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  /* Example Item Styles */
  .exampleItem {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .exampleInput {
    flex-grow: 1;
    margin-right: 8px;
  }
  
  /* Ant Design Button Override */
  .antBtnPrimary {
    width: 100px;
    height: 32px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Add Example Button Override */
  .addExampleButtonAntBtnCssDevOnlyDoNotOverrideM4timiAntBtnPrimaryAntBtnCompactItemAntBtnCompactLastItem {
    background-color: #1890ff;
    color: white;
    padding: 0 15px;
    font-size: 14px;
    width: auto;
    transition: background-color 0.3s;
  }
  
  .addExampleButtonAntBtnCssDevOnlyDoNotOverrideM4timiAntBtnPrimaryAntBtnCompactItemAntBtnCompactLastItemHover {
    background-color: #40a9ff;
  }
  
  .addExampleButtonAntBtnCssDevOnlyDoNotOverrideM4timiAntBtnPrimaryAntBtnCompactItemAntBtnCompactLastItemActive {
    background-color: #096dd9;
  }