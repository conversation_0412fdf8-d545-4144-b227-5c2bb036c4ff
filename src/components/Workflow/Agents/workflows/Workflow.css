/* General Styles */
.form-container {
  padding: 20px;
}

.page-title {
  padding-top: 25px;
  font-size: 24px;
  font-weight: bold;
}

.button-container {
  margin-bottom: 20px;
}

/* Header and Button Group */
.header-container-workflow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 10px;
}

.button-group {
  display: flex;
  padding-top: 30px;
  align-items: center;
  justify-content: space-between;
  width: 280px;
}

/* Button Styles */
.save-workflow {
  border: none;
  cursor: pointer;
  border-radius: 2px;
  font-size: 12px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-right: 125px;
  margin-top: 6px;
}
.debug-button{
  border: none;
  cursor: pointer;
  border-radius: 2px;
  font-size: 12px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-top: 6px;
}

.custom-button,
.logout-button,
.add-example-button,
.example-delete {
  border: none;
  cursor: pointer;
  border-radius: 2px;
  font-size: 12px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.custom-button {
  background-color: #1890ff;
  color: white;
  padding: 8px 16px;
}

.custom-button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.debug-button,
.logout-button,
.save-workflow {
  margin-left: 1px;
  width: 100px;
}

.debug-button {
  background-color: #126cf3b5;
  color: white;
}

.debug-button:hover {
  background-color: #e67e22;
}

.debug-button:active {
  background-color: #d35400;
}

.save-workflow {
  background-color: #4CAF50;
  color: white;
}

.save-workflow:hover {
  background-color: #45a049;
}

.save-workflow:active {
  background-color: #3e8e41;
}

.example-delete {
  width: 60px;
  padding: 0 8px;
}

/* Link Button */
.link-button {
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: inherit;
}

.link-button:hover {
  color: #40a9ff;
}

/* Table Styles */
.ant-table-wrapper {
  padding: 0 20px;
  overflow: auto;
}

.ant-table-cell {
  white-space: normal !important;
  word-wrap: break-word;
}

.selected-row {
  background-color: #e6f7ff !important;
}

/* Card Styles */
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: space-between;
  padding: 0 20px;
}

.Card {
  width: calc(30.333% - 16px);
  border: 1px solid #1890ff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  margin-bottom: 16px;
  transition: transform 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.Card:hover {
  transform: translateY(-5px);
  border-color: #40a9ff;
}

.Card .ant-card-head-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.Card .ant-card-body p {
  margin: 0;
  padding: 4px 0;
}

.Card .ant-btn-link {
  color: #1890ff;
  padding: 0;
  font-size: 14px;
}

.Card .ant-btn-link:hover {
  color: #40a9ff;
}

.Card .ant-btn-link,
.Card .custom-button {
  margin-right: 8px;
}

.Card .ant-switch {
  margin-right: 8px;
}

.Card .ant-btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Example Item Styles */
.example-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.example-input {
  flex-grow: 1;
  margin-right: 8px;
}

/* Ant Design Button Override */
.ant-btn-primary {
  width: 100px;
  height: 32px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Add Example Button Override */
.add-example-button.ant-btn.css-dev-only-do-not-override-m4timi.ant-btn-primary.ant-btn-compact-item.ant-btn-compact-last-item {
  background-color: #1890ff;
  color: white;
  padding: 0 15px;
  font-size: 14px;
  width: auto;
  transition: background-color 0.3s;
}

.add-example-button.ant-btn.css-dev-only-do-not-override-m4timi.ant-btn-primary.ant-btn-compact-item.ant-btn-compact-last-item:hover {
  background-color: #40a9ff;
  /* width: 10px; */
}

.add-example-button.ant-btn.css-dev-only-do-not-override-m4timi.ant-btn-primary.ant-btn-compact-item.ant-btn-compact-last-item:active {
  background-color: #096dd9;
  /* width: 10px; */
}
