/* WorkflowSwitcher.module.css */
.workflow_drawer {
    position: fixed;
    top: 100px;
    right: 0;
    width: 250px;
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%);
}

.workflow_drawer.open {
    transform: translateX(0);
}

.workflow_header {
    background: #333;
    color: #fff;
    padding: 10px;
    cursor: pointer;
    text-align: center;
    border-radius: 4px;
}

.vertical-text {
    writing-mode: vertical-rl;
    text-orientation: upright;
}

.drawer_content {
    margin-top: 10px;
    padding: 10px;
}

.tabButtonsContainer {
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 10px;
}

.tabButton {
    border: 1px solid #dcdcdc;
    border-radius: 16px;
    padding: 5px 10px;
    cursor: pointer;
    color: #666;
    background-color: #f9f9f9;
    transition: background-color 0.3s, color 0.3s;
}

.tabButton.active {
    background-color: #e4e4e4;
    color: #000;
}

.tabButton:hover {
    background-color: #d3d3d3;
}

.workflow-card {
    margin: 10px 0;
    cursor: pointer;
}

.workflow-card.selected {
    border: 2px solid #1890ff;
    background-color: #e6f7ff;
}
