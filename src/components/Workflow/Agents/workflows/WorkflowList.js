import React, { useEffect, useState } from 'react';
import { Button, message, Switch, Tooltip, Select, Pagination } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector,useDispatch } from 'react-redux';
import { WORKFLOW_EDIT_NAME, WORKFLOW_NEW_NAME, WORKFLOW_ENDPOINT, SUPERUSER, ENTERPRISE, USER } from '../../../Configs/Config';
import { fetchBulk, deleteData, updateData, processWorkflows } from '../../../Routers/Router';
import { setWorkflows } from '../../workflowSlice';
import { Card } from '@chatui/core';
import SidebarList from '../../../SidebarList';
import './Workflow.css';

const { Option } = Select;

const WorkflowList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  // const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const sortField = 'updated_at';
  const sortOrder = 'desc';
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedWorkflowType, setSelectedWorkflowType] = useState('');
  const [workflowTypeOptions, setWorkflowTypeOptions] = useState([]);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user_info } = useSelector((state) => state.user);


  useEffect(() => {
    const fetchWorkflowTypes = async () => {
      try {
        const res = await fetch(`${WORKFLOW_ENDPOINT}/workflow_types`);
        if (res.ok) {
          const { data } = await res.json();
          setWorkflowTypeOptions(data.map(type => ({
            value: type.type,
            label: type.type,
            description: type.description
          })));
        }
      } catch (error) {
        console.error('Failed to fetch workflow types', error);
      }
    };

    fetchWorkflowTypes();
    getData(currentPage, pageSize);
  }, [sortField, sortOrder, currentPage, pageSize, selectedWorkflowType]);

  const getData = async ( page, pageSize) => {
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${WORKFLOW_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${user_info.username}`;
      if (selectedWorkflowType) {
        endpoint_api += `&workflow_type=${selectedWorkflowType}`;
      }
      const result = await fetchBulk(endpoint_api);
      console.log(result.data,'data')
      setData(result.data);
      setTotalItems(result.count);
      dispatch(setWorkflows(processWorkflows(result.data)));
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch workflow data');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    const endpoint_api = `${WORKFLOW_ENDPOINT}/${id}`;
    try {
      await deleteData(endpoint_api);
      getData(currentPage, pageSize);
      message.success('工作流删除成功');
    } catch (error) {
      message.error('删除工作流失败');
    }
  };

  const handleIsActiveChange = async (record, isActive,label) => {
    console.log(label,'labellabellabel');
    console.log(record,'record')
    const endpoint_api = `${WORKFLOW_ENDPOINT}/${record.id}?operator=${label}&owner=${record.username}`;
    const updatedRecord = { ...record, is_active: isActive };
    try {
      await updateData(endpoint_api, updatedRecord);
      getData( currentPage, pageSize);
      message.success('工作流状态更新成功');
    } catch (error) {
      message.error('更新工作流状态失败');
    }
  };
 const handleIsPublishedChange = async (record, isPublished,label) => {

    console.log(record,'record')
    const endpoint_api = `${WORKFLOW_ENDPOINT}/${record.id}?operator=${label}&owner=${record.username}`;
    const updatedRecord = { ...record, is_published: isPublished };
    try {
      await updateData(endpoint_api, updatedRecord);
      getData( currentPage, pageSize);
      message.success('工作流状态更新成功');
    } catch (error) {
      message.error('更新工作流状态失败');
    }
  };
  const handlePageChange = (page, newPageSize) => {
    setCurrentPage(page);
    setPageSize(newPageSize);
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">工作流列表</h1>
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          <Select
            placeholder="筛选工作流类型"
            style={{ width: 200 }}
            value={selectedWorkflowType}
            onChange={setSelectedWorkflowType}
            allowClear
          >
            {workflowTypeOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <SidebarList />
        </div>
      </div>
      <div className="button-container">
        <button
          className="custom-button"
          onClick={() => navigate(`${WORKFLOW_NEW_NAME}`)}
          // onClick={() => window.open(`${window.location.origin}${WORKFLOW_NEW_NAME}`)}
        >
          新增
        </button>
      </div>
      <div className="card-container">
        {data.map((record) => (
          <Card
            key={record.id}
            title={record.name}
            className="Card"
            style={{ marginBottom: '16px', cursor: 'pointer' }}
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click event propagation
              if (user_info.username===record.username){
                navigate(`${WORKFLOW_EDIT_NAME}/${record.id}`);
                // window.open(`${window.location.origin}${WORKFLOW_EDIT_NAME}/${record.id}`);
              }
            }}
          >
            <p>工作流名称: {record.name}</p>
            <Tooltip title={record.description}>
              <p>描述: {record.description}</p>
            </Tooltip>
            <p>创建者: {record.username}</p>
            <p>工作流类型: {record.workflow_type}</p>
            <p style={{ color: '#d9d9d9' }}>10K 复制</p>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '8px' }}>
              <Switch
                checked={record.is_active}
                onChange={(checked, event) => {
                  event.stopPropagation();
                  const label = checked
                    ? record.username === user_info.username
                      ? "激活"
                      : "复制"
                    : record.username === user_info.username
                    ? "未激活"
                    : "未复制";
                  handleIsActiveChange(record, checked, label);
                }}
                checkedChildren={record.username === user_info.username ? "激活" : "复制"}
                unCheckedChildren={record.username === user_info.username ? "未激活" : "未复制"}
                disabled={user_info.role === SUPERUSER && record.username !== user_info.username}
              />
              <Switch
                checked={record.is_published}
                onChange={(checked, event) => {
                  event.stopPropagation();
                  const label = checked ? "发布" : "未发布";
                  handleIsPublishedChange(record, checked, label);
                }}
                checkedChildren="发布"
                unCheckedChildren="未发布"
                disabled={record.username !== user_info.username}
              />
            </div>
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                handleDelete(record.id);
              }}
              disabled={record.username !== user_info.username}
            >
              删除
            </Button>
          </Card>
        ))}
      </div>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={totalItems}
        showSizeChanger
        onChange={handlePageChange}
        pageSizeOptions={['12', '24', '48', '96']}
        style={{ marginTop: '16px', textAlign: 'center' }}
      />
    </div>
  );
};

export default WorkflowList;
