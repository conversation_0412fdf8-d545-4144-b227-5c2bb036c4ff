// EditWorkflow.js
import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { SaveOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import {
  NEW_WORKFLOW,
  WORKFLOW_ENDPOINT,
  LLM_ENDPOINT,
  KNOWLEDGE_BASE_ENDPOINT,
  TOOL_ENDPOINT,
  MCP_SERVER_ENDPOINT,
  WORKFLOWS_NAME,
} from '../../../Configs/Config';
import SidebarList from '../../../SidebarList';
import ReactFlow, {
  addEdge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
} from 'reactflow';
import AddNode from '../../AddNode';
import {
  updateSelctedWorkflow as updateReduxWorkflow,
  setSelectedWorkflowId,
} from '../../workflowSlice';
import { nodes as initialNodes, edges as initialEdges } from '../../initial-elements';
import { Form, Input, Button, Modal, message, notification, Switch, Space, Select, Tooltip, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { uploadImageToServer } from './uploadUtils';
import 'reactflow/dist/style.css';
import './Workflow.css';
import { createData, updateData, fetchBulk } from '../../../Routers/Router';
import { getLlmOptions, getPromptOptions,getMcpServerOptions } from '../Utils/utils';
import { getNodeTypes } from './utils';


const onInit = (reactFlowInstance) => console.log('flow loaded:', reactFlowInstance);

const EditWorkflow = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { workflows } = useSelector((state) => state.workflow);
  const { user_info } = useSelector((state) => state.user);
  const username = user_info.username;
  const { id } = useParams();
  const selected_workflow_id = id; // 工作流 ID，从路由参数获取

  // 是否激活、debug、loading等状态
  const [isActive, setIsActive] = useState(true);
  const [isDebugMode, setIsDebugMode] = useState(false);
  const [loading, setLoading] = useState(false);

  // 大模型、知识库、正负向提示词
  const [llmOptions, setLlmOptions] = useState([]);
  const [kbOptions, setKbOptions] = useState([]);
  const [mcpServers, setMcpServers] = useState([]);
    const [mcpResources, setMcpResources] = useState([]);
  const [workflowTypes, setWorkflowTypes] = useState([]);
  const [negativePrompts, setNegativePrompts] = useState([]);
  const [positivePrompts, setPositivePrompts] = useState([]);

  // 工具列表（给ToolNode用）
  const [tools, setTools] = useState([]);
  const [toolsLoading, setToolsLoading] = useState(true);

  // 示例
  const [examples, setExamples] = useState([]);
  const [currentExample, setCurrentExample] = useState({ 
    text: '', 
    conversable: true,
    attachments: [] 
  });

  // 从 store 中获取选中的工作流
  const selectedWorkflow = useMemo(() => {
    return (
      workflows.find((wf) => wf.id === selected_workflow_id) || {
        nodes: initialNodes,
        edges: initialEdges,
      }
    );
  }, [workflows, selected_workflow_id]);

  // React Flow Hooks
  const [nodes, setNodes, onNodesChange] = useNodesState(selectedWorkflow.nodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(selectedWorkflow.edges);

  // 加载 tools
  useEffect(() => {
    const loadTools = async () => {
      try {
        const fetchedTools = await fetchBulk(`${TOOL_ENDPOINT}/bulk`);
        const toolsArray = Array.isArray(fetchedTools?.data)
          ? fetchedTools.data
          : Array.isArray(fetchedTools)
          ? fetchedTools
          : [];
        setTools(toolsArray);
      } catch (err) {
        console.error('Error loading tools:', err);
      } finally {
        setToolsLoading(false);
      }
    };
    loadTools();
  }, []);

  // 加载大模型、知识库、正负向提示词等
  useEffect(() => {
    const fetchData = async () => {
      try {
        // 加载 LLM 列表
        const resultLlmOptions = await getLlmOptions(LLM_ENDPOINT, username);
        const llmOps = resultLlmOptions.map((llm) => ({
          value: llm.name,
          label: llm.name,
        }));
        setLlmOptions(llmOps);
        // 加载 Tool 列表
        const toolsOptions = await getLlmOptions(TOOL_ENDPOINT, username);
        const toolsOps = toolsOptions.map((tool) => ({
          value: tool.name,
          label: tool.name,
        }));
        setTools(toolsOps);
        const mcpServerOptions = await getMcpServerOptions(MCP_SERVER_ENDPOINT, username,'tool');
        const mcpServerOpts = mcpServerOptions.map((mcp_server) => ({
          value: mcp_server.name,
          label: mcp_server.name,
        }));
        setMcpServers(mcpServerOpts);

        const mcpResourceOptions = await getMcpServerOptions(MCP_SERVER_ENDPOINT, username, 'resource');
        
        // 获取工作流类型
        const workflowTypesRes = await fetch(`${WORKFLOW_ENDPOINT}/workflow_types`);
        if (workflowTypesRes.ok) {
          const { data } = await workflowTypesRes.json();
          const workflowTypeOptions = data.map((type) => ({
            value: type.type,
            label: type.type,
            description: type.description
          }));
          setWorkflowTypes(workflowTypeOptions);
        }
    
        
        // 合并两个数组
        const mcpResourcesOpts = mcpResourceOptions.map(mcp => ({
          value: mcp.name,
          label: mcp.name
        }));
        
        setMcpResources(mcpResourcesOpts);
        // 加载知识库
        const resultKbOptions = await getLlmOptions(KNOWLEDGE_BASE_ENDPOINT, username);
        const kbOps = resultKbOptions.map((kb) => ({
          value: kb.name,
          label: kb.name,
        }));
        setKbOptions(kbOps);

        // 加载正向/负向提示词
        const promptOptions = await getPromptOptions(username);
        const positiveOptions = promptOptions.positivePrompts.map((p) => ({
          value: p.name,
          label: p.name,
          task_type: p.task_type,
        }));
        const negativeOptions = promptOptions.negativePrompts.map((p) => ({
          value: p.name,
          label: p.name,
          task_type: p.task_type,
        }));
        setPositivePrompts(positiveOptions);
        setNegativePrompts(negativeOptions);
        
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    };

    fetchData();
  }, [username]);

  // 存储当前选中的工作流 ID 到全局 store
  useEffect(() => {
    dispatch(setSelectedWorkflowId(selected_workflow_id));
  }, [selected_workflow_id, dispatch]);

  // 将 selectedWorkflow 中的信息赋值给表单 + nodes / edges
  useEffect(() => {
    if (selectedWorkflow) {
      setNodes(selectedWorkflow.nodes);
      setEdges(selectedWorkflow.edges);
      setExamples(selectedWorkflow.examples || []);
      form.setFieldsValue(selectedWorkflow);
    }
  }, [selectedWorkflow, setNodes, setEdges, form]);

  // 调试模式切换
  const toggleDebugMode = () => {
    setIsDebugMode((prevMode) => !prevMode);
    setNodes((prevNodes) =>
      prevNodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          isDebugMode: !isDebugMode,
        },
      }))
    );
  };
  const onConnect = useCallback((params) => {
    const restrictedPrefixes = ['attachment', 'query'];
    const sourceHandle = params.sourceHandle || '';
    const targetHandle = params.targetHandle || '';
    
    // 检查源或目标是否为受限的前缀类型
    const sourceRestricted = restrictedPrefixes.some(prefix => sourceHandle.startsWith(prefix));
    const targetRestricted = restrictedPrefixes.some(prefix => targetHandle.startsWith(prefix));
    
    // 如果源是受限类型，目标必须是相同的受限类型
    if (sourceRestricted) {
      const sourcePrefix = restrictedPrefixes.find(prefix => sourceHandle.startsWith(prefix));
      
      // 如果目标不是受限类型，或者是不同的受限类型，不允许连接
      if (!targetHandle.startsWith(sourcePrefix)) {
        message.error(`${sourcePrefix}类型数据只能传递到相同类型的数据端口!`);
        return;
      }
    }
    
    // 如果目标是受限类型，源必须是相同的受限类型
    if (targetRestricted) {
      const targetPrefix = restrictedPrefixes.find(prefix => targetHandle.startsWith(prefix));
      
      // 如果源不是受限类型，或者是不同的受限类型，不允许连接
      if (!sourceHandle.startsWith(targetPrefix)) {
        message.error(`只有${targetPrefix}类型数据才能传递到${targetPrefix}类型的数据端口!`);
        return;
      }
    }
    
    // 通过所有检查，添加边
    setEdges((eds) => addEdge(params, eds));
  }, [setEdges]);


  const handleEdgeUpdate = (oldEdge, newConnection) => {
    setEdges((els) =>
      els.map((e) => {
        if (e.id === oldEdge.id) {
          return { ...e, ...newConnection };
        }
        return e;
      })
    );
  };

  const [isPromptOpen, setIsPromptOpen] = useState(false);
  const handleSave = () => {
    setIsPromptOpen(true);
  };

  // 新增节点
  const onAddNode = (newNode) => {
    setNodes((prevNodes) => [...prevNodes, newNode]);
  };

  // 示例相关
  const addExample = () => {
    if (currentExample.text.trim() !== '') {
      setExamples([...examples, { 
        ...currentExample,
        attachments: currentExample.attachments || [] 
      }]);
      setCurrentExample({ 
        text: '', 
        conversable: true,
        attachments: [] 
      });
    }
  };
  const removeExample = (index) => {
    const newExamples = examples.filter((_, i) => i !== index);
    setExamples(newExamples);
  };

  // label若是ReactNode，需要序列化
  const serializeLabel = (label) => {
    if (typeof label === 'object') {
      if (React.isValidElement(label)) {
        return React.Children.map(label.props.children, (child) =>
          typeof child === 'string' ? child : serializeLabel(child)
        ).join('');
      } else {
        return label.toString();
      }
    }
    return label;
  };

  // 保存工作流
  const saveWorkflow = async (values) => {
    const serializedNodes = nodes.map((node) => ({
      ...node,
      data: {
        ...node.data,
        label: serializeLabel(node.data.label),
      },
    }));

    const serializedEdges = edges.map((edge) => ({
      ...edge,
    }));

    const update_workflow_data = {
      ...selectedWorkflow,
      ...values,
      nodes: serializedNodes,
      edges: serializedEdges,
      username: selectedWorkflow.username || username,
      examples,
    };

    try {
      let updatedWorkflow;
      const workflowPayload = {
        ...update_workflow_data,
        name: update_workflow_data.workflow_name,
        nodes: JSON.stringify(update_workflow_data.nodes),
        edges: JSON.stringify(update_workflow_data.edges),
        database: Array.isArray(update_workflow_data.database)
          ? update_workflow_data.database.join(', ')
          : update_workflow_data.database,
        description: update_workflow_data.description,
        examples: update_workflow_data.examples,
      };

      if (selected_workflow_id === NEW_WORKFLOW) {
        // 新建
        const endpoint_api = `${WORKFLOW_ENDPOINT}/`;
        updatedWorkflow = await createData(endpoint_api, workflowPayload);
      } else {
        // 更新
        const endpoint_api = `${WORKFLOW_ENDPOINT}/${selected_workflow_id}`;
        updatedWorkflow = await updateData(endpoint_api, workflowPayload);
      }

      if (!updatedWorkflow) {
        throw new Error('Failed to update workflow');
      }

      // 更新redux
      const reduxPayload = {
        ...update_workflow_data,
        id: updatedWorkflow.id,
      };
      dispatch(updateReduxWorkflow({ ...reduxPayload, selected_workflow_id }));
      dispatch(setSelectedWorkflowId(reduxPayload.id));

      // 返回工作流列表
      navigate(`${WORKFLOWS_NAME}`);
    } catch (error) {
      console.error('Failed to update/create workflow:', error);
      alert(`Error updating/creating workflow: ${error.message}`);
    }
  };

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          await saveWorkflow(values);
          message.success('工作流保存成功');
        } catch (error) {
          notification.error({
            message: '保存失败',
            description: 'Failed to save workflow. Please try again.',
          });
        } finally {
          setLoading(false);
          setIsPromptOpen(false);
        }
      },
    });
  };

  // 构建 nodeTypes
  // 将 tools 传给 getNodeTypes，并把 handleToolNodeReady 作为回调函数注入
  const nodeTypes = useMemo(
    () =>
      getNodeTypes(
        llmOptions,
        kbOptions,
        positivePrompts,
        negativePrompts,
        isDebugMode,
        tools,
        mcpServers,
        mcpResources
      ),
    [
      llmOptions,
      kbOptions,
      positivePrompts,
      negativePrompts,
      isDebugMode,
      tools,
      mcpServers,
      mcpResources
    ]
  );

  // 若没有取到 workflow
  if (!selectedWorkflow) {
    return <div>加载中...</div>;
  }

  return (
    <div className="workflow">
      <div className="header-container">
        <h1 className="page-title">编辑工作流</h1>
        <div className="button-group">
          <SidebarList />
          <button className="debug-button" onClick={toggleDebugMode}>
            {isDebugMode ? '关闭调试' : '运行调试'}
          </button>
          <Button className="save-workflow" icon={<SaveOutlined />} onClick={handleSave}>
            发布
          </Button>
        </div>
      </div>

      <ReactFlowProvider>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onEdgeUpdate={handleEdgeUpdate}
          onConnect={onConnect}
          onInit={onInit}
          fitView
          nodeTypes={nodeTypes}
          attributionPosition="top-right"
        >
          <Controls />
          <Background color="#aaa" gap={16} />
          {/* 示例：添加节点 */}
          <AddNode
            onAdd={onAddNode}
            kb_name={kbOptions[0]?.label}
            positive_prompt={positivePrompts[0]?.label}
            nagative_prompt={negativePrompts[0]?.label}
            internet_search_enabled={true}
          />
        </ReactFlow>
      </ReactFlowProvider>

      {/* 保存工作流弹窗 */}
      <Modal
        title="保存工作流"
        open={isPromptOpen}
        onCancel={() => setIsPromptOpen(false)}
        footer={null}
        destroyOnClose={false}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item
            name="workflow_name"
            label="工作流名称"
            rules={[{ required: true, message: '请输入工作流名称!' }]}
          >
            <Input disabled={selected_workflow_id !== NEW_WORKFLOW} />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
            initialValue={isActive}
          >
            <Switch checked={isActive} onChange={(checked) => setIsActive(checked)} />
          </Form.Item>
          <Form.Item
            name="is_published"
            label="是否发布"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="workflow_type"
            label="工作流类型"
            rules={[{ required: true, message: '请选择工作流类型!' }]}
          >
            <Select
              allowClear
              style={{ width: '100%' }}
              placeholder="请选择工作流类型"
              options={workflowTypes}
              optionRender={(option) => (
                <div>
                  <div>{option.label}</div>
                  <Tooltip title={option.data.description}>
                    <div style={{ 
                      fontSize: 12, 
                      color: '#666',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: '200px'
                    }}>
                      {option.data.description}
                    </div>
                  </Tooltip>
                </div>
              )}
            />
          </Form.Item>
          <Form.Item label="示例">
            <Space direction="vertical" style={{ width: '100%' }}>
              {examples.map((example, index) => (
                <Space key={index} style={{ width: '100%', justifyContent: 'space-between' }}>
                  <div
                    style={{ padding: '3px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}
                  >
                    {index + 1}. {example.text}{' '}
                    {example.conversable ? '(可对话)' : '(不可对话)'}
                  </div>
                  <Button onClick={() => removeExample(index)} icon={<DeleteOutlined />} danger>
                    删除
                  </Button>
                </Space>
              ))}
              <Space.Compact style={{ width: '100%' }}>
                <Input
                  value={currentExample.text}
                  onChange={(e) =>
                    setCurrentExample({ ...currentExample, text: e.target.value })
                  }
                  onPressEnter={addExample}
                  placeholder="输入新的示例"
                  style={{ width: 'calc(100% - 90px)' }}
                />
                <Switch
                  checked={currentExample.conversable}
                  onChange={(checked) =>
                    setCurrentExample({ ...currentExample, conversable: checked })
                  }
                  checkedChildren="可对话"
                  unCheckedChildren="不可对话"
                  style={{ marginRight: '10px' }}
                />
                <Button type="primary" icon={<PlusOutlined />} onClick={addExample}>
                  添加
                </Button>
              </Space.Compact>
              <div style={{ marginTop: 8 }}>
                <Button 
                  type="dashed" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    const newAttachment = { content: '', url: '', filename: '' };
                    setCurrentExample({
                      ...currentExample,
                      attachments: [...(currentExample.attachments || []), newAttachment]
                    });
                  }}
                >
                  添加附件
                </Button>
                {currentExample.attachments?.map((attachment, idx) => (
                  <div key={idx} style={{ marginTop: 8, padding: 8, border: '1px dashed #d9d9d9', borderRadius: 4 }}>
                    <Form.Item label="内容">
                      <Input
                        value={attachment.content}
                        onChange={(e) => {
                          const updatedAttachments = [...currentExample.attachments];
                          updatedAttachments[idx].content = e.target.value;
                          setCurrentExample({
                            ...currentExample,
                            attachments: updatedAttachments
                          });
                        }}
                        placeholder="附件内容"
                      />
                    </Form.Item>
                    <Form.Item label="URL">
                      <Space.Compact style={{ width: '100%' }}>
                        <Input
                          value={attachment.url}
                          onChange={(e) => {
                            const updatedAttachments = [...currentExample.attachments];
                            updatedAttachments[idx].url = e.target.value;
                            setCurrentExample({
                              ...currentExample,
                              attachments: updatedAttachments
                            });
                          }}
                          placeholder="附件URL"
                        />
                        <Upload.Dragger
                          showUploadList={false}
                          multiple={false}
                          accept="image/*"
                          beforeUpload={async (file) => {
                            try {
                              const url = await uploadImageToServer(URL.createObjectURL(file), file.name);
                              const updatedAttachments = [...currentExample.attachments];
                              updatedAttachments[idx].url = url;
                              updatedAttachments[idx].filename = file.name;
                              setCurrentExample({
                                ...currentExample,
                                attachments: updatedAttachments
                              });
                              return false;
                            } catch (error) {
                              return false;
                            }
                          }}
                          style={{ padding: '8px' }}
                        >
                          <p className="ant-upload-drag-icon">
                            <UploadOutlined />
                          </p>
                          <p className="ant-upload-text">点击或拖拽图片到此处上传</p>
                        </Upload.Dragger>
                      </Space.Compact>
                    </Form.Item>
                    <Form.Item label="文件名">
                      <Input
                        value={attachment.filename}
                        onChange={(e) => {
                          const updatedAttachments = [...currentExample.attachments];
                          updatedAttachments[idx].filename = e.target.value;
                          setCurrentExample({
                            ...currentExample,
                            attachments: updatedAttachments
                          });
                        }}
                        placeholder="附件文件名"
                      />
                    </Form.Item>
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        const updatedAttachments = currentExample.attachments.filter((_, i) => i !== idx);
                        setCurrentExample({
                          ...currentExample,
                          attachments: updatedAttachments
                        });
                      }}
                    >
                      删除附件
                    </Button>
                  </div>
                ))}
              </div>
            </Space>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存更改
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EditWorkflow;
