/* WorkflowSwitcher.css */

/* Workflow Switcher Base Styles */
.workflow-switcher {
    position: fixed;
    right: 2px;
    top: 1px;
}

/* Workflow Switcher Drawer Styles */
.workflow_drawer {
    position: fixed;
    right: 0px;
    top: 10%; /* Start the drawer slightly down from the top when closed */
    bottom: 10%; /* Ensure the drawer does not extend beyond the bottom of the viewport */
    width: 28px; /* Width when closed */
    z-index: 1000; /* Ensure it appears above other elements */
    height: 30px; /* Allow height to adjust with content */
    background-color: #fff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease, transform 0.3s ease; /* Smooth transitions for width and transform */
}

.workflow_drawer.open {
    transform: translateX(0);  /* Move into view when open */
    width: 450px; /* Width when open */
    top: 10px; /* Position the drawer 10px from the top of the viewport */
    z-index: 1005; /* Ensure it appears above other elements */
}

/* Workflow Drawer Header */
.workflow_header {
    padding: 10px;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease, background-color 0.3s ease;
    height: 10px;
}

.workflow_header.vertical-text {
    writing-mode: vertical-lr;  /* Vertical text orientation */
    justify-content: center;
    align-items: center;
    height: 95px;
    padding-left: 1px;
    padding-right: 1px;
    position: fixed; /* Ensure the header stays in a fixed position */
    right: 0px; /* Align the header 10px from the right edge */
    top: 10%; /* Adjust the top position as needed */
    width: 28px;
    font-size: 18px;
    font-weight: bold;
    background-color: #007bff;
    color: white;
    z-index: 1001;
}

/* Drawer Content Styles */
.drawer_content {
    padding: 10px;
    max-height: calc(92vh - 20%); /* Ensure the content fits within the viewport minus some padding */
    overflow-y: auto;
}

/* Tab Button Styles */
.drawer_tab_buttons {
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 10px;
}

.drawer_tab_button {
    border: 1px solid #dcdcdc;
    border-radius: 16px;
    padding: 5px 15px;
    cursor: pointer;
    color: #666;
    background-color: #f9f9f9;
    transition: background-color 0.3s, color 0.3s;
}

.drawer_tab_button.active {
    background-color: #e4e4e4;
    color: #000;
}

.drawer_tab_button:hover {
    background-color: #d3d3d3;
}

/* Workflow Card Styles */
.workflow_card {
    margin: 20px 0; /* Add spacing between cards */
    padding: 10px;
    border: 1px solid #ddd; /* Default border for cards */
    background-color: #f5f5f5;
    transition: border-color 0.3s ease-in-out;
    cursor: pointer;
}

/* Card Selected State */
.workflow_card.selected {
    border-color: #1890ff; /* Blue border for selected card */
    box-shadow: 0 0 8px rgba(24, 144, 255, 0.6); /* Add a blue shadow */
}
