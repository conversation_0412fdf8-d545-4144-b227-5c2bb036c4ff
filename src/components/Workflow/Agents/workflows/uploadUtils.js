import { message } from 'antd';
import {MANAGER_API_BASE_URL} from '../../../Configs/Config'


// 上传图片到服务器获取URL
export const uploadImageToServer = async (imageUrl, filename) => {
const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
  try {
    // 如果已经是HTTP URL，直接返回
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    
    // 从Blob URL获取图片数据
    const response = await fetch(imageUrl);
    const blob = await response.blob();

    // 创建FormData
    const formData = new FormData();
    formData.append('file', blob, filename);

    // 调用上传接口
    const UPLOAD_API = `${MANAGER_API_BASE_URL}/api/v1/file-managers/only_upload_file/`;

    const uploadResponse = await fetch(UPLOAD_API, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData,
    });

    if (!uploadResponse.ok) {
      throw new Error(`上传失败: ${uploadResponse.status}`);
    }

    const result = await uploadResponse.json();

    if (result.code === 200 && result.url) {
      return result.url;
    }
    throw new Error('上传接口返回格式不正确');
  } catch (error) {
    console.error('图片上传错误:', error);
    message.error('图片上传失败，请重试');
    throw error;
  }
};
