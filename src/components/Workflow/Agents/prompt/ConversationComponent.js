// import React, { useState, useEffect, useRef } from 'react';
// import { ToastContainer, toast } from 'react-toastify';
// import { useSelector } from 'react-redux';
// import 'react-toastify/dist/ReactToastify.css';
// import { Typography, Avatar, Space, Select } from 'antd';
// import styles from './ConversationComponent.module.css';
// import { v4 as uuid } from 'uuid';
// import { AGENT_LLM_REQUEST_ENDPOINT, USER, MANAGER_API_BASE_URL } from '../../../Configs/Config';
// import { fetchKbData } from '../../../utils/fetchKbData';

// const { Paragraph } = Typography;
// const { Option } = Select;

// const RECONNECT_INTERVAL = 5000; // 5 seconds

// const ConversationComponent = () => {
//   const [wsStatus, setWsStatus] = useState('disconnected');
//   const [messages, setMessages] = useState([]); // Storing all messages including user and assistant messages
//   const [content, setContent] = useState("");
//   const defaultName = '提示词生成器';
//   const { user_info } = useSelector((state) => state.user);
//   const { workflows } = useSelector((state) => state.workflow);
//   const userWorkflows = workflows.filter((workflow) => workflow.username === user_info.username);

//   const [selectedWorkflowId, setSelectedWorkflowId] = useState(() => {
//     const defaultWorkflow = userWorkflows.find(item => item.name === defaultName);
//     return defaultWorkflow ? defaultWorkflow.id : null;
//   });
//   const wsRef = useRef(null);
//   const inputRef = useRef(null);
//   const conversation_id = useRef(uuid()).current; // Assigning a unique ID for the conversation

//   const selected_workflow = selectedWorkflowId ? userWorkflows.find(item => item.id === selectedWorkflowId) : null;
//   const [nodes, setNodes] = useState([]); // Fetch and set your nodes data here
//   const username = user_info.username; // Replace with your actual user data

//   const connectWebSocket = () => {
//     if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
//       wsRef.current = new WebSocket(AGENT_LLM_REQUEST_ENDPOINT);

//       wsRef.current.onopen = () => {
//         console.log("WebSocket connection established.");
//         setWsStatus('connected');
//       };

//       wsRef.current.onerror = (error) => {
//         console.error("WebSocket error:", error);
//         setWsStatus('error');
//       };

//       wsRef.current.onclose = () => {
//         console.log("WebSocket connection closed.");
//         setWsStatus('disconnected');
//         // Attempt to reconnect after a delay
//         setTimeout(() => {
//           console.log("Attempting to reconnect to WebSocket...");
//           connectWebSocket();
//         }, RECONNECT_INTERVAL);
//       };
//     }
//   };

//   useEffect(() => {
//     connectWebSocket();
//     return () => {
//       if (wsRef.current) {
//         wsRef.current.close();
//         console.log("WebSocket connection closed because the component unmounted.");
//       }
//     };
//   }, []); // Empty dependency array ensures this runs only once when the component mounts

//   useEffect(() => {
//     if (selected_workflow) {
//       const fetchData = async () => {
//         try {
//           const kbData = await fetchKbData(selected_workflow);
//           setNodes(kbData); // Update nodes state with fetched data
//         } catch (error) {
//           console.error('Error fetching KB data:', error);
//           toast.error("无法获取工作流数据，请重试。");
//         }
//       };

//       fetchData();
//     } else {
//       setNodes([]); // Clear nodes if no workflow is selected
//     }
//   }, [selected_workflow]);

//   const processMessage = async (messageContent) => {
//     if (wsStatus !== 'connected') {
//       toast.error("正在连接服务器。请稍后重试。");
//       return;
//     }

//     const ai_message_id = uuid();
//     const userMessage = {
//       id: ai_message_id,
//       content: messageContent,
//       session_id: conversation_id,
//       user: username,
//       selected_workflow: selected_workflow,
//       workflow_name: selected_workflow?.name,
//       role: USER,
//       ai_message: false,
//     };

//     // Add the user's message to the messages state
//     setMessages(prevMessages => [...prevMessages, userMessage]);

//     const queryObject = {
//       id: ai_message_id,
//       content: messageContent,
//       session_id: conversation_id,
//       user: username,
//       selected_workflow: selected_workflow,
//       workflow_name: selected_workflow?.name,
//       role: USER,
//       nodes: nodes,
//       manager_api_base_url: MANAGER_API_BASE_URL,
//     };

//     // Send the query object to the WebSocket
//     wsRef.current.send(JSON.stringify(queryObject));

//     setContent(""); // Clear the input field

//     let accumulated_assistant_message = '';
//     const assistantMessageId = uuid(); // Unique ID for the assistant's message

//     // Add an initial empty assistant message to the state
//     setMessages(prevMessages => [...prevMessages, {
//       id: assistantMessageId,
//       content: '', // Initially empty content
//       session_id: conversation_id,
//       user: 'Assistant',
//       role: 'ASSISTANT',
//       ai_message: true,
//     }]);

//     return new Promise((resolve, reject) => {
//       const messageHandler = (event) => {
//         try {
//           const parse_data = JSON.parse(event.data);
//           const stream_data = parse_data.content[0].content;
//           accumulated_assistant_message += stream_data;

//           // Update the assistant message content in the state
//           setMessages(prevMessages =>
//             prevMessages.map(msg =>
//               msg.id === assistantMessageId ? { ...msg, content: accumulated_assistant_message } : msg
//             )
//           );

//           // If the message is completed, resolve the promise and remove the event listener
//           if (parse_data.is_completed) {
//             wsRef.current.removeEventListener('message', messageHandler);
//             resolve({ accumulated_assistant_message });
//           }
//         } catch (error) {
//           console.error("处理WebSocket消息时出错:", error);
//           wsRef.current.removeEventListener('message', messageHandler);
//           reject(error);
//         }
//       };

//       wsRef.current.addEventListener('message', messageHandler);
//     });
//   };

//   const handleSendMessage = async () => {
//     if (content.trim()) {
//       await processMessage(content);
//     }
//   };

//   const handleKeyPressed = (e) => {
//     if (e.key === 'Enter' && !e.shiftKey) {
//       e.preventDefault();
//       handleSendMessage();
//     }
//   };

//   const handleWorkflowChange = (value) => {
//     setSelectedWorkflowId(value);
//   };

//   const aiAvatarUrl = 'https://minio.aimed.cn/aigc-meeting/ai.png';
//   const userAvatarUrl = 'https://minio.aimed.cn/aigc-meeting/user.png';

//   return (
//     <div className={styles.conversationContainer}>
//       <div className={styles.selectContainer}>
//         <Select
//           value={selectedWorkflowId}
//           onChange={handleWorkflowChange}
//           className={styles.workflowSelect}
//           placeholder="选择工作流"
//         >
//           {userWorkflows.map((workflow) => (
//             <Option key={workflow.id} value={workflow.id}>
//               {workflow.name}
//             </Option>
//           ))}
//         </Select>
//       </div>
//       <div className={styles.messagesContainer}>
//         {messages.map((msg) => (
//           <div key={msg.id} className={styles.message}>
//             <Space align="start">
//               <Avatar src={msg.ai_message ? aiAvatarUrl : userAvatarUrl} />
//               <Paragraph
//                 style={{ margin: 0 }}
//                 ellipsis={false}
//                 copyable={false}
//                 editable={false}
//                 strong={false}
//               >
//                 {msg.content}
//               </Paragraph>
//             </Space>
//           </div>
//         ))}
//       </div>
//       <div className={styles.inputContainer}>
//         <textarea
//           ref={inputRef}
//           className={styles.input}
//           placeholder="描述场景生成提示词"
//           value={content}
//           onChange={(e) => setContent(e.target.value)}
//           onKeyDown={handleKeyPressed}
//         />
//         <button onClick={handleSendMessage} className={styles.sendButton}>
//           Send
//         </button>
//       </div>
//       <ToastContainer />
//     </div>
//   );
// };

// export default ConversationComponent;
