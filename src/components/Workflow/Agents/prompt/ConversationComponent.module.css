.conversationContainer {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100vh; /* Full height to ensure the container covers the viewport */
    padding: 0;
    margin: 0;
    background-color: #f5f5f5; /* Background color for the whole container */
}

.selectContainer {
    padding: 10px 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.workflowSelect {
    width: 200px;
    border-radius: 4px;
}

.messagesContainer {
    flex: 1;
    overflow-y: auto; /* Allow scrolling if messages overflow */
    padding: 20px;
    background-color: #ffffff; /* Background color for the message area */
}

.inputContainer {
    position: fixed;
    bottom: 0;
    right: 5px;
    width: 48%; /* Full width of the viewport */
    padding: 10px;
    border-radius: 20px;
    background-color: #ffffff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    z-index: 1000; /* Ensure it's on top of other content */
}

.input {
    flex: 1;
    padding: 15px;
    border-radius: 50px;
    border: 1px solid #ddd;
    box-sizing: border-box;
    margin-right: 10px;
    font-size: 16px;
    outline: none;
}

.sendButton {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 50px;
    cursor: pointer;
}

.sendButton:hover {
    background-color: #0056b3;
}
