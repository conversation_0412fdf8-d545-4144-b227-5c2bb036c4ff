

import { createSlice } from '@reduxjs/toolkit';

const loadPrompts = () => {
  try {
    const storedPrompts = localStorage.getItem('prompts');
    return storedPrompts ? JSON.parse(storedPrompts) : [];
  } catch (error) {
    console.error('Failed to load prompts from localStorage:', error);
    return [];
  }
};

const savePrompts = (prompts) => {
  try {
    localStorage.setItem('prompts', JSON.stringify(prompts));
  } catch (error) {
    console.error('Failed to save prompts to localStorage:', error);
  }
};

const initialState = {
  prompts: loadPrompts(),
};

export const PromptSlice = createSlice({
  name: 'prompt',
  initialState,
  reducers: {
    setPrompts: (state, action) => {
      state.prompts = action.payload;
      savePrompts(state.prompts);
    },
    addPrompt: (state, action) => {
      state.prompts.push(action.payload);
      savePrompts(state.prompts);
    },
    updatePrompt: (state, action) => {
      const index = state.prompts.findIndex(prompt => prompt.id === action.payload.id);
      if (index !== -1) {
        state.prompts[index] = action.payload;
        savePrompts(state.prompts);
      }
    },
    removePrompt: (state, action) => {
      state.prompts = state.prompts.filter(prompt => prompt.id !== action.payload);
      savePrompts(state.prompts);
    },
    clearPrompts: (state) => {
      state.prompts = [];
      savePrompts(state.prompts);
    },
  },
});

export const {
  setPrompts,
  addPrompt,
  updatePrompt,
  removePrompt,
  clearPrompts,
} = PromptSlice.actions;

export default PromptSlice.reducer;
