import React, { useState } from 'react';
import { Drawer, <PERSON><PERSON>, Card, Tooltip } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import styles from './SystemMessageCard.module.css'; // 使用CSS模块

const SystemMessageCard = ({ systemMessagesOptions }) => {
  const [isVisible, setIsVisible] = useState(false);  // 控制侧边栏是否显示

  // 复制到剪贴板功能
  const handleCopy = (message) => {
    navigator.clipboard.writeText(message).then(() => {
      alert('Copied to clipboard!');
    }).catch(() => {
      alert('Failed to copy!');
    });
  };

  // 展开侧边栏
  const showDrawer = () => {
    setIsVisible(true);
  };

  // 隐藏侧边栏
  const closeDrawer = () => {
    setIsVisible(false);
  };

  return (
    <>
      {/* 触发按钮，用于显示侧边栏 */}
      <Button 
        type="primary" 
        onClick={showDrawer}
        icon={<CopyOutlined />}
        className={styles.topRightButton} // 将按钮放置在右上角
      >
        系统消息
      </Button>

      {/* 侧边栏 */}
      <Drawer
        title="系统消息"
        placement="right"
        onClose={closeDrawer}
        open={isVisible}
        width={300}
      >
        {/* 渲染系统消息卡片 */}
        {systemMessagesOptions.map((msg, index) => (
          <div key={index} className={styles.messageContainer}>
            <Card
              className={styles.systemMessageCard}
              actions={[
                <Tooltip title="Copy to clipboard">
                  <Button icon={<CopyOutlined />} onClick={() => handleCopy(msg.value)} />
                </Tooltip>
              ]}
            >
              <p>{msg.value}</p>
            </Card>
          </div>
        ))}
      </Drawer>
    </>
  );
};

export default SystemMessageCard;
