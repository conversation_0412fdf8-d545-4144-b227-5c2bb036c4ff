import React, { useEffect, useState } from 'react';
import {  message, Select, Pagination, Tooltip, Switch, } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import SidebarList from '../../../SidebarList';
import {
  PROMPT_EDIT_NAME,
  PROMPT_NEW_NAME,
  PROMPT_ENDPOINT,
  SUPERUSER,
  ENTERPRISE,
  USER,
} from '../../../Configs/Config';
import { fetchBulk } from '../../../Routers/Router';

import {
  HeaderContainer,
  ButtonContainer,
  DeleteButton,
  handleDelete,
  handleBulkDelete,
  handleIsActiveChange,
} from '../Utils/Common';
import { Card } from '@chatui/core';
import './Prompt.css';

const { Option } = Select;

const PromptList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const sortField = 'updated_at';
  const sortOrder = 'desc';

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalItems, setTotalItems] = useState(0);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);

  useEffect(() => {
    getData( currentPage, pageSize);
  }, [sortField, sortOrder, currentPage, pageSize]);

  const getData = async ( page, pageSize) => {
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${PROMPT_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${user_info.username}`;

      const result = await fetchBulk(endpoint_api);
      console.log(result,'result')
      setData(result.data);
      setTotalItems(result.count);
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch prompt data');
    } finally {
      setLoading(false);
    }
  };
  const handlePageChange = (page, newPageSize) => {
    setCurrentPage(page);
    setPageSize(newPageSize);
    getData( page, newPageSize);
  };

  return (
    <div className="form-container">
      <HeaderContainer title="提示词列表" />
      <SidebarList />
      <ButtonContainer
        onNew={() => navigate(PROMPT_NEW_NAME)}
        onDelete={() => handleBulkDelete(selectedRowKeys, PROMPT_ENDPOINT, setData, setSelectedRowKeys)}
        newButtonText="新增"
        deleteButtonText="删除"
        isDeleteDisabled={selectedRowKeys.length === 0 || user_info.role !== SUPERUSER}
      />
      <div className="card-container">
        {data.map((record) => (
          <Card 
          key={record.id} 
          title={record.name} 
          className="Card"
          style={{ marginBottom: '16px', cursor: 'pointer' }}
          onClick={(e) => {
            e.stopPropagation(); // Prevent card click event propagation
            if (user_info.username===record.username){
              navigate(`${PROMPT_EDIT_NAME}/${record.id}`);
            }
          }}
          >
            <p>名称: {record.name}</p>
            <p>描述: {record.description}</p>
            <p>上下文: 
              <Tooltip title={record.context}>
                <span>{record.context?.length > 40 ? `${record.context.substring(0, 40)}...` : record.context}</span>
              </Tooltip>
            </p>
            <p>目标: 
              <Tooltip title={record.objective}>
                <span>{record.objective?.length > 40 ? `${record.objective.substring(0, 40)}...` : record.objective}</span>
              </Tooltip>
            </p>
            <p>风格: 
              <Tooltip title={record.style}>
                <span>{record.style?.length > 40 ? `${record.style.substring(0, 40)}...` : record.style}</span>
              </Tooltip>
            </p>
            <p>语气: 
              <Tooltip title={record.tone}>
                <span>{record.tone?.length > 40 ? `${record.tone.substring(0, 40)}...` : record.tone}</span>
              </Tooltip>
            </p>
            <p>受众: 
              <Tooltip title={record.audience}>
                <span>{record.audience?.length > 40 ? `${record.audience.substring(0, 40)}...` : record.audience}</span>
              </Tooltip>
            </p>
            <p>回复格式: 
              <Tooltip title={record.response_format}>
                <span>{record.response_format?.length > 40 ? `${record.response_format.substring(0, 40)}...` : record.response_format}</span>
              </Tooltip>
            </p>
            <p>任务类型: {record.task_type}</p>
            <p>提示词类型: {record.prompt_type}</p>
            {/* <p>Agent类型: {record.agent_type}</p> */}
            <p>创建者: {record.username}</p>
            <Switch
              checked={record.is_active}
              checkedChildren="发布"
              unCheckedChildren="未发布"
              onChange={(checked, event) => {
                event.stopPropagation(); // Stop event propagation here
                handleIsActiveChange(record, checked, PROMPT_ENDPOINT, setData);
              }}
              disabled={user_info.role === SUPERUSER && record.username !== user_info.username}
            />
            <DeleteButton
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(record.id, PROMPT_ENDPOINT, setData)
              }}
              disabled={record.username !== user_info.username}
            />
          </Card>
        ))}
      </div>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={totalItems}
        showSizeChanger
        onChange={handlePageChange}
        pageSizeOptions={['12', '24', '48', '96']}
        style={{ marginTop: '16px', textAlign: 'center' }}
      />
    </div>
  );
};

export default PromptList;
