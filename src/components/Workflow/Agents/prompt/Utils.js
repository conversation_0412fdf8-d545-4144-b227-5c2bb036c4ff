import { message } from 'antd';

/**
 * A utility function to generate a prompt description and populate form fields.
 * 
 * @param {Object} options - The options for the function.
 * @param {string} options.apiEndpoint - The API endpoint for creating the data.
 * @param {Object[]} options.prompts - The list of available prompts.
 * @param {string} options.selectedPrompt - The selected prompt nameD.
 * @param {Object} options.form - The form instance from Ant Design.
 * @param {function} options.createData - The function for making the API call.
 * @param {function} options.setLoading - The function to toggle loading state.
 * @param {boolean} [options.isEdit=false] - Whether this is an edit operation. If true, the name field is not changed.
 */
export const generatePromptDescription = async ({
  apiEndpoint,
  prompts,
  selectedPrompt,
  llm_model_name,
  form,
  createData,
  setLoading,
  isEdit = false, // Default isEdit to false
}) => {
  console.log(selectedPrompt,'selectedPrompt')
  if (!selectedPrompt) {
    message.error("Please select a prompt first");
    return;
  }
  console.log(selectedPrompt,'selectedPrompt111111111')
  const selectedPromptData = prompts.find((prompt) => prompt.name === selectedPrompt);
  // if (selectedPromptData) {
  try {
    setLoading(true); // Start the loading spinner

    // Get the prompt description entered in the form
    const formDescription = form.getFieldValue('description');
    
    // Make the API call
    console.log(apiEndpoint,)
    const response = await createData(apiEndpoint, {
      positive_prompt_data: selectedPromptData,
      content: formDescription, // Use the form's description as 'content',
      llm_model_name:llm_model_name,
    });

    // Assuming response.data has the structure provided
    const responseData = response.data;

    // Conditionally populate form fields based on isEdit
    const fieldsToUpdate = {
      context: responseData.context,
      objective: responseData.objective,
      style: responseData.style,
      tone: responseData.tone,
      audience: responseData.audience,
      response_format: responseData.response, // Assuming 'response' maps to 'response_format'
    };

    // If not in edit mode, also update the name
    if (!isEdit) {
      fieldsToUpdate.name = responseData.name;
    }

    // Populate form fields with response data
    form.setFieldsValue(fieldsToUpdate);

    message.success('Prompt generated successfully');
    
  } catch (error) {
    console.error('Error generating prompt:', error);
    message.error('Failed to generate prompt. Please try again.');
  } finally {
    setLoading(false); // Stop the loading spinner
  }
  // }
};
