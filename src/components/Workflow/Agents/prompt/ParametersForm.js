import React, { useState } from 'react';
import { Form, Input, Button, Select, Modal, Table, Tooltip, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import styles from './ParametersForm.module.css';

const { Option } = Select;

const ParametersForm = ({ value = [], onChange }) => {
  const [visible, setVisible] = useState(false);
  const [editingIndex, setEditingIndex] = useState(-1);
  const [paramForm] = Form.useForm();

  const paramTypes = [
    { label: '字符串', value: 'string' },
    { label: '布尔值', value: 'boolean' },
    { label: '数组', value: 'array' },
    { label: '数字', value: 'number' },
  ];

  const showModal = (index = -1) => {
    setEditingIndex(index);
    if (index >= 0) {
      const param = value[index];
      paramForm.setFieldsValue({
        name: param.name,
        description: param.description,
        type: param.type,
        required: param.required ? 'true' : 'false',
        default: param.default !== undefined ? JSON.stringify(param.default) : undefined
      });
    } else {
      paramForm.resetFields();
    }
    setVisible(true);
  };

  const handleOk = () => {
    paramForm.validateFields().then(values => {
      let defaultValue = undefined;
      if (values.default) {
        try {
          defaultValue = JSON.parse(values.default);
        } catch (e) {
          // If parsing fails, use the string value as is
          defaultValue = values.default;
        }
      }

      const newParam = {
        name: values.name,
        description: values.description,
        type: values.type,
        required: values.required === 'true',
      };

      if (defaultValue !== undefined) {
        newParam.default = defaultValue;
      }

      let newParameters = [...value];
      if (editingIndex >= 0) {
        newParameters[editingIndex] = newParam;
      } else {
        newParameters.push(newParam);
      }

      onChange(newParameters);
      setVisible(false);
      paramForm.resetFields();
    });
  };

  const handleDelete = (index) => {
    const newParameters = [...value];
    newParameters.splice(index, 1);
    onChange(newParameters);
  };

  const columns = [
    {
      title: '参数名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (description) => (
        <Tooltip placement="topLeft" title={description}>
          {description}
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeObj = paramTypes.find(t => t.value === type);
        return typeObj ? typeObj.label : type;
      },
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      key: 'required',
      render: (required) => (required ? '是' : '否'),
    },
    {
      title: '默认值',
      dataIndex: 'default',
      key: 'default',
      render: (defaultVal) => (
        defaultVal !== undefined ? JSON.stringify(defaultVal) : '-'
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record, index) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showModal(index)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(index)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.parametersForm}>
      <div className={styles.header}>
        <h3>提示词参数</h3>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => showModal()}
        >
          添加参数
        </Button>
      </div>

      <Table
        dataSource={value}
        columns={columns}
        rowKey={(record, index) => index}
        pagination={false}
        size="middle"
        className={styles.paramTable}
      />

      <Modal
        title={editingIndex >= 0 ? "编辑参数" : "添加参数"}
        open={visible}
        onOk={handleOk}
        onCancel={() => setVisible(false)}
        destroyOnClose
      >
        <Form
          form={paramForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="参数名"
            rules={[{ required: true, message: '请输入参数名!' }]}
          >
            <Input placeholder="如：query、max_tokens" />
          </Form.Item>
          <Form.Item
            name="description"
            label="参数描述"
            rules={[{ required: true, message: '请输入参数描述!' }]}
          >
            <Input.TextArea rows={3} placeholder="描述该参数的作用和使用方法" />
          </Form.Item>
          <Form.Item
            name="type"
            label="参数类型"
            rules={[{ required: true, message: '请选择参数类型!' }]}
            initialValue="string"
          >
            <Select>
              {paramTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="required"
            label="是否必填"
            rules={[{ required: true, message: '请选择是否必填!' }]}
            initialValue="true"
          >
            <Select>
              <Option value="true">是</Option>
              <Option value="false">否</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="default"
            label="默认值（可选，JSON格式）"
          >
            <Input.TextArea rows={2} placeholder="如数组: [], 字符串: &quot;默认文本&quot;, 数字: 10" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ParametersForm;