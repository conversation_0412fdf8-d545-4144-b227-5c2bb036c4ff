// import React from 'react';
// import CreatePrompt from './PromptCreate';
// import Messages from '../../../Dashboard/Chat/Messages';
// import NewMessageInput from '../../../Dashboard/Chat/NewMessageInput';

// import { useDynamicTheme,messageInputContainerStyleFunction } from '../../../Configs/StyleConfigs';


// const CombineConversationPrompt = () => {
//   const { background_color, text_color } = useDynamicTheme();
//   const width = '50%'
//   const messageInputContainerStyle = messageInputContainerStyleFunction(background_color, text_color,width)
//   return (
//     <div className="prompt-create-container" style={{ display: 'flex', width: '100%' }}>
//       <div style={{ width: '50%'}}>
//         <CreatePrompt />
//       </div>
//       <div style={messageInputContainerStyle} className='message-input-container'>
//         <Messages />
//         <NewMessageInput />
//       </div>
//     </div>
//   );
// };

// export default CombineConversationPrompt;
