import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Modal, notification, Spin, Switch, InputNumber, Row, Col, Select } from 'antd';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { fetchData, createData, updateData, fetchBulk } from '../../../Routers/Router';
import { PROMPT_ENDPOINT, PROMPTS_NAME, AGENT_ENDPOINT, LLM_CHAT_ENDPOINT, LLM_ENDPOINT } from '../../../Configs/Config';
import './Prompt.css'; // Import the CSS file
import { getConstantTypesOptions } from '../Utils/utils';
import { generatePromptDescription } from './Utils';
import SidebarList from '../../../SidebarList';
import LoadingSpinner from '../../../utils/LoadingSpinner'; // Import LoadingSpinner component
// import SystemMessageCard from './SystemMessageCard';
import { useSelector } from 'react-redux';
import ParametersForm from './ParametersForm';
const { Option } = Select;
const { TextArea } = Input;

const EditPrompt = () => {
  const [form] = Form.useForm();  // Initialize useForm and connect it to Form
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [taskTypeOptions, setTaskTypeOptions] = useState([]);
  const [promptData, setPromptData] = useState(null); // State to store fetched prompt data
  const [isActive, setIsActive] = useState(true);
  const { id } = useParams(); // Used to get the prompt ID from the URL
  const navigate = useNavigate();
  const location = useLocation();
  const promptName = 'co-star格式提示词生成器'; // Fixed prompt name to fetch specific prompt
  const [llmData, setLlmData] = useState([]); // State to store fetched prompt data
  const [selected_llm, setSelectedLlm] = useState(null); // State to store fetched prompt data
  const [cotTemplateOptions, setCotTemplateOptions] = useState([]);
  const { user_info } = useSelector((state) => state.user);
  
  // Fetch existing prompt data using the prompt ID
  useEffect(() => {
    const fetchPrompt = async () => {
      try {
        const endpoint_api = `${PROMPT_ENDPOINT}/${id}`;
        const data = await fetchData(endpoint_api);
        
        // Check if `cot`, `system_message`, `parameters`, and `output_parameters` are empty, and set default values if necessary
        const initialValues = {
          ...data, // Spread the existing data
          cot: data.cot || cotTemplateOptions[0]?.value || "Let's think step by step", // Set default for cot if it's empty
          system_message: data.system_message || "You are a helpful assistant", // Set default for system_message if it's empty
          parameters: data.parameters || [], // Ensure parameters exists, default to empty array
          output_parameters: data.output_parameters || [], // Ensure output_parameters exists, default to empty array
        };

        form.setFieldsValue(initialValues); // Populate the form with the updated values
        setIsActive(data.is_active); // Set isActive state based on fetched data
      } catch (error) {
        message.error('Failed to fetch prompt details');
      } finally {
        setInitialLoading(false);
      }
    };
    fetchPrompt();
  }, [id, form, cotTemplateOptions]);

  // Fetch task type options and prompt data by the fixed prompt name
  useEffect(() => {
    const fetchAgentTypeOptions = async () => {
      const task_endpoint_api = `${AGENT_ENDPOINT}/task_types`;
      const task_options = await getConstantTypesOptions(task_endpoint_api);
      setTaskTypeOptions(task_options);

      const cot_endpoint_api = `${PROMPT_ENDPOINT}/cots/`;
      const cot_options = await fetchData(cot_endpoint_api);
      setCotTemplateOptions(cot_options.data);
      
      // Fetch the specific prompt by its fixed name
      const prompt_endpoint = `${PROMPT_ENDPOINT}/query_by_name/${promptName}`;
      const prompt_result = await fetchData(prompt_endpoint); // Fetch the prompt data
      setPromptData(prompt_result);
      const endpoint_api = `${LLM_ENDPOINT}/bulk?username=${user_info.username}`;
      const result = await fetchBulk(endpoint_api);
      const llm_data = result.data;
      setLlmData(llm_data);
    };
    fetchAgentTypeOptions();
  }, [user_info.username]);

  // Function to generate prompt description using the fetched prompt data
  const handleGeneratePrompt = () => {
    if (promptData) {
      generatePromptDescription({
        apiEndpoint: LLM_CHAT_ENDPOINT,
        prompts: [promptData], // Directly pass the fetched prompt data
        selectedPrompt: promptName,
        form,
        llm_model_name: selected_llm,
        createData: createData, // Use updateData for editing
        setLoading,
        isEdit: true
      });
    } else {
      message.error('Prompt data not available.');
    }
  };

  // Function to handle form submission
  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${PROMPT_ENDPOINT}/${id}`;
          const response = await updateData(endpoint_api, values);
          if (response) {
            message.success('Prompt updated successfully');
            const from = location.state?.from || `${PROMPTS_NAME}`;
            navigate(from);
          }
        } catch (error) {
          notification.error({
            message: '更新失败',
            description: 'Failed to update prompt. Please try again.',
            duration: 5,
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  if (initialLoading) {
    return <Spin tip="Loading..." />;
  }

  return (
    <div className="page-container">
      {loading && <LoadingSpinner />}

      <div className="header-container">
        <h1 className="page-title">编辑提示词</h1>
        <SidebarList />
      </div>
      <div className="form-container">
        <Form
          form={form}  // Connect the form instance here
          layout="vertical"
          onFinish={onFinish}
        >
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="description"
                label="提示词描述"
                extra={
                  <>
                    描述任务场景，点击
                    <span style={{ fontWeight: 'bold', color: 'blue' }}>一键生成</span>自动生成提示词， 如：
                    <span style={{ fontWeight: 'bold', color: 'blue' }}>
                    我正在仔细阅读一篇重要的研究文章，试图解读作者的核心观点、研究方法和结论。我需要将这些文献的内容进行深入分析，并与现有的理论框架相结合，为我的研究课题提供支持和新的视角
                   </span>
                  </>
                }
                rules={[{ required: true, message: '请输入提示描述!' }]}
              >
                <TextArea rows={4} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={3} align="middle">
            <Col span={3}>
              <Form.Item
                name="llm_model"
                label="选择LLM模型"
                rules={[{ required: false, message: '请选择一个LLM模型!' }]}
              >
                <Select
                  value={selected_llm}
                  onChange={(value) => setSelectedLlm(value)}
                  placeholder="选择一个LLM模型"
                >
                  {llmData.map((llm) => (
                    <Option key={llm.id} value={llm.name}>
                      {llm.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={2}>
              <Button
                type="primary"
                onClick={handleGeneratePrompt}
                style={{ marginLeft: '10px', marginTop: '15px'}}
              >
                一键生成
              </Button>
            </Col>
          </Row>

          <Form.Item
            name="name"
            label="提示词名称"
            rules={[{ required: true, message: '请输入提示名称!' }]}
          >
            <Input disabled />
          </Form.Item>

          <Form.Item
            name="system_message"
            label="系统角色"
            rules={[{ required: true, message: '请输入系统角色!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="cot"
            label="链式思维模版"
            rules={[{ required: true, message: '请选择链式思维模版!' }]}
          >
            <Select>
              {cotTemplateOptions.map((cot_template) => (
                <Option key={cot_template.value} value={cot_template.value}>
                  {cot_template.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="context"
            label="上下文"
            rules={[{ required: true, message: '请输入上下文!' }]}
          >
            <TextArea rows={12} />
          </Form.Item>

          <Form.Item
            name="objective"
            label="目标"
            rules={[{ required: true, message: '请输入目标!' }]}
          >
            <TextArea rows={20} />
          </Form.Item>

          <Form.Item
            name="style"
            label="风格"
            rules={[{ required: false, message: '请输入风格!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="tone"
            label="语气"
            rules={[{ required: false, message: '请输入语气!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="audience"
            label="受众"
            rules={[{ required: false, message: '请输入受众!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="response_format"
            label="回复格式"
            rules={[{ required: false, message: '请输入回复格式!' }]}
          >
            <TextArea rows={6} />
          </Form.Item>

          {/* 参数字段 */}
          <Form.Item
            name="parameters"
            label="输入词参数"
            tooltip="添加提示词需要的输入参数，例如查询内容、大纲标题，大纲子标题等,这个参数来自于上一个llm 生成的结果，默认字段content"
          >
            <ParametersForm />
          </Form.Item>

          {/* 新增输出参数字段 */}
          <Form.Item
            name="output_parameters"
            label="输出参数"
            tooltip="添加提示词需要的输出格式参数，例如摘要、关键词等"
          >
            <ParametersForm />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
            initialValue={isActive}
          >
            <Switch checked={isActive} onChange={(checked) => setIsActive(checked)} />
          </Form.Item>

          <Form.Item
            name="task_type"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型!' }]}
          >
            <Select>
              {taskTypeOptions.map((task_type) => (
                <Option key={task_type.value} value={task_type.value}>
                  {task_type.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            shouldUpdate={(prevValues, currentValues) => prevValues.task_type !== currentValues.task_type}
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('task_type') === '反思' ? (
                <Form.Item
                  name="num_reflection"
                  label="反思次数"
                  initialValue={1}
                  rules={[{ required: true, message: '请输入反思次数' }]}
                >
                  <InputNumber min={0} max={10} step={1} />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            name="prompt_type"
            label="提示词类型"
            rules={[{ required: true, message: '请选择提示词类型!' }]}
            initialValue="正向提示词"
          >
            <Select>
              <Option value="负向提示词">负向提示词</Option>
              <Option value="正向提示词">正向提示词</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存更改
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default EditPrompt;