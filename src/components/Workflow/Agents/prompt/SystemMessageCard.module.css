/* 控制整个 SystemMessageCard 的布局，自动适应内容高度 */
.systemMessageCard {
    display: flex;
    align-items: center; /* 垂直居中 */
    overflow: hidden;
    padding: 5px;
    word-wrap: break-word; /* 允许长内容换行 */
    white-space: normal; /* 使得内容自动换行 */
    z-index: 1100;  /* 确保按钮在卡片上方 */
  }
  
  .messageContainer {
    margin-bottom: 10px; /* 给每个卡片一些间隔 */
  }
  
  .topRightButton {
    position: fixed;
    top: 60px;
    right: 20px;
    z-index: 1000;  /* 确保按钮在卡片上方 */
  }
  