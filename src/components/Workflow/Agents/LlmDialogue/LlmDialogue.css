/* LlmDialogue.css */

/* Container for the entire page */
.page-container {
    padding: 24px;
    background-color: #f7f7f7;
    min-height: 100vh;
  }
  
  /* Title styling */
  .page-title {
    font-size: 24px;
    font-weight: bold;
    text-align: center; /* Center align the title */
    margin-bottom: 24px;
  }
  
  /* Container for the form */
  .form-container {
    background-color: #fff;
    padding: 10px 10px 24px; /* Add padding to left and right */
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  /* Style for the form items */
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  /* Style for the submit button */
  .ant-btn-primary {
    width: 80px;
  }
  
  /* Style for the divider */
  .section-divider {
    margin: 24px 0;
  }
  
  /* Style for the select options */
  .ant-select-multiple .ant-select-selection-item {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #0050b3;
  }
  
  /* Style for the form labels */
  .ant-form-item-label > label {
    font-weight: 500;
  }
  
  /* Style for the input fields */
  .ant-input {
    border-radius: 4px;
  }
  
  /* Style for the input number fields */
  .ant-input-number {
    width: 100%;
  }
  
  /* Style for the modal confirm buttons */
  .ant-modal-confirm-btns {
    display: flex;
    justify-content: center;
  }
  
  .ant-modal-confirm-btns > .ant-btn {
    width: 120px;
    margin: 0 8px;
  }
  
  /* Custom style for the header container */
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 20px; /* Add padding to left and right */
  }
  
  /* Custom style for the button container */
  .button-container {
    display: flex;
    justify-content: flex-start; /* Align buttons to the left */
  }
  
  /* Custom style for the button */
  .custom-button {
    padding: 4px 12px;
    font-size: 14px;
    background-color: #1890ff;
    color: #fff;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .custom-button:hover {
    background-color: #40a9ff;
  }
  
  .custom-button:focus {
    background-color: #096dd9;
  }
  
  /* Custom style for the selected row */
  .selected-row,
  .hover-row:hover {
    background-color: #e6f7ff !important;
  }
  
  /* Custom style for the table wrapper */
.ant-table-wrapper {
  padding: 0 20px; /* Add padding to left and right */
  overflow: auto; /* Ensure the table wrapper can scroll if needed */
}
.ant-table-cell {
  white-space: normal !important; /* Allow line breaks in table cells */
  word-wrap: break-word; /* Break long words for better adaptability */
}
  
  .link-button {
    background: none;
    border: none;
    color: #1890ff;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
    font-size: inherit;
  }
  
  .link-button:hover {
    color: #40a9ff;
  }
  