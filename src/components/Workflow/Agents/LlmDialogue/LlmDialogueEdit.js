// import React, { useState, useEffect } from 'react';
// import { Form, Input, Button, message, Modal, notification, Spin } from 'antd';
// import { useNavigate, useParams } from 'react-router-dom';
// import { fetchData, updateData } from '../../../Routers/Router';
// import { LLM_DIALOGUE_ENDPOINT, LLM_DIALOGUES_NAME } from '../../../Configs/Config';
// // import { updateLlmDialogue as updateLlmDialogueInStore } from './LlmDialogueSlice';
// import './LlmDialogue.css'; // Import the CSS file
// import Logout from '../../../Login/Logout'; // Import the Logout component

// const EditLlmDialogue = () => {
//   const [form] = Form.useForm();
//   const [loading, setLoading] = useState(false);
//   const [initialLoading, setInitialLoading] = useState(true);
// //   const dispatch = useDispatch();
//   const navigate = useNavigate();
//   const { id } = useParams();

//   useEffect(() => {
//     const getLlmDialogue = async () => {
//       try {
//         const endpoint_api = `${LLM_DIALOGUE_ENDPOINT}/${id}`;
//         const data = await fetchData(endpoint_api);
//         form.setFieldsValue(data);
//       } catch (error) {
//         message.error('Failed to fetch dialogue details');
//       } finally {
//         setInitialLoading(false);
//       }
//     };

//     getLlmDialogue();
//   }, [id, form]);

//   const onFinish = (values) => {
//     Modal.confirm({
//       title: '确认保存',
//       content: '您确定要保存这些更改吗？',
//       onOk: async () => {
//         setLoading(true);
//         try {
//           const endpoint_api = `${LLM_DIALOGUE_ENDPOINT}/${id}`;
//           const response = await updateData(endpoint_api, values);
//           if (response) {
//             // dispatch(updateLlmDialogueInStore({ id, ...values }));
//             message.success('Dialogue updated successfully');
//             navigate(`${LLM_DIALOGUES_NAME}`);
//           }
//         } catch (error) {
//           notification.error({
//             message: '更新失败',
//             description: 'Failed to update dialogue. Please try again.',
//             onClose: () => console.log('Notification closed'),
//             duration: 5, // Set duration to 0 to keep it open until manually closed
//           });
//         } finally {
//           setLoading(false);
//         }
//       },
//     });
//   };

//   if (initialLoading) {
//     return <Spin tip="Loading..." />;
//   }

//   return (
//     <div className="page-container">
//       <div className="header-container">
//         <h1 className="page-title">编辑对话</h1>
//         <Logout /> {/* Add the Logout component */}
//       </div>
//       <div className="form-container">
//         <Form
//           form={form}
//           layout="vertical"
//           onFinish={onFinish}
//           onFinishFailed={(errorInfo) => {
//             console.log('Failed:', errorInfo);
//           }}
//         >
//           <Form.Item
//             name="session_id"
//             label="会话 ID"
//             rules={[{ required: true, message: '请输入会话 ID!' }]}
//           >
//             <Input disabled />
//           </Form.Item>
//           <Form.Item
//             name="llm_id"
//             label="大模型名"
//             rules={[{ required: true, message: '请输入大模型名!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item
//             name="workflow_name"
//             label="工作流名称"
//             rules={[{ required: true, message: '请输入工作流名称!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item
//             name="agent_name"
//             label="Agent 名称"
//             rules={[{ required: true, message: '请输入 Agent 名称!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item
//             name="query"
//             label="用户问题"
//             rules={[{ required: true, message: '请输入用户问题!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item
//             name="prompt_id"
//             label="对话提示词"
//             rules={[{ required: false, message: '请输入对话提示词!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item
//             name="answer"
//             label="模型输出"
//             rules={[{ required: true, message: '请输入模型输出!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item
//             name="input_token"
//             label="输入 token 数量"
//             rules={[{ required: true, message: '请输入输入 token 数量!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item
//             name="output_token"
//             label="输出 token 数量"
//             rules={[{ required: true, message: '请输入输出 token 数量!' }]}
//           >
//             <Input />
//           </Form.Item>
//           <Form.Item>
//             <Button type="primary" htmlType="submit" loading={loading}>
//               保存更改
//             </Button>
//           </Form.Item>
//         </Form>
//       </div>
//     </div>
//   );
// };

// export default EditLlmDialogue;
