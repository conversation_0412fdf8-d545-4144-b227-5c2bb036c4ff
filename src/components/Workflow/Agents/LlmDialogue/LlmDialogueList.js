import React, { useEffect, useState } from 'react';
import { Table, Button, message,Tooltip } from 'antd';
import { useSelector } from 'react-redux';
import {  LLM_DIALOGUE_ENDPOINT } from '../../../Configs/Config';
import { fetchBulk, deleteData, deleteBulk } from '../../../Routers/Router';
// import Logout from '../../../Login/Logout'; // Import the Logout component
import './LlmDialogue.css'; // Import the CSS file
import SidebarList from '../../../SidebarList';
const LlmDialogueList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const { user_info } = useSelector((state) => state.user);
  useEffect(() => {
    const getData = async () => {
      try {
        const endpoint_api = `${LLM_DIALOGUE_ENDPOINT}/bulk?username=${user_info.username}`;
        const result = await fetchBulk(endpoint_api);
        setData(result.data);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    getData();
  }, []);

  const handleDelete = async (id) => {
    const endpoint_api = `${LLM_DIALOGUE_ENDPOINT}/${id}`;
    try {
      await deleteData(endpoint_api);
      setData(data.filter((item) => item.id !== id));
      message.success('对话删除成功');
    } catch (error) {
      message.error('删除对话失败');
    }
  };

  const handleBulkDelete = async () => {
    try {
      const endpoint_api = `${LLM_DIALOGUE_ENDPOINT}/bulk`;
      await deleteBulk(endpoint_api, { llm_dialogue_ids: selectedRowKeys });
      setData(data.filter((item) => !selectedRowKeys.includes(item.id)));
      setSelectedRowKeys([]);
      message.success('选中的对话已删除');
    } catch (error) {
      message.error('删除选中的对话失败');
    }
  };

  const columns = [
    {
      title: '索引',
      dataIndex: 'index',
      key: 'index',
      render: (text, record, index) => index + 1,
    },
    {
      title: '会话 ID',
      dataIndex: 'session_id',
      key: 'session_id',
    },
    {
      title: '大模型名',
      dataIndex: 'llm_id',
      key: 'llm_name',
    },
    {
      title: '知识库名称',
      dataIndex: 'kb_id',
      key: 'kb_id',
    },
    {
      title: '工作流名称',
      dataIndex: 'workflow_name',
      key: 'workflow_name',
    },
    {
      title: 'Agent 名称',
      dataIndex: 'agent_name',
      key: 'agent_name',
    },
    {
      title: '用户问题',
      dataIndex: 'query',
      key: 'query',
    },
    {
      title: '对话提示词',
      dataIndex: 'prompt_id',
      key: 'prompt_id',
      // render: (text, record) => (
      //   <button
      //     onClick={() => navigate(`${LLM_DIALOGUE_EDIT_NAME}/${record.id}`)}
      //     className="link-button"
      //     disabled={user_info.role !== 'admin'}
      //   >
      //     {text}
      //   </button>
      // ),
    },
    {
      title: '模型输出',
      dataIndex: 'answer',
      key: 'answer',
      render: (text) => (
        <Tooltip title={text}>
          <span>{text.length > 40 ? `${text.substring(0, 40)}...` : text}</span>
        </Tooltip>
      ),      
    },
    {
      title: '输入 token 数量',
      dataIndex: 'input_token',
      key: 'input_token',
    },
    {
      title: '输出 token 数量',
      dataIndex: 'output_token',
      key: 'output_token',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
    },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => (
        <Button type="link" onClick={() => handleDelete(record.id)}>
          删除
        </Button>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys),
    getCheckboxProps: (record) => ({
      className: selectedRowKeys.includes(record.id) ? 'selected-row' : '',
    }),
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">对话列表</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        {/* <button className="custom-button" onClick={() => navigate(`${LLM_DIALOGUE_NEW_NAME}`)}>
          新增
        </button> */}
        <button
          className="custom-button"
          style={{ marginLeft: 8 }}
          onClick={handleBulkDelete}
          disabled={selectedRowKeys.length === 0 || user_info.role !== 'admin'}
        >
          删除
        </button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        rowClassName={(record) => (selectedRowKeys.includes(record.id) ? 'selected-row' : '')}
      />
    </div>
  );
};

export default LlmDialogueList;
