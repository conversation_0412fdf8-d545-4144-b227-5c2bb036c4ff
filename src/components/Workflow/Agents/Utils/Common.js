import React from 'react';
import { But<PERSON>, Switch, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { deleteBulk,deleteData, updateData } from '../../../Routers/Router';

// import Logout from '../../../Login/Logout'; // Import the Logout component
export const HeaderContainer = ({ title }) => (
  <div className="header-container">
    <h1 className="page-title">{title}</h1>
    {/* <Logout /> */}
  </div>
);

export const ButtonContainer = ({ onNew, onDelete, newButtonText, deleteButtonText, isDeleteDisabled }) => (
  <div className="button-container">
    <button className="custom-button" onClick={onNew}>
      {newButtonText}
    </button>
    {/* <button
      className="custom-button delete-button"
      onClick={onDelete}
      disabled={isDeleteDisabled}
    >
      {deleteButtonText}
    </button> */}
  </div>
);

export const LinkButton = ({ to, children, disabled = false }) => {
  const navigate = useNavigate();
  return (
    <button
      onClick={() => navigate(to)}
      className={`link-button ${disabled ? 'disabled' : ''}`}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export const ActiveSwitch = ({ checked, onChange, disabled = false }) => (
  <Switch
    checked={checked}
    onChange={onChange}
    disabled={disabled}
    className={disabled ? 'switch-grey' : ''}
  />
);

export const DeleteButton = ({ onClick, disabled = false }) => (
  <Button
    type="link"
    onClick={onClick}
    disabled={disabled}
    style={{ opacity: disabled ? 0.4 : 1 }}
  >
    删除
  </Button>
);

export const handleDelete = async (id, endpoint, setData, successMessage = '删除成功', errorMessage = '删除失败') => {
  try {
    await deleteData(`${endpoint}/${id}`);
    setData(prevData => prevData.filter(item => item.id !== id));
    message.success(successMessage);
  } catch (error) {
    message.error(errorMessage);
  }
};

export const handleBulkDelete = async (selectedKeys, endpoint, setData, setSelectedKeys, successMessage = '批量删除成功', errorMessage = '批量删除失败') => {
  try {
    await deleteBulk(`${endpoint}/bulk`, { ids: selectedKeys });
    setData(prevData => prevData.filter(item => !selectedKeys.includes(item.id)));
    setSelectedKeys([]);
    message.success(successMessage);
  } catch (error) {
    message.error(errorMessage);
  }
};

export const handleIsActiveChange = async (record, isActive, endpoint, setData, successMessage = '状态更新成功', errorMessage = '状态更新失败') => {
  const updatedRecord = { ...record, is_active: isActive };
  try {
    const update_data = await updateData(`${endpoint}/${record.id}`, updatedRecord);
    setData(prevData => prevData.map(item =>
      item.id === record.id ? { ...update_data, is_active: isActive } : item
    ));
    message.success(successMessage);
  } catch (error) {
    message.error(errorMessage);
  }
};