import React, { useEffect, useState } from 'react';
import { Button, message, Switch, Select, Pagination } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  LLM_EDIT_NAME,
  LLM_NEW_NAME,
  LLM_ENDPOINT,
  SUPERUSER,
  ENTERPRISE,
  USER,
} from '../../../Configs/Config';
import { fetchBulk, deleteData, deleteBulk, updateData } from '../../../Routers/Router';
import { Card } from '@chatui/core';
import './LLM.css';
import SidebarList from '../../../SidebarList';
const { Option } = Select;

const LLMList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [sortField, setSortField] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalItems, setTotalItems] = useState(0);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  // const [roleFilter, setRoleFilter] = useState('all');

  useEffect(() => {
    getData( currentPage, pageSize);
  }, [sortField, sortOrder, currentPage, pageSize]);
  
  const handlePageChange = (page, newPageSize) => {
    setCurrentPage(page);
    setPageSize(newPageSize);
    getData( page, newPageSize);
  };
  const getData = async ( page, pageSize) => {
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${LLM_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${user_info.username}`;
    

      const result = await fetchBulk(endpoint_api);
      setData(result.data);
      setTotalItems(result.count);
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch LLM data');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    const endpoint_api = `${LLM_ENDPOINT}/${id}`;
    try {
      await deleteData(endpoint_api);
      setData(data.filter((item) => item.id !== id));
      message.success('大模型删除成功');
    } catch (error) {
      message.error('删除大模型失败');
    }
  };

  const handleIsActiveChange = async (record, isActive) => {
    const endpoint_api = `${LLM_ENDPOINT}/${record.id}`;
    const updatedRecord = { ...record, is_active: isActive };
    try {
      const update_data = await updateData(endpoint_api, updatedRecord);
      setData(data.map(item =>
        item.id === record.id ? { ...update_data, is_active: isActive } : item
      ));
      message.success('大模型状态更新成功');
    } catch (error) {
      message.error('更新大模型状态失败');
    }
  };

  const handleBulkDelete = async () => {
    try {
      const endpoint_api = `${LLM_ENDPOINT}/bulk`;
      await deleteBulk(endpoint_api, { llm_ids: selectedRowKeys });
      setData(data.filter((item) => !selectedRowKeys.includes(item.id)));
      setSelectedRowKeys([]);
      message.success('选中的大模型已删除');
    } catch (error) {
      message.error('删除选中的大模型失败');
    }
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">LLM配置</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        <button
          className="custom-button"
          onClick={() => navigate(`${LLM_NEW_NAME}`)}
        >
          新增
        </button>
      </div>
      <div className="card-container">
        {data.map((record) => (
          <Card 
          key={record.id} 
          title={record.name} 
          className="Card"
          style={{ marginBottom: '16px', cursor: 'pointer' }}
          onClick={(e) => {
            e.stopPropagation(); // Prevent card click event propagation
            if (user_info.username===record.username){
              navigate(`${LLM_EDIT_NAME}/${record.id}`);
            }
          }}
          >
            {/* <p>模型服务地址: {record.server_url}</p> */}
            <p>模型名称: {record.name}</p>
            <p>企业: {record.enterprise}</p>
            <p>描述: {record.description}</p>
            <p>输入价格: ¥{record.input_price}/1000 tokens</p>
            <p>输出价格: ¥{record.output_price}/1000 tokens</p>
            <p>最大长度: {record.max_length}</p>
            <p>创建者: {record.username}</p>
            <Switch
              checked={record.is_active}
              onChange={(checked,event) => {
                event.stopPropagation(); // Stop event propagation here
                handleIsActiveChange(record, checked);
              }}
              checkedChildren="发布"
              unCheckedChildren="未发布"
              disabled={record.username !== user_info.username}
            />
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                handleDelete(record.id);
              }}
              disabled={record.username !== user_info.username}
            >
              删除
            </Button>
          </Card>
        ))}
      </div>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={totalItems}
        showSizeChanger
        onChange={handlePageChange}
        pageSizeOptions={['12', '24', '48', '96']}
        style={{ marginTop: '16px', textAlign: 'center' }}
      />
    </div>
  );
};

export default LLMList;
