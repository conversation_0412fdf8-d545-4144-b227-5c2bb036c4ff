import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, message, Modal, notification, Spin, InputNumber, Popover, Switch } from 'antd';
import { SketchPicker } from 'react-color';
import { useNavigate, useParams } from 'react-router-dom';
import { fetchData, updateData } from '../../../Routers/Router';
import { LLM_ENDPOINT, LLMS_NAME } from '../../../Configs/Config';

import './LLM.css'; // Import the CSS file
import SidebarList from '../../../SidebarList';
const { Option } = Select;

const EditLLM = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [color, setColor] = useState('#0056b3');
  const [isActive, setIsActive] = useState(true);
  const [doSample, setDoSample] = useState(false);
  const [isReasoning, setIsReasoning] = useState(false); // New state for reasoning models
  const [isMultimodal, setIsMultimodal] = useState(false); // New state for multimodal models
  const [isPublished, setIsPublished] = useState(true); // Added for completeness
  const navigate = useNavigate();
  const { id } = useParams();


  useEffect(() => {
    const getLLM = async () => {
      try {
        const endpoint_api = `${LLM_ENDPOINT}/${id}`;
        const data = await fetchData(endpoint_api);
        form.setFieldsValue(data);
        setColor(data.color || '#0056b3');
        setIsActive(data.is_active);
        setDoSample(data.do_sample || false);
        // Set the new state variables based on fetched data
        setIsReasoning(data.is_reasoning || false);
        setIsMultimodal(data.is_multimodal || false);
        setIsPublished(data.is_published !== undefined ? data.is_published : true);
      } catch (error) {
        message.error('Failed to fetch LLM details');
      } finally {
        setInitialLoading(false);
      }
    };

    getLLM();
  }, [id, form]);

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${LLM_ENDPOINT}/${id}`;
          // Include the new fields in the update payload
          const response = await updateData(endpoint_api, { 
            ...values, 
            color, 
            is_active: isActive,
            do_sample: doSample,
            is_reasoning: isReasoning,
            is_multimodal: isMultimodal,
            is_published: isPublished
          });
          if (response) {
            // dispatch(updateLLMInStore({ id, ...values, color, is_active: isActive }));
            message.success('LLM updated successfully');
            navigate(`${LLMS_NAME}`);
          }
        } catch (error) {
          notification.error({
            message: '更新失败',
            description: 'Failed to update LLM. Please try again.',
            onClose: () => console.log('Notification closed'),
            duration: 5, // Set duration to 0 to keep it open until manually closed
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleColorChange = (color) => {
    setColor(color.hex);
  };

  const colorPickerContent = (
    <SketchPicker color={color} onChangeComplete={handleColorChange} />
  );

  if (initialLoading) {
    return <Spin tip="Loading..." />;
  }

  return (
    <div className="page-container">
      <div className="header-container">
        <h1 className="page-title">LLM配置</h1>
        <SidebarList /> {/* Add the Logout component */}
      </div>
      <div className="form-container">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={(errorInfo) => {
            console.log('Failed:', errorInfo);
          }}
        >
          <Form.Item
            name="name"
            label="模型名"
            rules={[{ required: true, message: '请输入模型名!' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="server_url"
            label="模型服务地址"
            rules={[{ required: true, message: '请输入模型服务地址!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="api_key"
            label="模型密钥"
            rules={[{ required: true, message: '请输入模型密钥!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="enterprise"
            label="企业"
            rules={[{ required: false, message: '请输入企业名称!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="大模型描述"
            rules={[{ required: false, message: '请输入大模型描述!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="input_price"
            label="大模型输入价格(元/1000token)"
            rules={[{ required: true, message: '请输入大模型输入价格!' }]}
          >
            <InputNumber min={0} step={0.001} />
          </Form.Item>
          <Form.Item
            name="output_price"
            label="大模型输出价格(元/1000token)"
            rules={[{ required: true, message: '请输入大模型输出价格' }]}
          >
            <InputNumber min={0} step={0.001} />
          </Form.Item>
          <Form.Item
            name="max_length"
            label="模型最大长度"
            initialValue={16000}
            rules={[{ required: true, message: '请输入模型最大长度' }]}
          >
            <InputNumber min={0} max={32000} step={100} />
          </Form.Item>
          <Form.Item
            name="top_k"
            label="模型top_k"
            initialValue={50}
            rules={[{ required: true, message: '请输入模型top_k' }]}
          >
            <InputNumber min={0} max={100} step={1} />
          </Form.Item>
          <Form.Item
            name="temperature"
            label="模型温度"
            initialValue={1.0}
            rules={[{ required: true, message: '模型温度' }]}
          >
            <InputNumber min={0} max={1} step={0.1} />
          </Form.Item>
          <Form.Item
            name="top_p"
            label="模型top_p"
            initialValue={0.9}
            rules={[{ required: true, message: '模型top_p' }]}
          >
            <InputNumber min={0} max={1} step={0.1} />
          </Form.Item>
          <Form.Item
            name="do_sample"
            label="是否进行采样"
            valuePropName="checked"
            initialValue={doSample}
          >
            <Switch checked={doSample} onChange={(checked) => setDoSample(checked)} />
          </Form.Item>
          <Form.Item
            name="labels"
            label="大模型标签"
            initialValue={['医疗']}
          >
            <Select mode="multiple">
              <Option value="医疗">医疗</Option>
              <Option value="金融">金融</Option>
              <Option value="教育">教育</Option>
              <Option value="代码">代码</Option>
              <Option value="聊天">聊天</Option>
              <Option value="通用">通用</Option>
            </Select>
          </Form.Item>
          
          {/* New fields for reasoning and multimodal models */}
          <Form.Item
            name="is_reasoning"
            label="推理模型"
            valuePropName="checked"
            tooltip="如果是推理模型，请启用此选项"
            initialValue={isReasoning}
          >
            <Switch checked={isReasoning} onChange={(checked) => setIsReasoning(checked)} />
          </Form.Item>
          
          <Form.Item
            name="is_multimodal"
            label="多模态模型"
            valuePropName="checked"
            tooltip="如果是支持多模态（如图像、音频等）的模型，请启用此选项"
            initialValue={isMultimodal}
          >
            <Switch checked={isMultimodal} onChange={(checked) => setIsMultimodal(checked)} />
          </Form.Item>
          
          <Form.Item
            name="is_published"
            label="是否发布"
            valuePropName="checked"
            tooltip="只有发布的模型才能被其他用户看到"
            initialValue={isPublished}
          >
            <Switch checked={isPublished} onChange={(checked) => setIsPublished(checked)} />
          </Form.Item>
          
          <Form.Item
            name="color"
            label="模型颜色"
          >
            <Popover content={colorPickerContent} trigger="click">
              <div style={{ backgroundColor: color, width: '10%', height: '32px', border: '1px solid #d9d9d9', borderRadius: '2px', cursor: 'pointer' }} />
            </Popover>
          </Form.Item>
          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
            initialValue={isActive}
          >
            <Switch checked={isActive} onChange={(checked) => setIsActive(checked)} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存更改
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default EditLLM;