/* CreateLLM.module.css */

.pageContainer {
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* Ensure it takes full height */
    padding: 20px;
    background-color: #f0f2f5; /* Light background for contrast */
  }
  
  .headerContainer {
    display: flex;
    justify-content: space-between; /* Pushes title and sidebar apart */
    align-items: center; /* Vertically align items */
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #d9d9d9; /* Optional separator */
  }
  
  .pageTitle {
    margin: 0; /* Remove default margin */
    font-size: 24px; /* Adjust size as needed */
    color: #333;
  }
  
  .formContainer {
    background-color: #fff; /* White background for the form */
    padding: 24px;
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    flex-grow: 1; /* Allow form container to grow if needed */
    max-width: 800px; /* Optional: Limit form width */
    margin: 0 auto; /* Center the form container horizontally */
  }
  
  /* Style for the color picker trigger div */
  .colorPickerTrigger {
    width: 36px; /* Make it slightly larger */
    height: 36px;
    border: 1px solid #d9d9d9;
    border-radius: 4px; /* Slightly rounder corners */
    cursor: pointer;
    display: inline-block; /* Ensure it behaves like other form elements */
    vertical-align: middle; /* Align with text/labels */
  }
  
  /* You can add more specific styles if needed, e.g., for form items */
  /* .formContainer :global(.ant-form-item) {
    margin-bottom: 16px;
  } */
  
  /* :global selector targets Ant Design's classes directly without module scoping.
     Use sparingly, prefer styling wrapper elements. */