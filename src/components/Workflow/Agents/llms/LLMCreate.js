import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, message, Modal, notification, InputNumber, Popover, Switch } from 'antd';
import { SketchPicker } from 'react-color';
import { useNavigate } from 'react-router-dom';
// 1. Import the CSS module
import styles from './CreateLLM.module.css'; // Use styles object
import { LLMS_NAME, LLM_ENDPOINT } from '../../../Configs/Config';
import { createData, fetchData } from '../../../Routers/Router';
import SidebarList from '../../../SidebarList';
const { Option } = Select;

const CreateLLM = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [llmNames, setLlmNames] = useState([]); // Initialize as empty array
  const [color, setColor] = useState('#0056b3');
  const [isActive, setIsActive] = useState(true);
  const [doSample, setDoSample] = useState(false);
  const [isReasoning, setIsReasoning] = useState(false); // New state for reasoning models
  const [isMultimodal, setIsMultimodal] = useState(false); // New state for multimodal models
  const [isPublished, setIsPublished] = useState(true); // Added for completeness
  const navigate = useNavigate();

  useEffect(() => {
    const fetchLLMNames = async () => {
      try {
        const LLM_NAMES_ENDPOINT = `${LLM_ENDPOINT}/llm_names`;
        const result = await fetchData(LLM_NAMES_ENDPOINT);
        // Assuming result.data is the array of names
        setLlmNames(result.data || []); // Set to empty array if data is null/undefined
      } catch (error) {
        console.error('Error fetching LLM names:', error);
        // Optionally show an error message to the user
         notification.error({
            message: '加载模型名称失败',
            description: '无法获取现有模型名称列表，请稍后重试。',
         });
      }
    };

    fetchLLMNames();
  }, []); // Empty dependency array means this effect runs only once on mount

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要创建这个LLM配置吗？', // Adjusted confirmation message
      okText: '确认', // Customize button text
      cancelText: '取消', // Customize button text
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${LLM_ENDPOINT}/`;
          // Include color, is_active, do_sample, and the new fields from state
          const payload = {
            ...values,
            color,
            is_active: isActive,
            do_sample: doSample,
            is_reasoning: isReasoning,
            is_multimodal: isMultimodal,
            is_published: isPublished
          };
          await createData(endpoint_api, payload);
          message.success('LLM created successfully');

          form.resetFields();
          // Reset state variables manually if needed after resetFields
          setColor('#0056b3');
          setIsActive(true);
          setDoSample(false);
          setIsReasoning(false);
          setIsMultimodal(false);
          setIsPublished(true);

          navigate(`${LLMS_NAME}`);
        } catch (error) {
          console.error('Error creating LLM:', error);
          // Provide more specific error feedback if possible
          const errorMsg = error.response?.data?.detail || 'Failed to create LLM. Please check the details and try again.';
          notification.error({
            message: '创建失败',
            description: errorMsg,
            duration: 7, // Slightly longer duration for error
          });
        } finally {
          setLoading(false);
        }
      },
      onCancel: () => {
        console.log('LLM creation cancelled');
      },
    });
  };


  const handleColorChange = (newColor) => {
    setColor(newColor.hex);
  };

  const colorPickerContent = (
    <SketchPicker color={color} onChangeComplete={handleColorChange} />
  );

  // --- Form Validation ---
   const validateLlmName = (_, value) => {
     if (value && llmNames.includes(value.trim())) {
       return Promise.reject(new Error('此模型名称已存在!'));
     }
     return Promise.resolve();
   };


  return (
    // 2. Apply styles using the 'styles' object
    <div className={styles.pageContainer}>
      <div className={styles.headerContainer}>
        <h1 className={styles.pageTitle}>LLM配置</h1>
        <SidebarList /> {/* Add the Logout component */}
      </div>
      <div className={styles.formContainer}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={(errorInfo) => {
            console.log('Failed:', errorInfo);
            message.error('请检查表单输入!'); // General form error message
          }}
           // Add initial values here if preferred over individual items
           initialValues={{
             max_length: 16000,
             top_k: 50,
             temperature: 1.0,
             top_p: 0.9,
             do_sample: false, // Matches initial state
             labels: ['医疗'],
             is_active: true, // Matches initial state
             is_reasoning: false, // Matches initial state for new field
             is_multimodal: false, // Matches initial state for new field
             is_published: true, // Matches initial state
             input_price: 0.000, // Default price
             output_price: 0.000 // Default price
           }}
        >
          <Form.Item
            name="name"
            label="模型名"
            rules={[
              { required: true, message: '请输入模型名!' },
              { validator: validateLlmName } // Add custom validator
            ]}
            hasFeedback // Shows validation status icon
          >
            <Input
              placeholder="例如 gpt-4-turbo"
            />
          </Form.Item>
          <Form.Item
            name="server_url"
            label="模型服务地址 (API Endpoint)"
            rules={[{ required: true, message: '请输入模型服务地址!' }]}
            tooltip="例如: https://api.openai.com/v1/chat/completions"
          >
            <Input placeholder="https://api.example.com/v1/..." />
          </Form.Item>
          <Form.Item
            name="api_key"
            label="模型密钥 (API Key)"
            rules={[{ required: true, message: '请输入模型密钥!' }]}
            tooltip="如果模型需要认证，请提供API密钥"
          >
            <Input.Password placeholder="输入API Key" />
          </Form.Item>
          <Form.Item
            name="enterprise"
            label="企业/提供商"
            rules={[{ required: false }]} // Made optional
            tooltip="例如 OpenAI, Google, Anthropic, 或内部名称"
          >
            <Input placeholder="例如 OpenAI" />
          </Form.Item>
          <Form.Item
            name="description"
            label="大模型描述"
            rules={[{ required: false }]} // Made optional
          >
            <Input.TextArea rows={3} placeholder="描述此模型的功能、特点或用途" />
          </Form.Item>
          <Form.Item
            name="input_price"
            label="输入价格 (元 / 1k tokens)"
            rules={[{ required: true, type: 'number', min: 0, message: '请输入有效的输入价格' }]}
            tooltip="模型处理输入文本的价格"
          >
            {/* Use precision for currency */}
            <InputNumber min={0} step={0.001} precision={5} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="output_price"
            label="输出价格 (元 / 1k tokens)"
            rules={[{ required: true, type: 'number', min: 0, message: '请输入有效的输出价格' }]}
            tooltip="模型生成输出文本的价格"
          >
            <InputNumber min={0} step={0.001} precision={5} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="max_length"
            label="最大上下文长度 (tokens)"
            rules={[{ required: true, type: 'integer', min: 1, message: '请输入有效的最大长度' }]}
            tooltip="模型能处理的最大token数（输入+输出）"
          >
             {/* Increase max value if needed */}
            <InputNumber min={1} max={128000} step={1000} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="top_k"
            label="Top K"
            rules={[{ required: true, type: 'integer', min: 0, message: '请输入有效的Top K值' }]}
            tooltip="在每个步骤中，只考虑概率最高的 K 个词"
          >
            <InputNumber min={0} max={100} step={1} style={{ width: '100%' }}/>
          </Form.Item>
          <Form.Item
            name="temperature"
            label="Temperature"
            rules={[{ required: true, type: 'number', min: 0, max: 2, message: '请输入0到2之间的Temperature值' }]} // Allow up to 2 for temp
            tooltip="控制随机性。较低的值使输出更确定性，较高的值更随机。通常在0到1之间。"
          >
            <InputNumber min={0} max={2} step={0.05} precision={2} style={{ width: '100%' }}/>
          </Form.Item>
          <Form.Item
            name="top_p"
            label="Top P (Nucleus Sampling)"
            rules={[{ required: true, type: 'number', min: 0, max: 1, message: '请输入0到1之间的Top P值' }]}
            tooltip="选择累积概率超过 P 的最小词集。通常与Temperature二选一使用。"
          >
            <InputNumber min={0} max={1} step={0.05} precision={2} style={{ width: '100%' }}/>
          </Form.Item>
          <Form.Item
            name="do_sample"
            label="启用采样 (Do Sample)"
            tooltip="是否启用采样策略（如Top K, Top P, Temperature）。如果不启用，通常使用贪心解码。"
            valuePropName="checked" // Connects Switch value to form field
          >
            {/* Controlled Switch using state and onChange */}
            <Switch checked={doSample} onChange={setDoSample} />
          </Form.Item>
          <Form.Item
            name="labels"
            label="大模型标签"
             tooltip="用于分类或过滤模型的标签"
          >
            <Select mode="tags" placeholder="输入或选择标签">
              {/* Add more common options or fetch dynamically */}
              <Option value="通用">通用</Option>
              <Option value="医疗">医疗</Option>
              <Option value="金融">金融</Option>
              <Option value="教育">教育</Option>
              <Option value="代码">代码</Option>
              <Option value="聊天">聊天</Option>
            </Select>
          </Form.Item>
          
          {/* New fields for reasoning and multimodal models */}
          <Form.Item
            name="is_reasoning"
            label="推理模型"
            tooltip="如果是推理模型，请启用此选项"
            valuePropName="checked"
          >
            <Switch checked={isReasoning} onChange={setIsReasoning} />
          </Form.Item>
          
          <Form.Item
            name="is_multimodal"
            label="多模态模型"
            tooltip="如果是支持多模态（如图像、音频等）的模型，请启用此选项"
            valuePropName="checked"
          >
            <Switch checked={isMultimodal} onChange={setIsMultimodal} />
          </Form.Item>
          
          <Form.Item
            name="is_published"
            label="是否发布"
            tooltip="只有发布的模型才能被其他用户看到"
            valuePropName="checked"
          >
            <Switch checked={isPublished} onChange={setIsPublished} />
          </Form.Item>
          
          <Form.Item
            label="模型颜色" // Label doesn't need a 'name' if not submitting its value directly
             tooltip="选择一个颜色用于在UI中区分此模型"
          >
            <Popover content={colorPickerContent} trigger="click" placement="right">
               {/* Apply style from CSS module, keep dynamic background inline */}
              <div
                className={styles.colorPickerTrigger}
                style={{ backgroundColor: color }}
              />
            </Popover>
          </Form.Item>
          <Form.Item
            name="is_active"
            label="是否激活"
            tooltip="只有激活的模型才能被应用程序使用"
            valuePropName="checked" // Connects Switch value to form field
          >
             {/* Controlled Switch using state and onChange */}
            <Switch checked={isActive} onChange={setIsActive} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} size="large">
              创建LLM
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default CreateLLM;