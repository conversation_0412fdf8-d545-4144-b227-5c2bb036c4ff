import { createSlice } from '@reduxjs/toolkit';

const loadLLMs = () => {
  try {
    const storedLLMs = localStorage.getItem('llms');
    return storedLLMs ? JSON.parse(storedLLMs) : [];
  } catch (error) {
    console.error('Failed to load LLMs from localStorage:', error);
    return [];
  }
};

const saveLLMs = (llms) => {
  try {
    localStorage.setItem('llms', JSON.stringify(llms));
  } catch (error) {
    console.error('Failed to save LLMs to localStorage:', error);
  }
};

const initialState = {
  llms: loadLLMs(),
};

export const LLMSlice = createSlice({
  name: 'llm',
  initialState,
  reducers: {
    setLLMs: (state, action) => {
      state.llms = action.payload;
      saveLLMs(state.llms);
    },
    addLLM: (state, action) => {
      state.llms.push(action.payload);
      saveLLMs(state.llms);
    },
    updateLLM: (state, action) => {
      const index = state.llms.findIndex(llm => llm.id === action.payload.id);
      if (index !== -1) {
        state.llms[index] = action.payload;
        saveLLMs(state.llms);
      }
    },
    removeLLM: (state, action) => {
      state.llms = state.llms.filter(llm => llm.id !== action.payload);
      saveLLMs(state.llms);
    },
    clearLLMs: (state) => {
      state.llms = [];
      saveLLMs(state.llms);
    },
  },
});

export const {
  setLLMs,
  addLLM,
  updateLLM,
  removeLLM,
  clearLLMs,
} = LLMSlice.actions;

export default LLMSlice.reducer;
