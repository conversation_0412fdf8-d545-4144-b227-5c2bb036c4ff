import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Select, Switch, message, Spin, Table, Checkbox } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { createData, fetchBulk } from '../../../../Routers/Router';
import SidebarList from '../../../../SidebarList';
import './Tool.css';  // 自定义样式文件
import { TOOL_ENDPOINT, WORKSPACE_ENDPOINT } from '../../../../Configs/Config';

const { Option } = Select;
const { TextArea } = Input;

const CreateTool = () => {
  const [form] = Form.useForm();  // 使用 Ant Design 的表单
  const [loading, setLoading] = useState(false);
  const [workspaceOptions, setWorkspaceOptions] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const [parameters, setParameters] = useState([]);
  const [outputParameters, setOutputParameters] = useState([]);
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const initialWorkspaceId = queryParams.get('workspace_id');  // 从 URL 查询参数中获取初始的工作区 ID

  useEffect(() => {
    const fetchWorkspaceOptions = async () => {
      try {
        const result = await fetchBulk(`${WORKSPACE_ENDPOINT}/bulk`, {});
        setWorkspaceOptions(result.data);  // 设置工作区选项
      } catch (error) {
        message.error('Failed to load workspace options');
      } finally {
        setInitialLoading(false);
      }
    };
    fetchWorkspaceOptions();
  }, []);

  const onFinish = async (values) => {
    setLoading(true);

    const valuesWithWorkspace = { ...values, workspace_id: initialWorkspaceId, parameters, output_parameters: outputParameters };
 
    try {
      await createData(`${TOOL_ENDPOINT}/`, valuesWithWorkspace);  // 调用 createData 发送 POST 请求
      message.success('Tool created successfully');
      form.resetFields();
      setParameters([]);
      setOutputParameters([]);
      navigate(-1);  // 创建成功后返回上一页
    } catch (error) {
      message.error(`Failed to create tool. Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return <Spin tip="Loading..." />;
  }

  const handleParameterChange = (index, key, value) => {
    const newParameters = [...parameters];
    newParameters[index] = { ...newParameters[index], [key]: value };
    setParameters(newParameters);
  };

  const handleOutputParameterChange = (index, key, value) => {
    const newOutputParameters = [...outputParameters];
    newOutputParameters[index] = { ...newOutputParameters[index], [key]: value };
    setOutputParameters(newOutputParameters);
  };

  const parameterColumns = [
    {
      title: '参数名称',
      dataIndex: 'name',
      key: 'name',
      render: (_, record, index) => (
        <Input
          value={parameters[index]?.name || ''}
          onChange={(e) => handleParameterChange(index, 'name', e.target.value)}
          placeholder="参数名称"
        />
      ),
    },
    {
      title: '参数描述',
      dataIndex: 'description',
      key: 'description',
      render: (_, record, index) => (
        <Input
          value={parameters[index]?.description || ''}
          onChange={(e) => handleParameterChange(index, 'description', e.target.value)}
          placeholder="参数描述"
        />
      ),
    },
    {
      title: '参数类型',
      dataIndex: 'type',
      key: 'type',
      render: (_, record, index) => (
        <Select
          value={parameters[index]?.type || ''}
          onChange={(value) => handleParameterChange(index, 'type', value)}
          placeholder="参数类型"
        >
          <Option value="String">字符</Option>
          <Option value="Int">整数</Option>
          <Option value="Boolean">是非</Option>
          <Option value="Array">数据</Option>
          <Option value="Object">对象</Option>
          <Option value="Float">浮点数</Option>
        </Select>
      ),
    },
    {
      title: '传入方法',
      dataIndex: 'method',
      key: 'method',
      render: (_, record, index) => (
        <Select
          value={parameters[index]?.method || ''}
          onChange={(value) => handleParameterChange(index, 'method', value)}
          placeholder="传入方法"
        >
          <Option value="Query">Query</Option>
          <Option value="Body">Body</Option>
          <Option value="Path">Path</Option>
          <Option value="Header">Header</Option>
        </Select>
      ),
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      key: 'required',
      render: (_, record, index) => (
        <Checkbox
          checked={parameters[index]?.required || false}
          onChange={(e) => handleParameterChange(index, 'required', e.target.checked)}
        />
      ),
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      render: (_, record, index) => (
        <Input
          value={parameters[index]?.defaultValue || ''}
          onChange={(e) => handleParameterChange(index, 'defaultValue', e.target.value)}
          placeholder="请输入默认值"
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record, index) => (
        <Button type="link" onClick={() => {
          const newParameters = parameters.filter((_, i) => i !== index);
          setParameters(newParameters);
        }}>删除</Button>
      ),
    },
  ];

  const outputParameterColumns = [
    {
      title: '参数名称',
      dataIndex: 'name',
      key: 'name',
      render: (_, record, index) => (
        <Input
          value={outputParameters[index]?.name || ''}
          onChange={(e) => handleOutputParameterChange(index, 'name', e.target.value)}
          placeholder="输出参数名称"
        />
      ),
    },
    {
      title: '参数描述',
      dataIndex: 'description',
      key: 'description',
      render: (_, record, index) => (
        <Input
          value={outputParameters[index]?.description || ''}
          onChange={(e) => handleOutputParameterChange(index, 'description', e.target.value)}
          placeholder="输出参数描述"
        />
      ),
    },
    {
      title: '参数类型',
      dataIndex: 'type',
      key: 'type',
      render: (_, record, index) => (
        <Select
          value={outputParameters[index]?.type || ''}
          onChange={(value) => handleOutputParameterChange(index, 'type', value)}
          placeholder="输出参数类型"
        >
          <Option value="String">字符</Option>
          <Option value="Int">整数</Option>
          <Option value="Boolean">是非</Option>
          <Option value="Array">数据</Option>
          <Option value="Object">对象</Option>
          <Option value="Float">浮点数</Option>
        </Select>
      ),
    },
    {
      title: '启用',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (_, record, index) => (
        <Switch
          checked={outputParameters[index]?.is_active || false}
          onChange={(checked) => handleOutputParameterChange(index, 'is_active', checked)}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record, index) => (
        <Button type="link" onClick={() => {
          const newOutputParameters = outputParameters.filter((_, i) => i !== index);
          setOutputParameters(newOutputParameters);
        }}>删除</Button>
      ),
    },
  ];

  return (
    <div className="page-container">
      <div className="header-container">
        <h1 className="page-title">创建工具</h1>
        <SidebarList />
      </div>
      <div className="content-container">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ workspace_id: initialWorkspaceId }}
        >
          <Form.Item
            name="name"
            label="工具名称"
            rules={[{ required: true, message: '请输入工具名称!' }]}
          >
            <Input placeholder="请输入工具名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="工具描述"
            rules={[{ required: false, message: '请输入工具描述' }]}
          >
            <TextArea rows={4} placeholder="请输入工具描述" />
          </Form.Item>

          <Form.Item
            name="path"
            label="API 路径"
            rules={[{ required: true, message: '请输入工具 API 路径' }]}
          >
            <Input placeholder="请输入工具 API 路径" />
          </Form.Item>

          <Form.Item
            name="method"
            label="请求方法"
            rules={[{ required: true, message: '请选择请求方法' }]}
          >
            <Select placeholder="请选择请求方法">
              <Option value="GET">GET</Option>
              <Option value="POST">POST</Option>
              <Option value="PUT">PUT</Option>
              <Option value="DELETE">DELETE</Option>
            </Select>
          </Form.Item>

          <h3>配置输入参数</h3>
          <Button type="dashed" onClick={() => setParameters([...parameters, {}])} block>
            新增参数
          </Button>
          <Table
            dataSource={parameters.map((param, index) => ({ ...param, key: index }))}
            columns={parameterColumns}
            pagination={false}
            rowKey="key"
          />

          <h3>配置输出参数</h3>
          <Button type="dashed" onClick={() => setOutputParameters([...outputParameters, {}])} block>
            新增参数
          </Button>
          <Table
            dataSource={outputParameters.map((param, index) => ({ ...param, key: index }))}
            columns={outputParameterColumns}
            pagination={false}
            rowKey="key"
          />

          <Form.Item
            name="is_active"
            label="是否启用"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="is_published"
            label="是否发布"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default CreateTool;