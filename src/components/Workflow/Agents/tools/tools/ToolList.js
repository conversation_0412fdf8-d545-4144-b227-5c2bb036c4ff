import React, { useEffect, useState } from 'react';
import { Button, message, Select, Pagination,Tooltip } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { fetchBulk, deleteData } from '../../../../Routers/Router';
import SidebarList from '../../../../SidebarList';
import { Card } from '@chatui/core';
import './Tool.css';  // 新增的样式文件
import { TOOL_ENDPOINT, TOOL_NEW_NAME, TOOL_EDIT_NAME } from '../../../../Configs/Config';

const { Option } = Select;

const ToolList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalItems, setTotalItems] = useState(0);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  const sortField = 'updated_at';
  const sortOrder = 'desc';
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const username = queryParams.get('username');
  const is_active = queryParams.get('is_active');
  const workspace_id = queryParams.get('workspace_id');
  console.log(queryParams,'queryParams');

  useEffect(() => {
    getData(currentPage, pageSize, is_active, username);
  }, [currentPage, pageSize, is_active, username,workspace_id]);

  const getData = async (page, pageSize, is_active, username) => {
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${TOOL_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&skip=${skip}&limit=${limit}&username=${user_info.username}`;

      if (username) {
        endpoint_api += `&username=${username}`;
      }

      if (is_active) {
        endpoint_api += `&is_active=${is_active}`;
      }
      const params = { workspace_id};
      const result = await fetchBulk(endpoint_api,params);
      console.log(result,' + result')
      setData(result.data);
      setTotalItems(result.count);
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch tool data');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteData(`${TOOL_ENDPOINT}/${id}?workspace_id=${workspace_id}`);
      setData(data.filter((item) => item.id !== id));
      message.success('Tool deleted successfully');
    } catch (error) {
      message.error('Failed to delete tool');
    }
  };

  const handlePageChange = (page, newPageSize) => {
    setCurrentPage(page);
    setPageSize(newPageSize);
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">Tool 列表</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        <Button
          className="custom-button"
          onClick={() => navigate(`${TOOL_NEW_NAME}?workspace_id=${workspace_id}`)}
        >
          新增
        </Button>
      </div>
      <div className="card-container">
        {data.map((record) => (
          <Card
            key={record.id}
            title={record.name}
            className="Card"
            style={{ marginBottom: '16px', cursor: 'pointer' }}
            onClick={() => navigate(`${TOOL_EDIT_NAME}/${workspace_id}/${record.id}`)}
          >
            <Tooltip title={record.description}>
              <p>
                描述: {record.description.length > 20 
                  ? record.description.substring(0, 20) + '...' 
                  : record.description}
              </p>
            </Tooltip>
            <p>路径: {record.path}</p>
            <p>请求方法: {record.method}</p>
            <p>创建者: {record.username}</p>
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(record.id);
              }}
            >
              删除
            </Button>
          </Card>
        ))}
      </div>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={totalItems}
        showSizeChanger
        onChange={handlePageChange}
        pageSizeOptions={['12', '24', '48', '96']}
        style={{ marginTop: '16px', textAlign: 'center' }}
      />
    </div>
  );
};

export default ToolList;
