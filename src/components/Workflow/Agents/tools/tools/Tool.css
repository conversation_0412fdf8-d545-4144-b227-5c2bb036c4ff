/* ToolList.css */
.form-container {
    padding: 20px;
    background-color: #f0f2f5;
  }
  
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
  }
  
  .button-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;
  }
  
  .custom-button {
    margin-right: 16px;
    background-color: #1890ff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .custom-button:hover {
    background-color: #40a9ff;
  }
  
  .card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: start;
  }
  
  .Card {
    width: calc(33.33% - 16px);
    background-color: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    transition: transform 0.2s;
  }
  
  .Card:hover {
    transform: scale(1.02);
    border-color: #1890ff;
  }
  
  .ant-pagination {
    margin-top: 16px;
    text-align: center;
  }
  