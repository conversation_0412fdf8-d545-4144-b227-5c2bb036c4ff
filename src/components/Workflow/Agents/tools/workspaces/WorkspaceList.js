import React, { useEffect, useState } from 'react';
import { Button, message, Pagination } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { fetchBulk, deleteData } from '../../../../Routers/Router';
import SidebarList from '../../../../SidebarList';
import './Workspace.css';
import { WORKSPACE_ENDPOINT, WORKSPACE_NEW_NAME, TOOLS_NAME } from '../../../../Configs/Config';

const WorkspaceList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalItems, setTotalItems] = useState(0);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  const sortField = 'updated_at';
  const sortOrder = 'desc';
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const username = queryParams.get('username');
  const is_published = queryParams.get('is_published');

  useEffect(() => {
    getData(currentPage, pageSize, is_published, username);
  }, [currentPage, pageSize, is_published, username]);

  const getData = async (page, pageSize, is_published, username) => {
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${WORKSPACE_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&skip=${skip}&limit=${limit}`;

      if (username) {
        endpoint_api += `&username=${username}`;
      }
      
      if (is_published) {
        endpoint_api += `&is_published=${is_published}`;
      }
      const result = await fetchBulk(endpoint_api);
      setData(result.data);
      setTotalItems(result.count);
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch workspace data');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteData(`${WORKSPACE_ENDPOINT}/${id}`);
      setData(data.filter((item) => item.id !== id));
      message.success('Workspace deleted successfully');
    } catch (error) {
      message.error('Failed to delete workspace');
    }
  };

  const handlePageChange = (page, newPageSize) => {
    setCurrentPage(page);
    setPageSize(newPageSize);
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">Workspace列表</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        <Button
          className="custom-button"
          onClick={() => navigate(`${WORKSPACE_NEW_NAME}`)}
        >
          新增
        </Button>
      </div>
      <div className="card-container">
        {data.map((record) => (
          <div
            key={record.id}
            className="Card"
            onClick={() => navigate(`${TOOLS_NAME}?workspace_id=${record.id}`)}
          >
            <h3>{record.name}</h3>
            <p>描述: {record.description}</p>
            <p>创建者: {record.username}</p>
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(record.id);
              }}
            >
              删除
            </Button>
          </div>
        ))}
      </div>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={totalItems}
        showSizeChanger
        onChange={handlePageChange}
        pageSizeOptions={['12', '24', '48', '96']}
        style={{ marginTop: '16px', textAlign: 'center' }}
      />
    </div>
  );
};

export default WorkspaceList;