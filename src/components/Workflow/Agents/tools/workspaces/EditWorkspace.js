import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Switch, message, Modal, Spin } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { WORKFLOWS_NAME, WORKSPACE_ENDPOINT } from '../../../../Configs/Config';
import { fetchData, updateData } from '../../../../Routers/Router';
import SidebarList from '../../../../SidebarList';
import './Workspace.css';
import { useSelector } from 'react-redux';

const { TextArea } = Input;

const EditWorkspace = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const navigate = useNavigate();
  const { id } = useParams(); // 获取URL中的id参数
  const { user_info } = useSelector((state) => state.user);

  useEffect(() => {
    const fetchWorkspace = async () => {
      try {
        const endpoint_api = `${WORKSPACE_ENDPOINT}/${id}`;
        const result = await fetchData(endpoint_api);
        form.setFieldsValue(result); // 将获取到的数据填充到表单中
        setInitialLoading(false);
      } catch (error) {
        message.error(`加载Workspace数据失败: ${error.message}`);
        setInitialLoading(false);
      }
    };
    fetchWorkspace();
  }, [id, form]);

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认更新',
      content: '您确定要更新这个Workspace吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${WORKSPACE_ENDPOINT}/${id}`;
          await updateData(endpoint_api, values);
          message.success('Workspace更新成功');
          navigate(`${WORKFLOWS_NAME}`); // 返回到工作区列表页面
        } catch (error) {
          message.error(`更新Workspace失败，请重试。错误信息: ${error.message}`);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  if (initialLoading) {
    return <Spin tip="Loading..." />;
  }

  return (
    <div className="page-container">
      <div className="header-container">
        <h1 className="page-title">编辑Workspace</h1>
        <SidebarList />
      </div>
      <div className="content-container">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          <Form.Item
            name="name"
            label="Workspace名称"
            rules={[{ required: true, message: '请输入Workspace名称!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="description"
            label="Workspace描述"
            rules={[{ required: true, message: '请输入Workspace描述!' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="是否启用"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              更新
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default EditWorkspace;
