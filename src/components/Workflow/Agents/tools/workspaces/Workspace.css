/* Workspace.css */

/* General Styles */
.form-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.page-title {
  padding-top: 25px;
  font-size: 24px;
  font-weight: bold;
}

.button-container {
  margin-bottom: 20px;
}

/* Header and Button Group */
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-container-workflow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 10px;
}

.button-group {
  display: flex;
  padding-top: 30px;
  align-items: center;
  justify-content: space-between;
  width: 280px;
}

/* Button Styles */
.custom-button {
  margin-right: 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.custom-button:hover {
  background-color: #40a9ff;
}

.custom-button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.save-workflow,
.debug-button,
.logout-button,
.add-example-button,
.example-delete {
  border: none;
  cursor: pointer;
  border-radius: 2px;
  font-size: 12px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.save-workflow,
.debug-button,
.logout-button {
  width: 100px;
}

.save-workflow {
  margin-right: 125px;
  margin-top: 6px;
  background-color: #4CAF50;
  color: white;
}

.save-workflow:hover {
  background-color: #45a049;
}

.save-workflow:active {
  background-color: #3e8e41;
}

.debug-button {
  margin-top: 6px;
  background-color: #126cf3b5;
  color: white;
}

.debug-button:hover {
  background-color: #e67e22;
}

.debug-button:active {
  background-color: #d35400;
}

.example-delete {
  width: 60px;
  padding: 0 8px;
}

/* Link Button */
.link-button {
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: inherit;
}

.link-button:hover {
  color: #40a9ff;
}

/* Card Styles */
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
}

.Card {
  width: calc(33.33% - 11px);  /* Ensures 3 cards per row */
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: transform 0.2s;
  margin-bottom: 16px;
  padding: 16px;
  box-sizing: border-box;
  cursor: pointer;
}

.Card:hover {
  transform: scale(1.02);
  border-color: #1890ff;
}

.Card h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 18px;
}

.Card p {
  margin: 0;
  padding: 4px 0;
}

/* Pagination Styles */
.ant-pagination {
  margin-top: 16px;
  text-align: center;
}

/* Table Styles */
.ant-table-wrapper {
  padding: 0 20px;
  overflow: auto;
}

.ant-table-cell {
  white-space: normal !important;
  word-wrap: break-word;
}

.selected-row {
  background-color: #e6f7ff !important;
}

/* Example Item Styles */
.example-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.example-input {
  flex-grow: 1;
  margin-right: 8px;
}

/* Add Example Button Override */
.add-example-button.ant-btn.css-dev-only-do-not-override-m4timi.ant-btn-primary.ant-btn-compact-item.ant-btn-compact-last-item {
  background-color: #1890ff;
  color: white;
  padding: 0 15px;
  font-size: 14px;
  width: auto;
  transition: background-color 0.3s;
}

.add-example-button.ant-btn.css-dev-only-do-not-override-m4timi.ant-btn-primary.ant-btn-compact-item.ant-btn-compact-last-item:hover {
  background-color: #40a9ff;
}

.add-example-button.ant-btn.css-dev-only-do-not-override-m4timi.ant-btn-primary.ant-btn-compact-item.ant-btn-compact-last-item:active {
  background-color: #096dd9;
}

/* Responsive design */
@media screen and (max-width: 1200px) {
  .Card {
    width: calc(50% - 8px);
  }
}

@media screen and (max-width: 768px) {
  .Card {
    width: 100%;
  }
}