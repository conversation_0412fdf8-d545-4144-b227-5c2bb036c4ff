import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Switch, message, Modal, Spin } from 'antd';
import { useNavigate } from 'react-router-dom';
import { TOOLS_NAME, WORKSPACE_ENDPOINT } from '../../../../Configs/Config';
import { createData } from '../../../../Routers/Router';
import SidebarList from '../../../../SidebarList';
import './Workspace.css';
import { useSelector } from 'react-redux';

const { TextArea } = Input;

const CreateWorkspace = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认创建',
      content: '您确定要创建这个Workspace吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${WORKSPACE_ENDPOINT}/`;
          const result = await createData(endpoint_api, values);
          message.success('Workspace创建成功');
          form.resetFields();
          navigate(`${TOOLS_NAME}?workspace_id=${result.id}`); // 返回到工作区列表页面
        } catch (error) {
          message.error(`创建Workspace失败，请重试。错误信息: ${error.message}`);
        } finally {
          setLoading(false);
        }
      },
    });
  };


  return (
    <div className="page-container">
      <div className="header-container">
        <h1 className="page-title">创建Workspace</h1>
        <SidebarList />
      </div>
      <div className="content-container">
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
          >
            <Form.Item
              name="name"
              label="Workspace名称"
              rules={[{ required: true, message: '请输入Workspace名称!' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="description"
              label="Workspace描述"
              rules={[{ required: true, message: '请输入Workspace描述!' }]}
            >
              <TextArea rows={4} />
            </Form.Item>

            <Form.Item
              name="is_active"
              label="是否启用"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading}>
                创建
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
  );
};

export default CreateWorkspace;
