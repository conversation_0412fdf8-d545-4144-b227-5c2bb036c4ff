/* AgentEdit.module.css */

.pageContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #d9d9d9;
}

.pageTitle {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.contentContainer {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.agentEditForm {
  display: flex;
  gap: 24px;
}

.leftContainer {
  flex: 1;
  min-width: 400px;
}

.rightContainer {
  flex: 1;
  min-width: 400px;
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input[type="textarea"] {
  resize: vertical;
}

.ant-select {
  width: 100%;
}

.ant-switch {
  margin-top: 4px;
}

.submitButtonContainer {
    text-align: center;
    margin-top: 20px;
}

.submitButton {
    width: 100%;
    max-width: 300px;
}

/* Styles for dynamic example list */
.exampleSpace {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.exampleSpace .ant-form-item {
  margin-bottom: 0;
}

.ant-divider-horizontal.ant-divider-with-text {
  margin: 24px 0;
}

.ant-divider-horizontal .ant-divider-inner-text {
  font-size: 16px;
  font-weight: bold;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .agentEditForm {
    flex-direction: column;
  }

  .leftContainer,
  .rightContainer {
    width: 100%;
    min-width: auto;
  }

  .headerContainer {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Style the example text area to take up most of the width */
.exampleSpace .ant-form-item:first-child {
  flex: 1; /* Allow the textarea to grow and fill the space */
  margin-right: 8px; /* Add spacing between textarea and switch */
  width: auto !important;
}

/* Adjust the width of the conversable switch */
.exampleSpace .ant-form-item:nth-child(2) {
  width: auto; /* Allow the switch to take its natural width */
  margin-right: 8px; /* Add spacing between switch and remove icon */
}

/* Full-width styling for the description area and example list */
.fullWidthItem .ant-form-item-control {
  width: 100%;
}

.fullWidthItem .ant-form-item-control-input-content {
    width: 100%;
}

/* Add this style to force the text area to take up the full space */
.fullWidthItem .ant-input[type="textarea"] {
  width: 100%;
}

/* Added button styling */
.submitButtonContainer {
  text-align: center; /* Center the button horizontally */
  margin-top: 20px; /* Add some spacing above the button */
}

.submitButton {
  width: 100%; /* Make the button take the full width of its container */
  max-width: 300px; /* Limit the maximum width of the button */
}