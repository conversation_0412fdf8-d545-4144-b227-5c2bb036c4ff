import React, { useEffect, useState, useRef } from 'react';
import { But<PERSON>, message, Switch, Select, Pagination, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import SidebarList from '../../../SidebarList';
import {
  AGENT_EDIT_NAME,
  AGENT_NEW_NAME,
  AGENT_ENDPOINT,
  SUPERUSER
} from '../../../Configs/Config';
import { fetchBulk, deleteData, deleteBulk, updateData } from '../../../Routers/Router';
import { Card } from '@chatui/core';
import './Agent.css';
import { setAgents } from './AgentSlice';

const { Option } = Select;

const AgentList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const sortField = 'updated_at';
  const sortOrder = 'desc';
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalItems, setTotalItems] = useState(0);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  const dispatch = useDispatch();

  // Ref for scrolling to the top of the card container
  const cardContainerRef = useRef(null);

  useEffect(() => {
    getData(currentPage, pageSize);
  }, [sortField, sortOrder, currentPage, pageSize]);

  const getData = async (page, pageSize) => {
    try {
      const skip = (page - 1) * pageSize;
      const limit = pageSize;
      let endpoint_api = `${AGENT_ENDPOINT}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}&username=${user_info.username}`;


      const result = await fetchBulk(endpoint_api);
      console.log(result.data,'fetchAgents')
      setData(result.data);
      setTotalItems(result.count);
      dispatch(setAgents(result.data));
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch agent data');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    const endpoint_api = `${AGENT_ENDPOINT}/${id}`;
    try {
      await deleteData(endpoint_api);
      setData(data.filter((item) => item.id !== id));
      message.success('Agent deleted successfully');
    } catch (error) {
      message.error('Failed to delete agent');
    }
  };

  const handleIsActiveChange = async (record, isActive) => {
    const endpoint_api = `${AGENT_ENDPOINT}/${record.id}`;
    const updatedRecord = { ...record, is_active: isActive };
    try {
      const update_data = await updateData(endpoint_api, updatedRecord);
      setData(data.map(item =>
        item.id === record.id ? { ...update_data, is_active: isActive } : item
      ));
      message.success('Agent激活状态更新成功');
    } catch (error) {
      message.error('更新Agent激活状态失败');
    }
  };

  const handleIsPublishedChange = async (record, isPublished) => {
    const endpoint_api = `${AGENT_ENDPOINT}/${record.id}`;
    const updatedRecord = { ...record, is_published: isPublished };
    try {
      const update_data = await updateData(endpoint_api, updatedRecord);
      setData(data.map(item =>
        item.id === record.id ? { ...update_data, is_published: isPublished } : item
      ));
      message.success('Agent发布状态更新成功');
    } catch (error) {
      message.error('更新Agent发布状态失败');
    }
  };

  const handlePageChange = (page, newPageSize) => {
    setCurrentPage(page);
    setPageSize(newPageSize);
    getData(page, newPageSize);
    if (cardContainerRef.current) {
      cardContainerRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleBulkDelete = async () => {
    try {
      const endpoint_api = `${AGENT_ENDPOINT}/bulk`;
      await deleteBulk(endpoint_api, { agent_ids: selectedRowKeys });
      setData(data.filter((item) => !selectedRowKeys.includes(item.id)));
      setSelectedRowKeys([]);
      message.success('Selected agents have been deleted');
    } catch (error) {
      message.error('Failed to delete selected agents');
    }
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">Agent列表</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        <button
          className="custom-button"
          onClick={() => navigate(`${AGENT_NEW_NAME}`)}
        >
          新增
        </button>
      </div>
      <div className="card-container" ref={cardContainerRef}>
        {data.map((record) => (
          <Card 
            key={record.id} 
            title={record.name} 
            className="Card"
            style={{ marginBottom: '16px', cursor: 'pointer' }}
            onClick={(e) => {
              e.stopPropagation(); // Prevent card click event propagation
              if (user_info.username === record.username) {
                navigate(`${AGENT_EDIT_NAME}/${record.id}`);
              }
            }}
          >
            <p>名称: {record.name}</p>
            <Tooltip title={record.description}>
              <p>
                描述: {record.description.length > 20 
                  ? record.description.substring(0, 20) + '...' 
                  : record.description}
              </p>
            </Tooltip>
            <p>大模型: {record.llm_name}</p>
            <Tooltip title={record.agent_type}>
              <p>
              类型: {record.agent_type.length > 20 
                  ? record.agent_type.substring(0, 20) + '...' 
                  : record.agent_type}
              </p>
            </Tooltip>
            <p>创建者: {record.username}</p>
            <div style={{ display: 'flex', gap: '8px', margin: '8px 0' }}>
              <Switch
                checked={record.is_active}
                onChange={(checked, event) => {
                  event.stopPropagation();
                  handleIsActiveChange(record, checked);
                }}
                checkedChildren="激活"
                unCheckedChildren="未激活"
                disabled={user_info.role === SUPERUSER && record.username !== user_info.username}
              />
              <Switch
                checked={record.is_published}
                onChange={(checked, event) => {
                  event.stopPropagation();
                  handleIsPublishedChange(record, checked);
                }}
                checkedChildren="发布"
                unCheckedChildren="未发布"
                disabled={user_info.role === SUPERUSER && record.username !== user_info.username}
              />
            </div>
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click event
                handleDelete(record.id);
              }}
              disabled={record.username !== user_info.username}
            >
              删除
            </Button>
          </Card>
        ))}
      </div>
      <Pagination
        current={currentPage}
        pageSize={pageSize}
        total={totalItems}
        showSizeChanger
        onChange={handlePageChange}
        pageSizeOptions={['12', '24', '48', '96']}
        style={{ marginTop: '16px', textAlign: 'center' }}
      />
    </div>
  );
};

export default AgentList;
