/* CreateAgent.module.css */

/* Overall page container */
.pageContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  /* Header section containing title and sidebar */
  .headerContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #f0f2f5;
    border-bottom: 1px solid #d9d9d9;
  }
  
  .pageTitle {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
  }
  
  /* Content container for the form */
  .contentContainer {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
  
  /* Agent form container */
  .agentFormContainer {
    display: flex;
    gap: 24px;
  }
  
  /* Left and right containers within the form */
  .leftContainer {
    flex: 1;
    min-width: 400px;
  }
  
  .rightContainer {
    flex: 1;
    min-width: 400px;
  }
  
  /* Style form items for better spacing */
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  /* Style form item labels */
  .ant-form-item-label > label {
    font-weight: 500;
  }
  
  /* Style the text area */
  .ant-input[type="textarea"] {
    resize: vertical;
  }
  
  /* Style Select components */
  .ant-select {
    width: 100%;
  }
  
  /* Style Switch components */
  .ant-switch {
    margin-top: 4px;
  }
  
  /* Style buttons */
  .ant-btn-primary {
    font-weight: 500;
  }
  
  /* Styles for dynamic example list */
  .exampleSpace {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px; /* Add margin between each example */
  }
  
  .exampleSpace .ant-form-item {
    margin-bottom: 0;
  }
  
  .ant-divider-horizontal.ant-divider-with-text {
    margin: 24px 0;
  }
  
  .ant-divider-horizontal .ant-divider-inner-text {
    font-size: 16px;
    font-weight: bold;
  }
  
  /* Responsive design for smaller screens */
  @media (max-width: 768px) {
    .agentFormContainer {
      flex-direction: column;
    }
  
    .leftContainer,
    .rightContainer {
      width: 100%;
      min-width: auto;
    }
  
    .headerContainer {
      flex-direction: column;
      align-items: flex-start;
    }
  }
  
  /* Style the example text area to take up most of the width */
  .exampleSpace .ant-form-item:first-child {
    flex: 1; /* Allow the textarea to grow and fill the space */
    margin-right: 8px; /* Add spacing between textarea and switch */
  }
  
  /* Adjust the width of the conversable switch */
  .exampleSpace .ant-form-item:nth-child(2) {
    width: auto; /* Allow the switch to take its natural width */
    margin-right: 8px; /* Add spacing between switch and remove icon */
  }
  
  /* Added button styling */
  .submitButtonContainer {
    text-align: center; /* Center the button horizontally */
    margin-top: 20px; /* Add some spacing above the button */
  }
  
  .submitButton {
    width: 100%; /* Make the button take the full width of its container */
    max-width: 300px; /* Limit the maximum width of the button */
  }