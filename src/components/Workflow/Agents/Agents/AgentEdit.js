import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Switch, Select, message, Modal, Spin, Space, Typography, Divider, Tooltip } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import {
  AGENT_ENDPOINT,
  AGENTS_NAME,
  LLM_ENDPOINT,
  KNOWLEDGE_BASE_ENDPOINT,
  WORKFLOW_ENDPOINT,
  SEARCH_NAME,
  TOOL_ENDPOINT,
  MCP_SERVER_ENDPOINT,
  PROMPT_ENDPOINT,
} from '../../../Configs/Config';
import { fetchData, updateData, fetchBulk } from '../../../Routers/Router';
import { useSelector, useDispatch } from 'react-redux';
import { getLlmOptions } from '../Utils/utils';
import SidebarList from '../../../SidebarList';
import { updateAgent, setSelectedAgentId } from './AgentSlice';
import styles from './AgentEdit.module.css';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

const AgentEdit = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [llmOptions, setLlmOptions] = useState([]);
  const [selectedAgentType, setSelectedAgentType] = useState(null);
  const [kbOptions, setKbOptions] = useState([]);
  const [workflowOptions, setWorkflowOptions] = useState([]);
  const [toolOptions, setToolOptions] = useState([]);
  const [agentTypeOptions, setAgentTypeOptions] = useState([]);
  const [mcpServersOptions, setMcpServersOptions] = useState([]);
  const [promptOptions, setPromptOptions] = useState([]);
  const { id } = useParams();
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  const dispatch = useDispatch();

  useEffect(() => {
    let isMounted = true;
    setInitialLoading(true);

    const fetchAllOptions = async () => {
      try {
        const llmOpts = getLlmOptions(LLM_ENDPOINT, user_info.username);
        const agentTypeOpts = fetchData(`${AGENT_ENDPOINT}/agent_types`);
        const kbOpts = getLlmOptions(KNOWLEDGE_BASE_ENDPOINT, user_info.username);
        const workflowOpts = fetchBulk(`${WORKFLOW_ENDPOINT}/bulk`, {username:user_info.username});
        const toolOpts = fetchBulk(`${TOOL_ENDPOINT}/bulk`, {username:user_info.username});
        const mcpServerOpts = fetchBulk(`${MCP_SERVER_ENDPOINT}/bulk`, {username:user_info.username,mcp_type:"tool"});
        const promptOpts = fetchBulk(`${PROMPT_ENDPOINT}/bulk`, {username:user_info.username});

        const [
          llmResult,
          agentTypeResult,
          kbResult,
          workflowResult,
          toolResult,
          mcpServerResult,
          promptResult,
        ] = await Promise.all([
          llmOpts,
          agentTypeOpts,
          kbOpts,
          workflowOpts,
          toolOpts,
          mcpServerOpts,
          promptOpts,
        ]);

        if (isMounted) {
          setLlmOptions(llmResult || []);
          setAgentTypeOptions(agentTypeResult.data || []);
          setKbOptions(kbResult || []);
          setWorkflowOptions(workflowResult.data || []);
          setToolOptions(toolResult.data || []);
          setMcpServersOptions(mcpServerResult.data || []);
          setPromptOptions(promptResult.data || []);
        }
      } catch (error) {
        console.error("Failed to fetch options:", error);
        if (isMounted) {
            message.error(`Failed to load options: ${error.message}`);
        }
      }
    };

    fetchAllOptions();

    return () => {
        isMounted = false;
    };
  }, [user_info.username]);
  console.log(promptOptions,'promptOptions')

  useEffect(() => {
    let isMounted = true;
    if (!id) return;

    const getAgent = async () => {
      setInitialLoading(true);
      try {
        const endpoint_api = `${AGENT_ENDPOINT}/${id}`;
        const data = await fetchData(endpoint_api);
        // console.log(data,isMounted,'dafdfa')
        if (isMounted) {
          const mapToNames = (items) => Array.isArray(items) ? items.map(item => item.name) : [];
          const toolsNames = mapToNames(data.tools);
          const workflowsNames = mapToNames(data.workflows);
          const kbNames = mapToNames(data.kb_names);
          const mcpServerNames = mapToNames(data.mcp_servers);
          const promptNames = mapToNames(data.prompt_names);

          form.setFieldsValue({
            ...data,
            tools: toolsNames,
            workflows: workflowsNames,
            kb_names: kbNames,
            mcp_servers: mcpServerNames,
            prompt_names: promptNames,
            examples: data.examples || [],
          });
          setSelectedAgentType(data.agent_type);
          updateKbNameRules(data.agent_type);
        }
      } catch (error) {
        console.error("Failed to fetch agent details:", error);
        if (isMounted) {
            message.error('Failed to fetch agent details');
        }
      } finally {
        if (isMounted) {
            setInitialLoading(false);
        }
      }
    };

    getAgent();

    return () => {
        isMounted = false;
    };
  }, [id, form]);

  useEffect(() => {
    if (id) {
      dispatch(setSelectedAgentId(id));
    }
  }, [id, dispatch]);

  const updateKbNameRules = (agentType) => {
    const isRequired = agentType === SEARCH_NAME;
    form.setFields([
      {
        name: 'kb_names',
        rules: [{ required: isRequired, message: isRequired ? 'Search Agent requires a Knowledge Base!' : '' }],
      },
    ]);
  };

  const handleAgentTypeChange = (value) => {
    setSelectedAgentType(value);
    updateKbNameRules(value);
  };

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const findFullObjects = (selectedNames, allOptions) => {
            if (!selectedNames || !Array.isArray(selectedNames)) return [];
            if (!Array.isArray(allOptions)) return [];
            return allOptions.filter((option) => selectedNames.includes(option.name));
          };

          const selectedTools = findFullObjects(values.tools, toolOptions);
          const selectedWorkflows = findFullObjects(values.workflows, workflowOptions);
          const selectedKb = findFullObjects(values.kb_names, kbOptions);
          const selectedMcpServers = findFullObjects(values.mcp_servers, mcpServersOptions);
          const selectedPrompts = findFullObjects(values.prompt_names, promptOptions);

          const updatedValues = {
            ...form.getFieldsValue(true),
            ...values,
            tools: selectedTools,
            workflows: selectedWorkflows,
            kb_names: selectedKb,
            mcp_servers: selectedMcpServers,
            prompt_names: selectedPrompts,
            is_active: !!values.is_active,
            long_memory: !!values.long_memory,
            is_published: !!values.is_published,
            updated_by: user_info.username,
          };
          console.log(updatedValues,'updatedValues')
          const endpoint_api = `${AGENT_ENDPOINT}/${id}`;
          const update_response = await updateData(endpoint_api, updatedValues);
          const updated_agent_data = update_response.data || update_response;
          dispatch(updateAgent(updated_agent_data));

          message.success('Agent updated successfully');
          navigate(AGENTS_NAME);
        } catch (error) {
          console.error("Failed to update agent:", error);
          const errorMsg = error.response?.data?.detail || error.message || '请重试';
          message.error(`Failed to update agent: ${errorMsg}`);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  if (initialLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin tip="Loading Agent Data..." size="large" />
      </div>
    );
  }

  return (
    <div className={styles.pageContainer}>
      <div className={styles.headerContainer}>
        <h1 className={styles.pageTitle}>{form.getFieldValue('name')}</h1>
        <SidebarList />
      </div>
      <div className={styles.contentContainer}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className={styles.agentEditForm}
        >
          <div className={styles.leftContainer}>
            <Form.Item
              name="description"
              label="角色与回复逻辑 (System Prompt)"
              rules={[{ required: false }]}
              className={styles.fullWidthItem}
            >
              <TextArea rows={10} placeholder="Define the agent's personality, role, and response guidelines..." />
            </Form.Item>
            <Form.List name="examples" >
                {(fields, { add, remove }) => (
                <>
                    <Divider orientation="left">示例</Divider>
                    {fields.map((field, index) => (
                    <Space key={field.key} className={styles.exampleSpace} align="baseline">
                        <Form.Item
                            key={`${field.key}-text`}
                            name={[field.name, 'text']}
                            label={`${index + 1}`}
                            rules={[{ required: true, message: 'Missing example text' }]}
                            className={styles.exampleTextItem}
                            fieldKey={[field.fieldKey, 'text']}
                        >
                            <TextArea placeholder="Example text" />
                        </Form.Item>
                        <Form.Item
                            key={`${field.key}-conversable`}
                            name={[field.name, 'conversable']}
                            label="Conversable"
                            valuePropName="checked"
                            initialValue={true}
                            className={styles.exampleConversableItem}
                            fieldKey={[field.fieldKey, 'conversable']}
                        >
                            <Switch />
                        </Form.Item>
                        <MinusCircleOutlined onClick={() => remove(field.name)} />
                    </Space>
                    ))}
                    <Form.Item>
                    <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />}>
                      添加示例
                    </Button>
                    </Form.Item>
                </>
                )}
            </Form.List>
          </div>
          <div className={styles.rightContainer}>
            <Form.Item
              name="name"
              label="Agent名称"
              rules={[{ required: true, message: 'Agent名称不能为空!' }]}
            >
              <Input disabled placeholder="Agent name (read-only)" />
            </Form.Item>

            <Form.Item
              name="agent_type"
              label="Agent类型"
              rules={[{ required: true, message: '请选择Agent类型!' }]}
            >
              <Select onChange={handleAgentTypeChange} placeholder="Select agent type">
                {agentTypeOptions.map((agent_type) => (
                  <Option key={agent_type.type} value={agent_type.type}>
                    <Tooltip title={agent_type.description} placement="right">
                      {agent_type.type}
                    </Tooltip>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="llm_name"
              label="大模型"
              rules={[{ required: true, message: '请选择大模型!' }]}
            >
              <Select placeholder="Select Large Language Model">
                {llmOptions.map((llm) => (
                  <Option key={llm.id} value={llm.name}>
                   {llm.name} {llm.enterprise ? `(${llm.enterprise})` : ''}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="prompt_names"
              label="提示词"
              rules={[{ required: false }]}
            >
              <Select 
                mode="multiple" 
                allowClear 
                placeholder="Select prompts (optional)"
                options={promptOptions
                  .filter(prompt => 
                    prompt?.parameters && 
                    Object.keys(prompt.parameters).length > 0
                  )
                  .map(prompt => ({
                    label: `${prompt.name} - ${prompt.task_type}`,
                    value: prompt.name,
                    key: prompt.id
                  }))
                }
              />
            </Form.Item>

            <Form.Item
              name="kb_names"
              label="知识库"
              rules={[{
                  required: selectedAgentType === SEARCH_NAME,
                  message: selectedAgentType === SEARCH_NAME ? 'Search Agent requires a Knowledge Base!' : ''
              }]}
            >
              <Select mode="multiple" allowClear placeholder="Select knowledge bases">
                {kbOptions.map((kb) => (
                  <Option key={kb.id} value={kb.name}>
                    {kb.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="workflows"
              label="工作流"
              rules={[{ required: false }]}
            >
              <Select mode="multiple" allowClear placeholder="Select workflows (optional)">
                {workflowOptions.map((workflow) => (
                  <Option key={workflow.id} value={workflow.name}>
                    {workflow.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="tools"
              label="工具"
              rules={[{ required: false }]}
            >
              <Select mode="multiple" allowClear placeholder="Select tools (optional)">
                {toolOptions.map((tool) => (
                  <Option key={tool.id} value={tool.name}>
                    {tool.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="mcp_servers"
              label="MCP Servers"
              rules={[{ required: false }]}
            >
              <Select mode="multiple" allowClear placeholder="Select MCP Servers (optional)">
                {mcpServersOptions.map((server) => (
                  <Option key={server.id} value={server.name}>
                    {server.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="prologue"
              label="开场白"
              rules={[{ required: false }]}
            >
              <TextArea rows={2} placeholder="e.g., Hello! How can I assist you today?" />
            </Form.Item>

            <Form.Item
              name="is_active"
              label="是否启用"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="long_memory"
              label="是否启用长期记忆"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="is_published"
              label="是否发布"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item className={styles.submitButtonContainer}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className={styles.submitButton}
              >
                保存更改
              </Button>
            </Form.Item>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default AgentEdit;
