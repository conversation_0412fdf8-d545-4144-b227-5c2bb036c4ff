import { createSlice } from '@reduxjs/toolkit';

const agents = [];

const initialState = {
    agents:agents,
    selected_agent_id:null
};

export const agentSlice = createSlice({
    name: 'agent',
    initialState,
    reducers: {
        setSelectedAgentId:(state,action) => {
            state.selected_agent_id = action.payload;
        },
        setAgents: (state, action) => {
            state.agents = action.payload;
            // 更新localStorage中的用户名
        },
        updateSelctedAgent: (state, action) => {
            const { selected_agent_id, ...updatedFields } = action.payload;
            
            // 查找指定的工作流
            const index = state.agents.findIndex(agent => agent.id === selected_agent_id);
            
            if (index !== -1) {
                // 更新已有的工作流，保留现有字段并更新传入的字段
                state.agents[index] = {
                    ...state.agents[index],  // 保留原有的字段
                    ...updatedFields,           // 覆盖传入的字段
                };
            } else {
                // 如果找不到，添加新的工作流
                state.agents.push({
                    id: selected_agent_id,  // 使用 selected_workflow_id 作为 id
                    ...updatedFields,           // 添加所有传入的字段
                });
            }
        },
        
        updateAgent: (state, action) => {
            const { id, ...updatedFields } = action.payload;
            // Find the index of the workflow to update
            const index = state.agents.findIndex(agent => agent.id === id);
        
            if (index !== -1) {
                // Update the workflow at the found index
                state.agents[index] = {
                    ...state.agents[index],
                    ...updatedFields
                };
            } else {
                // Add a new workflow if not found
                state.agents.push({
                    id,
                    ...updatedFields,
                });
            }
        },
    },
});

// Action creators are generated for each case reducer function
export const { setAgents,updateAgent,setSelectedAgentId,updateSelctedAgent } = agentSlice.actions;

export default agentSlice.reducer;
