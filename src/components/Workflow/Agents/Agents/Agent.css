.page-container {
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 12px;
}

.form-container {
  background-color: #fff;
  padding: 10px 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-width: 90%;
  margin: 0 auto;
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-btn-primary {
  width: 100%;
}

.section-divider {
  margin: 24px 0;
}

.ant-select-multiple .ant-select-selection-item {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #0050b3;
}

.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input {
  border-radius: 4px;
}

.ant-input-number {
  width: 100%;
}

.ant-modal-confirm-btns {
  display: flex;
  justify-content: center;
}

.ant-modal-confirm-btns > .ant-btn {
  width: 120px;
  margin: 0 8px;
}

.header-container {
  margin-bottom: 20px;
}

.content-container {
  display: flex;
  flex-direction: column; /* 改为垂直排列 */
  gap: 20px; /* 间距 */
}

.left-container,
.right-container {
  width: 100%;
}

.button-container {
  display: flex;
  justify-content: center; /* 居中对齐 */
  margin-bottom: 4px;
  padding: 0 20px;
}

.custom-button {
  padding: 4px 12px;
  font-size: 14px;
  background-color: #1890ff;
  color: #fff;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.custom-button:hover {
  background-color: #40a9ff;
}

.custom-button:focus {
  background-color: #096dd9;
}

.selected-row {
  background-color: #e6f7ff !important;
}

.ant-table-tbody > tr:hover {
  background-color: #e6f7ff !important;
}

/* Custom style for the table wrapper */
.ant-table-wrapper {
  padding: 0 20px; /* Add padding to left and right */
  overflow: auto; /* Ensure the table wrapper can scroll if needed */
}

.ant-table-cell {
  white-space: normal !important; /* Allow line breaks in table cells */
  word-wrap: break-word; /* Break long words for better adaptability */
}

.link-button {
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: inherit;
}

.link-button:hover {
  color: #40a9ff;
}

.agent-description {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* New CSS for the disabled delete button */
.custom-button.delete-button.disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}
