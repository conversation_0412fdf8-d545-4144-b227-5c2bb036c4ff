import React, { useState, useEffect, useCallback } from 'react';
import { Form, Input, Button, Switch, Select, message, Modal, Spin, Space, Typography, Divider, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  AGENT_ENDPOINT,
  AGENTS_NAME,
  LLM_ENDPOINT,
  KNOWLEDGE_BASE_ENDPOINT,
  WORKFLOW_ENDPOINT,
  TOOL_ENDPOINT,
  MCP_SERVER_ENDPOINT,
  PROMPT_ENDPOINT
} from '../../../Configs/Config';
import { createData, fetchBulk, fetchData } from '../../../Routers/Router';
import SidebarList from '../../../SidebarList';
import styles from './CreateAgent.module.css'; // Import CSS Module
import { useSelector, useDispatch } from 'react-redux';
import { getLlmOptions, getMcpServerOptions } from '../Utils/utils';
import { updateSelctedAgent } from './AgentSlice';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

const CreateAgent = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [llmOptions, setLlmOptions] = useState([]);
  const [agentTypeOptions, setAgentTypeOptions] = useState([]);
  const [initialLoading, setInitialLoading] = useState(true);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  const [kbOptions, setKbOptions] = useState([]);
  const [workflowOptions, setWorkflowOptions] = useState([]);
  const [toolOptions, setToolOptions] = useState([]);
  const [mcpServersOptions, setMcpServersOptions] = useState([]);
  const [promptOptions, setPromptOptions] = useState([]); // New state for prompt options
  const dispatch = useDispatch();

  useEffect(() => {
    let isMounted = true;

    const fetchInitialData = async () => {
      try {
        const llmOpts = getLlmOptions(LLM_ENDPOINT, user_info.username);
        const agentTypeOpts = fetchData(`${AGENT_ENDPOINT}/agent_types`);
        const workflowOpts = fetchBulk(`${WORKFLOW_ENDPOINT}/bulk`,{username:user_info.username});
        const toolOpts = fetchBulk(`${TOOL_ENDPOINT}/bulk`,{username:user_info.username});
        const mcpServerOpts = fetchBulk(`${MCP_SERVER_ENDPOINT}/bulk`, {username:user_info.username,mcp_type:"tool"});
        const kbOpts = getLlmOptions(KNOWLEDGE_BASE_ENDPOINT, user_info.username);
        const promptOpts = fetchBulk(`${PROMPT_ENDPOINT}/bulk`,{username:user_info.username}); // Fetch prompt options

        const [
          llmResult,
          agentTypeResult,
          workflowResult,
          toolResult,
          mcpServerResult,
          kbResult,
          promptResult,
        ] = await Promise.all([
          llmOpts,
          agentTypeOpts,
          workflowOpts,
          toolOpts,
          mcpServerOpts,
          kbOpts,
          promptOpts,
        ]);

        if (isMounted) {
          setLlmOptions(llmResult);
          setAgentTypeOptions(agentTypeResult.data || []);
          setWorkflowOptions(workflowResult.data || []);
          setToolOptions(toolResult.data || []);
          setMcpServersOptions(mcpServerResult.data || []);
          setKbOptions(kbResult || []);
          setPromptOptions(promptResult.data || []); // Set prompt options
          setInitialLoading(false);
        }
      } catch (error) {
        console.error("Failed to fetch initial data:", error);
        if (isMounted) {
            message.error(`Failed to load initial options: ${error.message}`);
            setInitialLoading(false);
        }
      }
    };

    fetchInitialData();

    return () => {
        isMounted = false;
    };

  }, [user_info.username]);

  const onFinish = async (values) => {
    Modal.confirm({
      title: '确认创建',
      content: '您确定要创建这个Agent吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const findFullObjects = (selectedNames, allOptions) => {
            if (!selectedNames || !Array.isArray(selectedNames)) return [];
            return allOptions.filter((option) => selectedNames.includes(option.name));
          };

          const selectedTools = findFullObjects(values.tools, toolOptions);
          const selectedWorkflows = findFullObjects(values.workflows, workflowOptions);
          const selectedKb = findFullObjects(values.kb_names, kbOptions);
          const selectedMcpServers = findFullObjects(values.mcp_servers, mcpServersOptions);
          const selectedPrompts = findFullObjects(values.prompt_names, promptOptions); // Process selected prompts

          const updatedValues = {
            ...values,
            tools: selectedTools,
            workflows: selectedWorkflows,
            kb_names: selectedKb,
            mcp_servers: selectedMcpServers,
            prompt_names: selectedPrompts, // Add selected prompts to form data
            is_active: values.is_active === undefined ? true : !!values.is_active,
            long_memory: values.long_memory === undefined ? false : !!values.long_memory,
            is_published: values.is_published === undefined ? false : !!values.is_published,
          };

          const endpoint_api = `${AGENT_ENDPOINT}/`;
          const new_agent = await createData(endpoint_api, updatedValues);
          dispatch(updateSelctedAgent(new_agent));
          message.success('Agent创建成功');
          form.resetFields();
          navigate(AGENTS_NAME);
        } catch (error) {
          console.error("Agent creation failed:", error);
          const errorMsg = error.response?.data?.detail || error.message || '请重试';
          message.error(`创建Agent失败: ${errorMsg}`);
        } finally {
          setLoading(false);
        }
      },
    });
  };


  if (initialLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin tip="Loading Initial Options..." size="large" />
      </div>
    );
  }

  return (
    <div className={styles.pageContainer}>
      <div className={styles.headerContainer}>
        <h1 className={styles.pageTitle}>创建Agent</h1>
        <SidebarList />
      </div>
      <div className={styles.contentContainer}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className={styles.agentFormContainer}
          initialValues={{
            is_active: true,
            long_memory: false,
            is_published: false,
          }}
        >
          <div className={styles.leftContainer}>
            <Form.Item
              name="description"
              label="角色与回复逻辑 (System Prompt)"
              rules={[{ required: false, message: '请输入系统角色!' }]}
            >
              <TextArea rows={10} placeholder="Define the agent's personality, role, and response guidelines..." />
            </Form.Item>
            <Form.List name="examples" >
                {(fields, { add, remove }) => (
                <>
                    <Divider orientation="left">示例</Divider>
                    {fields.map((field, index) => (
                    <Space key={field.key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                        <Form.Item
                            {...field}
                            name={[field.name, 'text']}
                            label={`${index + 1}`}
                            rules={[{ required: true, message: 'Missing example text' }]}
                        >
                            <TextArea placeholder="Example text" />
                        </Form.Item>
                        <Form.Item
                            {...field}
                            name={[field.name, 'conversable']}
                            label="Conversable"
                            valuePropName="checked"
                            initialValue={true}
                        >
                            <Switch />
                        </Form.Item>
                        <MinusCircleOutlined onClick={() => remove(field.name)} />
                    </Space>
                    ))}
                    <Form.Item>
                    <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />}>
                        添加示例
                    </Button>
                    </Form.Item>
                </>
                )}
            </Form.List>
          </div>
          <div className={styles.rightContainer}>
            <Form.Item
              name="name"
              label="Agent名称"
              rules={[
                { required: true, message: '请输入Agent名称!' },
                { whitespace: true, message: 'Agent名称不能为空格!' },
                { max: 100, message: 'Agent名称不能超过100个字符!' }
              ]}
            >
              <Input placeholder="e.g., Customer Support Assistant" />
            </Form.Item>

            <Form.Item
              name="agent_type"
              label="Agent类型"
              rules={[{ required: true, message: '请选择Agent类型!' }]}
            >
              <Select placeholder="Select agent type">
                {agentTypeOptions.map((agent_type) => (
                  <Option key={agent_type.type} value={agent_type.type}>
                    <Tooltip title={agent_type.description} placement="right">
                      {agent_type.type}
                    </Tooltip>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="llm_name"
              label="大模型"
              rules={[{ required: true, message: '请选择大模型!' }]}
            >
              <Select placeholder="Select Large Language Model">
                {llmOptions.map((llm) => (
                  <Option key={llm.id} value={llm.name}>
                    {llm.name} {llm.enterprise ? `(${llm.enterprise})` : ''}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
            name="prompt_names"
            label="提示词"
            rules={[{ required: false }]}
          >
            <Select mode="multiple" allowClear placeholder="Select prompts (optional)">
              {promptOptions
                .filter(prompt => 
                  prompt?.parameters && 
                  typeof prompt.parameters === 'object' && 
                  Object.keys(prompt.parameters).length > 0
                )
                .map((prompt) => (
                  <Option key={prompt.id} value={prompt.name}>
                    {prompt.name} - {prompt.task_type}
                  </Option>
                ))
              }
            </Select>
          </Form.Item>

            <Form.Item
              name="kb_names"
              label="知识库"
              rules={[{ required: false }]}
            >
              <Select mode="multiple" allowClear placeholder="Select knowledge bases (optional)">
                {kbOptions.map((kb) => (
                  <Option key={kb.id} value={kb.name}>
                    {kb.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="workflows"
              label="工作流"
              rules={[{ required: false }]}
            >
              <Select mode="multiple" allowClear placeholder="Select workflows (optional)">
                {workflowOptions.map((workflow) => (
                  <Option key={workflow.id} value={workflow.name}>
                    {workflow.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="tools"
              label="工具"
              rules={[{ required: false }]}
            >
              <Select mode="multiple" allowClear placeholder="Select tools (optional)">
                {toolOptions.map((tool) => (
                  <Option key={tool.id} value={tool.name}>
                    {tool.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="mcp_servers"
              label="MCP Servers"
              rules={[{ required: false }]}
            >
              <Select mode="multiple" allowClear placeholder="Select MCP Servers (optional)">
                {mcpServersOptions.map((server) => (
                  <Option key={server.id} value={server.name}>
                    {server.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="prologue"
              label="开场白"
              rules={[{ required: false, message: '请输入开场白!' }]}
            >
              <TextArea rows={2} placeholder="e.g., Hello! How can I assist you today?" />
            </Form.Item>

            <Form.Item
              name="is_active"
              label="是否启用"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="long_memory"
              label="是否启用长期记忆"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="is_published"
              label="是否发布"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} style={{ width: '100%' }}>
                创建Agent
              </Button>
            </Form.Item>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default CreateAgent;
