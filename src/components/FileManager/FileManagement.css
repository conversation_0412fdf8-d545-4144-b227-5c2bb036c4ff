/* FileManagement.css */

.table-container {
    padding: 20px;
    position: relative;
  }
  
  .action-buttons {
    display: flex;
    gap: 10px;
  }
  
  .modal-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .tooltip-content {
    max-height: 150px;
    overflow-y: auto;
  }
  
  .tooltip-content-large {
    max-height: 400px;
    overflow-y: auto;
  }
  
  .new-file-button {
    position: absolute;
    /* bottom: 20px;
    right: 20px; */
    z-index: 1000;
  }
  
  .button-container {
    margin-bottom: 20px; /* 按钮组下方的间距 */
    display: flex;
    gap: 10px; /* 按钮和选中计数器之间的间距 */
  }

.action-buttons {
  display: flex;
  gap: 10px;
}

.selected-count {
  margin-top: 10px; /* 在按钮组下方添加间距 */
  font-size: 14px; /* 控制字体大小 */
  color: #555; /* 文字颜色 */
}
.total-number {
  font-size: 16px; /* 字体大小 */
  color: #333; /* 字体颜色 */
  margin-bottom: 15px; /* 与下方内容的间距 */
  font-weight: bold; /* 字体加粗 */
  padding-left: 20px;
}
.blue-text {
  color: blue;
  font-weight: bold;
}