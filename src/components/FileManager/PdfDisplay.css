/* 按钮容器的样式 */
.button-container-display {
  position: fixed;
  right: 19%;
  top: 20px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1004;
  padding: 5px 10px;
}

/* 按钮的通用样式 */
.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
  z-index: 1001;
}

.icon-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.icon-button:last-child {
  margin-bottom: 0;
}

/* 文本区域的样式 */
.editable-textarea {
  width: calc(100% - 12px);
  font-size: 12px;
  margin-bottom: 10px;
  resize: none;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

/* 图片和表格容器的样式 */
.figure-container {
  position: relative;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 图片容器样式 */
.image-container {
  width: 50%; /* 将容器宽度设置为50% */
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

/* 图片内容样式 */
.content-image {
  width: 100%;
  height: auto;
  display: block;
  object-fit: contain;
}

/* 删除图标的样式 */
.delete-icon {
  position: absolute;
  right: 5px;
  top: 5px;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  cursor: pointer;
  color: red;
  padding: 5px;
  border-radius: 50%;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.3s ease;
  z-index: 1002;
}

.delete-icon:hover {
  background-color: rgba(255, 0, 0, 0.1);
}

/* 使内容避免被按钮遮挡的样式 */
.pdf-display-content {
  padding-right: 80px;
}

/* 包装器的样式 */
.pdf-display-wrapper {
  position: relative;
  z-index: 1;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

/* 标签选择器和删除按钮的容器样式 */
.label-delete-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  justify-content: space-between;
}

/* 删除 caption 输入框的按钮样式 */
.delete-caption-button {
  background-color: #f44336;
  color: white;
  font-size: 14px;
  border: none;
  border-radius: 50%;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 1003;
  margin-right: 5px;
}

.delete-caption-button:hover {
  background-color: #e53935;
}

/* 标签选择器的样式 */
.label-select {
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 4px;
  background-color: #ffffff;
  border: 1px solid #ddd;
  width: 60px;
  z-index: 1003;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  margin-left: 0;
  margin-right: 10px;
}

/* 新增的添加 caption 按钮的样式 */
.add-caption-button {
  background-color: #4caf50;
  color: white;
  font-size: 16px;
  border: none;
  border-radius: 50%;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 1003;
  margin-right: 5px;
}

.add-caption-button:hover {
  background-color: #45a049;
}

/* 标签显示的样式 */
.label-display {
  display: none;
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 12px;
  padding: 2px 4px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 4px;
  z-index: 1002;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 当编辑状态时显示标签 */
.is-editing .label-display {
  display: block;
}

/* Markdown 预览样式 */
.markdown-preview, .markdown-paragraph, .markdown-h1, .markdown-h2, .markdown-h3 {
  max-width: 100%;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
}

.preview-title {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

/* 防止 Katex 数学公式溢出页面宽度 */
.katex-display {
  overflow-x: auto;
  white-space: normal;
  max-width: 100%;
}
/* 为表格启用水平滚动 */
.table-container {
  overflow-x: auto;
  width: 100%;
}
.markdown-code {
  max-width: 100%;
  overflow-x: auto;
  word-wrap: break-word;
}

.katex {
  font-size: 1.1em;
}
