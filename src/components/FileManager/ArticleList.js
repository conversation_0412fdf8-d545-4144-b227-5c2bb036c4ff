import React, { useState, useEffect, useRef } from 'react';
import { Checkbox, List, Pagination, Empty, Input, message, Select } from 'antd';
import './ArticleList.css';
import { fetchBulk } from '../Routers/Router';
import { FILE_MANAGER_ENDPOINT, KNOWLEDGE_BASE_ENDPOINT } from '../Configs/Config';
import { useDispatch, useSelector } from 'react-redux'; 
import { updateSelectedDocs, clearSelectedDocs, setSelectedDocs as reduxSetSelectedDocs } from '../Dashboard/dashboardSlice'; 
import LoadingSpinner from '../Dashboard/loadingSpinner';

const { Option } = Select;

const ArticleList = ({ sortField = 'updated_at', sortOrder = 'desc', pageSize = 10 }) => {
    const { selected_docs, isChat } = useSelector((state) => state.dashboard); // Get selected docs and isChat from Redux
    const [data, setData] = useState([]);
    const [kbData, setKbData] = useState([]); // Knowledge base data
    const [selectedDocs, setSelectedDocs] = useState(selected_docs || []); // Set default value of selectedDocs to selected_docs from Redux
    const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState(null); // Store selected knowledge base
    const [filterLoading, setFilterLoading] = useState(true);
    const [total, setTotal] = useState(0); // Total data count
    const [currentPage, setCurrentPage] = useState(1); // Current page for articles
    const [searchKeyword, setSearchKeyword] = useState(''); // Search keyword state
    const maxSelections = 5; // Define the maximum number of selectable articles
    const [is_open, setIsOpen] = useState(false); // For toggling the sidebar open/close
    const articleListRef = useRef(null); // Ref for the article drawer container
    const dispatch = useDispatch(); // Initialize dispatch
    const { user_info } = useSelector((state) => state.user);

    // Function to toggle the drawer
    const toggleDrawer = () => {
        if (isChat) {
            // 如果是会话模式，显示警告提示
            message.warning('会话中，不能查看文章');
        } else {
            // 切换抽屉状态
            setIsOpen(!is_open);
        }
    };

    // Function to fetch articles with loading spinner management
    const fetchArticles = async () => {
        const skip = (currentPage - 1) * pageSize;
        setFilterLoading(true); // Start showing loading spinner
        try {
            let endpoint_api = `${FILE_MANAGER_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&skip=${skip}&limit=${pageSize}&username=${user_info.username}`;
            if (selectedKnowledgeBase) {
                endpoint_api += `&kb_id=${selectedKnowledgeBase}`;
            }
            if (searchKeyword) {
                endpoint_api += `&name__contains=${searchKeyword}`;
            }

            const result = await fetchBulk(endpoint_api);
            setData(result.data); // Set the fetched data
            setTotal(result.count); // Set total count
            setSelectedDocs([]); // Clear selected documents
            dispatch(clearSelectedDocs()); // Clear selected docs in Redux
        } catch (error) {
            message.error('Failed to fetch articles');
        } finally {
            setFilterLoading(false); // Hide loading spinner once data is fetched or an error occurs
        }
    };

    // Fetch knowledge base data for the Select component
    const fetchKnowledgeBases = async () => {
        try {
            const result = await fetchBulk(`${KNOWLEDGE_BASE_ENDPOINT}/bulk?username=${user_info.username}`);
            setKbData(result.data);
        } catch (error) {
            message.error('Failed to fetch knowledge bases');
        }
    };

    // Handle checkbox state change
    const handleCheckboxChange = (id, doc_id, title) => {
        const isSelected = selectedDocs.some((doc) => doc.id === id);
        const docInfo = { id: id, doc_id: doc_id, title: title };
        
        if (isSelected) {
            // Remove from selectedDocs and update Redux
            const updatedSelectedDocs = selectedDocs.filter((doc) => doc.id !== id);
            setSelectedDocs(updatedSelectedDocs);
            dispatch(reduxSetSelectedDocs({ selectedDocs: updatedSelectedDocs })); // Dispatch the updated docs to Redux
        } else if (selectedDocs.length < maxSelections) {
            // Add the docInfo to selectedDocs and update Redux
            const updatedSelectedDocs = [...selectedDocs, docInfo];
            setSelectedDocs(updatedSelectedDocs);
            dispatch(updateSelectedDocs(docInfo)); // Dispatch the updated docs to Redux
        } else {
            message.warning(`You can only select up to ${maxSelections} articles.`);
        }
    };
    
    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handleKnowledgeBaseChange = (value) => {
        setSelectedKnowledgeBase(value);
        setCurrentPage(1); // Reset to first page when changing knowledge base
        setSelectedDocs([]); // Clear selected documents locally
        dispatch(clearSelectedDocs()); // Clear selected docs in Redux
    };

    useEffect(() => {
        // Fetch articles whenever selectedKnowledgeBase, currentPage, or searchKeyword changes
        fetchArticles();
    }, [selectedKnowledgeBase, currentPage, searchKeyword]);

    useEffect(() => {
        // Fetch knowledge bases on mount
        fetchKnowledgeBases();
    }, []);

    useEffect(() => {
        // Close the drawer when 'Esc' is pressed
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                setIsOpen(false);
            }
        };

        // Detect click outside of the drawer, but ignore clicks on the Select dropdown
        const handleClickOutside = (e) => {
            const selectDropdown = document.querySelector('.ant-select-dropdown');
            if (
                articleListRef.current && !articleListRef.current.contains(e.target) &&
                (!selectDropdown || !selectDropdown.contains(e.target)) // Check if click is inside Select dropdown
            ) {
                setIsOpen(false);
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('mousedown', handleClickOutside);

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Sync local state with Redux selected_docs
    useEffect(() => {
        setSelectedDocs(selected_docs); // Sync selectedDocs with Redux state
    }, [selected_docs]);

    return (
        <div ref={articleListRef} className={`article_drawer ${is_open ? "open" : ""}`}>
            <div 
                className={`article_header ${is_open ? "" : "vertical-text"}`} 
                onClick={toggleDrawer} // Only triggers onClick
            >
                挑选文章
            </div>
            {is_open && (
                <div className="drawer_content">
                    <div className="search-bar-container">
                        <Select
                            placeholder="选择知识库"
                            onChange={handleKnowledgeBaseChange}
                            allowClear
                            style={{ width: 200, marginRight: '16px' }}
                            getPopupContainer={(trigger) => trigger.parentNode}
                        >
                            {kbData.map((kb) => (
                                <Option key={kb.id} value={kb.id}>
                                    {kb.name}
                                </Option>
                            ))}
                        </Select>
                        <Input.Search
                            placeholder="搜索文章"
                            allowClear
                            onSearch={handleSearch}
                            style={{ flex: 1 }}
                        />
                    </div>
                    {filterLoading ? (
                        <LoadingSpinner />
                    ) : data.length > 0 ? (
                        <>
                            <List
                                itemLayout="horizontal"
                                dataSource={data}
                                renderItem={(article) => (
                                    <List.Item>
                                        <Checkbox
                                            checked={selectedDocs.some((doc) => doc.id === article.id)}
                                            onChange={() => handleCheckboxChange(article.id, article.data_id, article.name)}
                                        >
                                            {article.name}
                                        </Checkbox>
                                    </List.Item>
                                )}
                            />
                            <Pagination
                                className="custom-pagination"
                                current={currentPage}
                                total={total}
                                pageSize={pageSize}
                                onChange={handlePageChange}
                            />
                        </>
                    ) : (
                        <Empty description="No articles found" />
                    )}
                </div>
            )}
        </div>
    );
};

export default ArticleList;
