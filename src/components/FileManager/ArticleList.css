/* ArticleList Base Styles */
.article_drawer {
    position: fixed;
    right: 0px;
    bottom: 20%; /* Ensure the drawer does not extend beyond the bottom of the viewport */
    width: 28px; /* Width when closed */
    z-index: 1000; /* Ensure it appears above other elements */
    height: 20px; /* Allow height to adjust with content */
    background-color: #fff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease, transform 0.3s ease; /* Smooth transitions for width and transform */
}

.article_drawer.open {
    transform: translateX(0);  /* Move into view when open */
    width: 450px; /* Width when open */
    top: 10px; /* Position the drawer 10px from the top of the viewport */
    z-index: 1005; /* Ensure it appears above other elements */
}

.article_header {
    padding: 10px;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease, background-color 0.3s ease;
    height: 10px;
}

.article_header.vertical-text {
    writing-mode: vertical-lr;  /* Vertical text orientation */
    justify-content: center;
    align-items: center;
    height: 80px;
    padding-left: 1px;
    padding-right: 1px;
    position: fixed; /* Ensure the header stays in a fixed position */
    right: 0px; /* Align the header 10px from the right edge */
    bottom: 20%; /* Adjust the top position as needed */
    width: 28px;
    font-size: 18px;
    font-weight: bold;
    background-color: #007bff; /* Added background color for visibility */
    color: white; /* Added text color */
    z-index: 1001; /* Ensure it appears above other elements */
}

.article_drawer.open .article_header {
    text-align: center;
    height: 10px;
    width: auto;
    justify-content: center;
    transform: none;
    align-items: center;
}

.drawer_content {
    position: relative; /* To position the search bar inside */
    padding: 10px;
    height: 90vh;
    display: flex;
    flex-direction: column; /* Flex layout to manage positioning */
    overflow-y: auto; /* Enable vertical scrolling if content exceeds max height */
    background-color: aliceblue;
}

/* Search box styling */
.search-bar-container {
    display: flex;
    justify-content: space-between; /* Space between the search bar and the knowledge base picker */
    align-items: center;
    /* padding: 10px 0; */
    /* width:100px; */
}

.search-box {
    flex: 1;
    margin-left: 16px; /* Add space between the Select and Input */
}

/* Checkbox List Styling */
.ant-checkbox-wrapper {
    font-size: 16px;
    color: #333;
    cursor: pointer;
    padding: 5px 0;
}

/* Selection Info Section */
.selection-info {
    margin-top: auto; /* Pushes the selection info to the bottom */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    background-color: #f0f0f0; /* Add a slight background color to highlight the section */
}

.selection-info span {
    font-size: 14px;
    color: #888;
}

.custom-pagination {
    text-align: center;
    font-size:14px;
}

/* Additional styles for better user experience */
.ant-select-selector {
    height: 36px; /* Adjust the height of the Select component to match the search bar */
    line-height: 36px;
}

.ant-input {
    height: 36px; /* Adjust the height of the Input component for consistency */
    line-height: 36px;
}

.drawer_content .ant-input-search-button {
    height: 36px; /* Ensure the search button height matches the input */
}

/* Add some padding to the bottom of the content to make sure the last items aren't hidden */
.drawer_content {
    padding-bottom: 16px;
}

/* When the drawer is closed, reduce the height to 20px */
.article_drawer:not(.open) {
    height: 20px;
}
