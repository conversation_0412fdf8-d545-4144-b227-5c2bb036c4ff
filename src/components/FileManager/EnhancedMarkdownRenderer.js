import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import remarkGfm from 'remark-gfm';
import 'katex/dist/katex.min.css';

const EnhancedMarkdownRenderer = ({ children }) => {
  // Pre-process LaTeX-style content
  const processContent = (content) => {
    if (typeof content !== 'string') return content;
    
    // Replace LaTeX-style commands with proper LaTeX math mode
    return content.replace(/\\textless/g, '$<$')
                 .replace(/\\textgreater/g, '$>$')
                 .replace(/\\([a-zA-Z]+)/g, '$$\\$1$$')
                 .replace(/(\d+)\\([a-zA-Z]+)/g, '$$$1\\$2$$');
  };

  const customComponents = {
    p: ({ node, children }) => <p className="markdown-paragraph">{children}</p>,
    h1: ({ node, children }) => <h1 className="markdown-h1">{children}</h1>,
    h2: ({ node, children }) => <h2 className="markdown-h2">{children}</h2>,
    h3: ({ node, children }) => <h3 className="markdown-h3">{children}</h3>,
    table: ({ node, children }) => (
      <div className="table-container">
        <table className="markdown-table">{children}</table>
      </div>
    ),
    th: ({ node, children }) => <th className="markdown-th">{children}</th>,
    td: ({ node, children }) => <td className="markdown-td">{children}</td>,
    code: ({ node, inline, children, ...props }) => {
      // Handle inline LaTeX within code blocks
      if (inline && typeof children[0] === 'string' && children[0].startsWith('\\')) {
        return <span className="latex-inline">{`$${children}$`}</span>;
      }
      return <code {...props}>{children}</code>;
    }
  };

  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath, remarkGfm]}
      rehypePlugins={[rehypeKatex]}
      components={customComponents}
    >
      {processContent(children)}
    </ReactMarkdown>
  );
};

export default EnhancedMarkdownRenderer;