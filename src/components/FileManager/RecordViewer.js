import React, { useState, useEffect } from 'react';
import { List, Tooltip } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import PdfDisplay from './PdfDisplay'; 
import './RecordViewer.css'; // Import the CSS file
import PDFViewer from './PDFViewer';
import SidebarList from '../SidebarList';

const RecordViewer = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { record, allRecords } = location.state;
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentRecord, setCurrentRecord] = useState(record);

  useEffect(() => {
    const recordIndex = allRecords.findIndex(
      (rec) => rec.id === record.id
    );
    setCurrentIndex(recordIndex);
    setCurrentRecord(record); // Ensure the current record is set initially
  }, [record, allRecords]);

  const navigateToRecord = (index) => {
    const rec = allRecords[index];
    setCurrentIndex(index);
    setCurrentRecord(rec);
    // Update the URL without reloading the page or losing state
    navigate(`/record-viewer/${rec.id}`, { state: { record: rec, allRecords } });
  };

  return (
    <div className="record-viewer-container">
      <div className="article-list">
        <h3>文章列表</h3>
        <SidebarList />
        <List
          dataSource={allRecords}
          renderItem={(item, index) => (
            <Tooltip title={item.name} key={item.id}>
              <List.Item
                style={{ cursor: 'pointer', background: index === currentIndex ? '#e6f7ff' : '#fff' }}
                onClick={() => navigateToRecord(index)}
              >
                {item.name.length > 40 ? `${item.name.substring(0, 40)}...` : item.name}
              </List.Item>
            </Tooltip>
          )}
        />
      </div>

      <div className="pdf-preview">
        <PDFViewer pdfUrl={currentRecord.url} /> {/* Embed the PDFViewer component */}
      </div>

      <div className="pdf-display">
        <PdfDisplay key={currentRecord.id} data={currentRecord} /> {/* Ensure component re-renders on record change */}
      </div>
    </div>
  );
};

export default RecordViewer;
