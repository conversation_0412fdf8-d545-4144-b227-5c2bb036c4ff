import React, { useEffect, useState } from 'react';
import { Table, Button, Modal, Form, Input, message, Tooltip, Drawer } from 'antd';
import { fetchFiles, deleteFile, deleteBulkFiles, createFile, updateFile } from './apiUtil';
import './FileManagement.css';  // 导入自定义的 CSS 文件

const { confirm } = Modal;

const FileManagement = () => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingFile, setEditingFile] = useState(null);
  const [drawerVisible, setDrawerVisible] = useState(false);

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    setLoading(true);
    try {
      const data = await fetchFiles();
      setFiles(data.data);
    } catch (error) {
      message.error('Failed to fetch files.');
    }
    setLoading(false);
  };

  const handleDelete = (fileId) => {
    confirm({
      title: 'Are you sure you want to delete this file?',
      onOk: async () => {
        try {
          await deleteFile(fileId);
          message.success('File deleted successfully');
          loadFiles();
        } catch (error) {
          message.error('Failed to delete file.');
        }
      },
    });
  };

  const handleBulkDelete = (selectedRowKeys) => {
    confirm({
      title: 'Are you sure you want to delete selected files?',
      onOk: async () => {
        try {
          await deleteBulkFiles(selectedRowKeys);
          message.success('Files deleted successfully');
          loadFiles();
        } catch (error) {
          message.error('Failed to delete files.');
        }
      },
    });
  };

  const handleEdit = (file) => {
    setEditingFile(file);
    setIsModalVisible(true);
    form.setFieldsValue(file);
  };

  const handleCreate = () => {
    setEditingFile(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleFormSubmit = async (values) => {
    try {
      if (editingFile) {
        await updateFile(editingFile.id, values);
        message.success('File updated successfully');
      } else {
        await createFile(values);
        message.success('File created successfully');
      }
      loadFiles();
      setIsModalVisible(false);
    } catch (error) {
      message.error('Failed to save file.');
    }
  };

  const handleRowClick = (record) => {
    setEditingFile(record);
    setDrawerVisible(true);
    form.setFieldsValue(record);
  };

  const handleDrawerClose = () => {
    setDrawerVisible(false);
    setEditingFile(null);
  };

  const handleDrawerSubmit = async (values) => {
    try {
      await updateFile(editingFile.id, values);
      message.success('File updated successfully');
      loadFiles();
      setDrawerVisible(false);
    } catch (error) {
      message.error('Failed to update file.');
    }
  };

  const columns = [
    { title: '文件标题', dataIndex: 'name', key: 'name' },
    { title: '用户', dataIndex: 'username', key: 'username' },
    { 
      title: '文件链接', dataIndex: 'url', key: 'url',
      render: (text) => (
        <Tooltip
          title={
            <div className="tooltip-content">
              {text}
            </div>
          }
        >
          {text.length > 50 ? `${text.slice(0, 50)}...` : text}
        </Tooltip>
      ),
     },
     {
       title: '内容', dataIndex: 'content', key: 'content',
       render: (text) => (
        <Tooltip
          title={
            <div className="tooltip-content-large">
              {text}
            </div>
          }
        >
          {text.length > 50 ? `${text.slice(0, 50)}...` : text}
        </Tooltip>
      ),
      },
     { title: '描述', dataIndex: 'description', key: 'description' },
     { title: '标签', dataIndex: 'label', key: 'label' },
     { title: '创建时间', dataIndex: 'create_time', key: 'create_time' },
     { title: '更新时间', dataIndex: 'update_time', key: 'update_time' },
      {
        title: '操作',
        key: 'actions',
        render: (text, record) => (
          <>
            {/* <Button onClick={() => handleEdit(record)}>Edit</Button> */}
            <Button danger onClick={() => handleDelete(record.id)}>Delete</Button>
          </>
        ),
      },
  ];

  return (
    <div className="table-container">
      <div className="button-container">
        <Button
          type="primary"
          className="new-file-button"
          onClick={handleCreate}
        >
          新增文件
        </Button>
      </div>
      <Table
        rowKey="id"
        columns={columns}
        dataSource={files}
        loading={loading}
        rowSelection={{
          onChange: handleBulkDelete,
        }}
        onRow={(record) => {
          return {
            onClick: () => handleRowClick(record),
          };
        }}
      />
      <Modal
        title={editingFile ? '修改文件' : '新增文件'}
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} onFinish={handleFormSubmit}>
          <Form.Item name="name" label="文件标题" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label="描述" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="url" label="文件链接" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        </Form>
      </Modal>
      <Drawer
        title="文件详情"
        width={600}
        onClose={handleDrawerClose}
        visible={drawerVisible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <Form form={form} layout="vertical" onFinish={handleDrawerSubmit}>
          <Form.Item name="name" label="文件标题" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="content" label="内容" rules={[{ required: true }]}>
            <Input.TextArea rows={15} />
          </Form.Item>
          <Form.Item name="url" label="文件链接" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="label" label="标签" rules={[{ required: false }]}>
            <Input />
          </Form.Item>
          <Form.Item name="description" label="描述" rules={[{ required: false }]}>
            <Input />
          </Form.Item>
          <div style={{ textAlign: 'right' }}>
            <Button onClick={handleDrawerClose} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={() => form.submit()} type="primary">
              保存
            </Button>
          </div>
        </Form>
      </Drawer>
    </div>
  );
};

export default FileManagement;
