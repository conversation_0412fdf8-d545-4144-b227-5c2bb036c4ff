.record-viewer-container {
    display: flex;
    height: 100vh;
  }
  
  .article-list {
    flex: 0 0 10%; /* 10% of the width */
    overflow-y: auto;
    padding: 10px;
    border-right: 1px solid #ccc;
    box-sizing: border-box;
  }
  
  .pdf-preview {
    flex: 0 0 45%; /* 45% of the width */
    overflow-y: auto;
    padding: 10px;
    border-right: 1px solid #ccc;
    box-sizing: border-box;
  }
  
  .pdf-display {
    flex: 0 0 45%; /* 占据宽度的 45% */
    overflow-y: auto;
    padding: 10px 20px; /* 上下10px，左右20px */
    box-sizing: border-box; /* 确保 padding 包含在元素的宽度和高度内 */
  }
  
  
  .navigation-buttons {
    margin-top: 20px;
    text-align: center;
  }
  