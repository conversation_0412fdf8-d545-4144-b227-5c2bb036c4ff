import React from 'react';
import { <PERSON><PERSON>, Switch, Tooltip, Popconfirm } from 'antd';
import { useNavigate } from 'react-router-dom';
import './FileManager.css';

const Columns = ({
  data,
  substring,
  user_info,
  SUPERUSER,
  handleIsPublishedChange,
  handleDelete,
  getKnowledgeBaseName
}) => {
  const navigate = useNavigate();

  return [
    {
      title: '索引',
      dataIndex: 'index',
      key: 'index',
      render: (text, record, index) => index + 1,
    },
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      render: (text, record) => (
        <button
          onClick={() => navigate(`/record-viewer/${record.id}`, { state: { record, allRecords: data } })}
          className={`link-button ${user_info.username !== record.username ? 'disabled' : ''}`}
          disabled={user_info.username !== record.username}
        >
          <Tooltip title={text}>
            <span>{text.length > substring ? `${text.substring(0, substring)}...` : text}</span>
          </Tooltip>
        </button>
      ),
    },
    {
      title: '类型',
      dataIndex: 'chunk_type',
      key: 'chunk_type',
      sorter: true,
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      sorter: true,
      render: (text) => (
        <Tooltip title={text}>
          <span>{text.length > substring ? `${text.substring(0, substring)}...` : text}</span>
        </Tooltip>
      ),
    },
    {
      title: '知识库',
      dataIndex: 'kb_id',
      key: 'kb_id',
      sorter: true,
      render: (text) => (
        <Tooltip title={getKnowledgeBaseName(text)}>
          <span>{getKnowledgeBaseName(text).length > substring ? `${getKnowledgeBaseName(text).substring(0, substring)}...` : getKnowledgeBaseName(text)}</span>
        </Tooltip>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'username',
      key: 'username',
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      sorter: true,
    },
    {
      title: '发布',
      dataIndex: 'is_published',
      key: 'is_published',
      sorter: true,
      render: (text, record) => {
        const isSuperUser = user_info.role === SUPERUSER;
        const isOwner = record.username === user_info.username;

        const isGrey = (isSuperUser && !isOwner);

        return (
          <Switch
            checked={record.is_published}
            onChange={(checked) => {
              if (!isGrey) {
                handleIsPublishedChange(record, checked);
              }
            }}
            disabled={isGrey}
            className={isGrey ? 'switch-grey' : ''}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => {
        const canDelete = record.username === user_info.username;
        return (
          <div style={{ display: 'flex', justifyContent: 'space-around' }}>
            <Button
              type="link"
              onClick={() => navigate(`/record-viewer/${record.id}`, { state: { record, allRecords: data } })}
              disabled={!canDelete}
            >
              参考原文
            </Button>
            <Popconfirm
              title="确定要删除这个文件吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                disabled={!canDelete}
                style={{ opacity: canDelete ? 1 : 0.4 }}
              >
                删除
              </Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];
};

export default Columns;
