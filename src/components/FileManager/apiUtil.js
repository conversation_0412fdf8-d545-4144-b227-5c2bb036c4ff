import { FETCH_FILES_ENDPOINT } from '../Configs/Config'

const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token

export const fetchFiles = async () => {
    const url = `${FETCH_FILES_ENDPOINT}/`;  // 使用从配置文件导入的基础 URL
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Failed to fetch files:', error);
      throw new Error('Failed to fetch files');
    }
  };
  

export const deleteFile = async (fileId) => {
    const url = `${FETCH_FILES_ENDPOINT}/${fileId}`;  // 使用从配置文件导入的基础 URL
    try {
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw new Error('Failed to delete file');
    }
  };
  
export  const deleteBulkFiles = async (fileIds) => {
    const url = `${FETCH_FILES_ENDPOINT}/files_by_ids`;  // 使用从配置文件导入的基础 URL
    try {
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
        body: JSON.stringify({ file_ids: fileIds }),
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to delete files:', error);
      throw new Error('Failed to delete files');
    }
  };
  
export const createFile = async (file_data) => {
    const url = `${FETCH_FILES_ENDPOINT}/`;  // 使用从配置文件导入的基础 URL
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
        body: JSON.stringify(file_data),
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to create file:', error);
      throw new Error('Failed to create file');
    }
  };
  
export const updateFile = async (fileId, file_data) => {
    const url = `${FETCH_FILES_ENDPOINT}/${fileId}`;  // 使用从配置文件导入的基础 URL
    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
        body: JSON.stringify(file_data),
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to update file:', error);
      throw new Error('Failed to update file');
    }
  };
  