import React, { useRef, useState } from 'react';
import { FileTextOutlined, CloseOutlined } from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import remarkGfm from 'remark-gfm';
import 'katex/dist/katex.min.css';
import ReactDOMServer from 'react-dom/server';

// 圈号映射对象
const CIRCLED_NUMBERS = {
  '1': '①',
  '2': '②',
  '3': '③',
  '4': '④',
  '5': '⑤',
  '6': '⑥',
  '7': '⑦',
  '8': '⑧',
  '9': '⑨',
  '10': '⑩',
  '11': '⑪',
  '12': '⑫',
  '13': '⑬',
  '14': '⑭',
  '15': '⑮',
  '16': '⑯',
  '17': '⑰',
  '18': '⑱',
  '19': '⑲',
  '20': '⑳',
};

// 检查是否包含KaTeX渲染的圈号
const containsKatexCircle = (text) => {
  return (
    text.includes('<span class="katex">') &&
    Object.values(CIRCLED_NUMBERS).some((circle) => text.includes(circle))
  );
};

// 预处理文本函数
const preprocessText = (text) => {
  const usedNumbers = new Set();

  if (containsKatexCircle(text)) {
    return text;
  }

  return text.replace(/\\textcircled\{(\d+)\}/g, (match, num) => {
    if (usedNumbers.has(num)) {
      let nextNum = parseInt(num) + 1;
      while (usedNumbers.has(nextNum.toString())) {
        nextNum++;
      }
      num = nextNum.toString();
    }

    usedNumbers.add(num);
    return CIRCLED_NUMBERS[num] || `(${num})`;
  });
};

// Utility function to concatenate arrays with '\n'
const getConcatenatedText = (text) => {
  if (Array.isArray(text)) {
    return text.join('\n');
  }
  return text;
};

// 修改KaTeX配置，添加自定义宏命令
const katexOptions = {
  strict: false,
  trust: true,
  throwOnError: false,
  macros: {
    '\\upmu': '\\mathrm{\\mu}',
    '\\N': '\\mathrm{N}', // 添加自定义宏
    '\\mum': '\\mathrm{\\mu m}',
    '\\um': '\\mathrm{\\mu m}',
    '\\cm': '\\mathrm{cm}',
    '\\kg': '\\mathrm{kg}',
    '\\m': '\\mathrm{m}',
    '\\textless': '<',
    '\\upbeta': '\\mathrm{\\beta}', // 添加 \upbeta 映射到 Unicode β
    '\\upalpha': '\\mathrm{\\alpha}',
  },
};

// 自定义文本组件
const CustomText = ({ children }) => {
  if (typeof children !== 'string') return <>{children}</>;
  return <>{preprocessText(children)}</>;
};

// 渲染内容组件
const RenderContent = ({ layouts, components }) => {
  return (
    <div>
      {layouts.map((item, index) => {
        if (item.type === 'text') {
          item = {
            ...item,
            text: preprocessText(item.text),
          };
        }

        return (
          <div key={index} style={{ marginBottom: '20px' }}>
            {item.type === 'text' ? (
              <div
                style={{
                  fontWeight: item?.text_level === 1 ? 'bold' : 'normal',
                  fontSize: item?.text_level === 1 ? '16px' : '12px',
                  lineHeight: '1.5',
                  wordBreak: 'break-word',
                }}
              >
                <ReactMarkdown
                  remarkPlugins={[remarkMath, remarkGfm]}
                  rehypePlugins={[[rehypeKatex, katexOptions]]}
                  components={components}
                  className="markdown-content"
                >
                  {item.text}
                </ReactMarkdown>
              </div>
            ) : item.type === 'table' || item.type === 'image' ? (
              <div className="figure-container">
                {item.type === 'table' && item.table_caption && (
                  <div className="table-caption">
                    <ReactMarkdown
                      remarkPlugins={[remarkMath, remarkGfm]}
                      rehypePlugins={[[rehypeKatex, katexOptions]]}
                      components={components}
                      className="markdown-content"
                    >
                      {preprocessText(getConcatenatedText(item.table_caption))}
                    </ReactMarkdown>
                  </div>
                )}
                <img
                  src={item.img_path}
                  alt={getConcatenatedText(
                    item.type === 'table' ? item.table_caption : item.img_caption
                  )}
                  style={{
                    maxWidth: '100%',
                    height: 'auto',
                    display: 'block',
                    margin: '10px auto',
                  }}
                  crossOrigin="anonymous"
                />
                {item.type === 'image' && item.img_caption && (
                  <div className="image-caption">
                    <ReactMarkdown
                      remarkPlugins={[remarkMath, remarkGfm]}
                      rehypePlugins={[[rehypeKatex, katexOptions]]}
                      components={components}
                      className="markdown-content"
                    >
                      {preprocessText(getConcatenatedText(item.img_caption))}
                    </ReactMarkdown>
                  </div>
                )}
                {item.type === 'table' && item.table_footnote && (
                  <div className="table-footnote">
                    <ReactMarkdown
                      remarkPlugins={[remarkMath, remarkGfm]}
                      rehypePlugins={[[rehypeKatex, katexOptions]]}
                      components={components}
                      className="markdown-content"
                    >
                      {preprocessText(getConcatenatedText(item.table_footnote))}
                    </ReactMarkdown>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        );
      })}
    </div>
  );
};

// 主组件
const MarkdownPdfExporter = ({ layouts, data, onClose }) => {
  const contentRef = useRef(null);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState(null);

  const components = {
    text: CustomText,
  };

  const exportToHtml = () => {
    setIsExporting(true);
    setError(null);

    try {
      const processedLayouts = layouts.map((item) => {
        if (item.type === 'text') {
          return {
            ...item,
            text: preprocessText(item.text),
          };
        }
        return item;
      });

      const content = (
        <RenderContent layouts={processedLayouts} components={components} />
      );

      const contentHtml = ReactDOMServer.renderToStaticMarkup(content);

      const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>${data.name || '文档'}</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/katex/0.12.0/katex.min.css">
  <style>
    body { 
      font-family: Arial, sans-serif; 
      padding: 20px; 
    }
    .markdown-content { 
      line-height: 1.5; 
    }
    .figure-container { 
      text-align: center; 
      margin: 20px 0; 
    }
    .table-caption, .image-caption, .table-footnote { 
      font-style: italic; 
      margin: 5px 0; 
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 10px;
    }
    table, th, td {
      border: 1px solid #d9d9d9;
    }
    th, td {
      padding: 8px;
      text-align: left;
    }
  </style>
</head>
<body>
  ${contentHtml}
</body>
</html>
      `;

      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${data.name || 'document'}.html`;
      document.body.appendChild(a);
      a.click();

      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      setError(`导出失败: ${error.message}`);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div>
      <div className="button-container" style={{ marginBottom: '20px' }}>
        <button
          onClick={exportToHtml}
          disabled={isExporting}
          className="icon-button export-button"
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 16px',
            borderRadius: '4px',
            border: '1px solid #d9d9d9',
            backgroundColor: '#fff',
            cursor: isExporting ? 'not-allowed' : 'pointer',
            opacity: isExporting ? 0.6 : 1,
          }}
        >
          <FileTextOutlined />
          {isExporting ? '导出中...' : '导出HTML'}
        </button>
        <button
          onClick={onClose}
          className="icon-button close-button"
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '8px 16px',
            borderRadius: '4px',
            border: '1px solid #d9d9d9',
            backgroundColor: '#fff',
            cursor: 'pointer',
            marginLeft: '10px',
          }}
        >
          <CloseOutlined />
          关闭
        </button>
        {error && <div style={{ color: 'red', marginTop: '8px' }}>{error}</div>}
      </div>

      <div
        ref={contentRef}
        className="pdf-content"
        style={{
          padding: '20px',
          backgroundColor: 'white',
          maxWidth: '210mm',
          margin: '0 auto',
          boxShadow: '0 0 10px rgba(0,0,0,0.1)',
        }}
      >
        <RenderContent layouts={layouts} components={components} />
      </div>
    </div>
  );
};

export default MarkdownPdfExporter;
