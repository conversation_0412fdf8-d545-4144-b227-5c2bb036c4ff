import React, { useEffect, useState } from 'react';
import { Table, message, Select, Tooltip,Popconfirm,Input} from 'antd';
import { useNavigate,useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';

import { 
  // FILE_MANAGER_NEW_NAME,
   FILE_MANAGER_ENDPOINT, SUPERUSER,
  KNOWLEDGE_BASE_ENDPOINT, AGENT_KNOWLEDGE_BASE_ENDPOINT
} from '../Configs/Config';
import { fetchBulk, deleteData, deleteBulk, updateData, createData, fetchData, createKBData } from '../Routers/Router';
import LoadingSpinner from '../utils/LoadingSpinner';
import SidebarList from '../SidebarList';
import Columns from './FileManagerColumns';

import './FileManager.css';
// import { duration } from '@mui/material';
const { Option } = Select;

const FileManagerList = () => {
  const [data, setData] = useState([]);
  const location = useLocation();
  const kb_id = location.state?.kb_id || null;
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [publishLoading, setPublishLoading] = useState(false);
  const [filterLoading, setFilterLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [sortField, setSortField] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState(kb_id);
  const [selectedPublished, setSelectedPublished] = useState(null); // New state for published filter
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10); // 假设每页显示10条数据
  const [total, setTotal] = useState(0); // 总数据条数
  const [publishedTotal, setPublishedTotal] = useState(0); // 总数据条数
  const [searchKeyword, setSearchKeyword] = useState('');
  const substring = 20;
  
  useEffect(() => {
    const skip = (currentPage - 1) * pageSize;
    // console.log(selectedKnowledgeBase,'selectedKnowledgeBase')
    getData(selectedKnowledgeBase, selectedPublished, skip);
    fetchKnowledgeBases();
  }, [sortField, sortOrder, selectedKnowledgeBase, selectedPublished, searchKeyword, currentPage, pageSize]);
  
  const getData = async (selectedKnowledgeBase,selectedPublished, skip = 0) => {
    setFilterLoading(true);
    try {
      let endpoint_api = `${FILE_MANAGER_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&skip=${skip}&limit=${pageSize}&username=${user_info.username}`;
      if (selectedKnowledgeBase) {
        endpoint_api += `&kb_id=${selectedKnowledgeBase}`;
      }
      if (selectedPublished !== null) {
        endpoint_api += `&is_published=${selectedPublished}`;
      }
      if (searchKeyword) {
        endpoint_api += `&name__contains=${searchKeyword}`;
      }
      const result = await fetchBulk(endpoint_api);
      setData(result.data);
      setTotal(result.count); // 设置总数据条数
      setPublishedTotal(result.published_count); // 设置总数据条数
    } catch (error) {
      message.error('Failed to fetch File Manager data');
    } finally {
      setLoading(false);
      setFilterLoading(false);
    }
  };

  const fetchKnowledgeBases = async () => {
    try {
      const result = await fetchBulk(`${KNOWLEDGE_BASE_ENDPOINT}/bulk?username=${user_info.username}`);
      setKnowledgeBases(result.data);
    } catch (error) {
      message.error('Failed to fetch knowledge bases');
    }
  };

  const handleKnowledgeBaseChange = (value) => {
    setSelectedRowKeys([]);
    setSelectedKnowledgeBase(value);
    getData(value,selectedPublished);
  };

  const handleDelete = async (id) => {
    setDeleteLoading(true);
    const endpoint_api = `${FILE_MANAGER_ENDPOINT}/${id}`;
    try {
      const delete_data = await deleteData(endpoint_api);
      const delete_agent_data = {
        agent_kb_name: getKnowledgeBasePinYinName(delete_data.kb_id),
        username: user_info.username,
        doc_id: delete_data.data_id
      };
      const agent_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/delete_article`;
      console.log(agent_endpoint_api,delete_agent_data);
      createData(agent_endpoint_api, delete_agent_data);
      
    const updatedData = data.filter((item) => item.id !== id);
    setData(updatedData);
    setTotal(updatedData.length); // 更新 total 为新的数据长度
      message.success('文件删除成功');
    } catch (error) {
      message.error('删除文件失败');
    } finally {
      setDeleteLoading(false);
    }
  };
  const handleIsPublishedChange = async (record, isPublished) => {
    setPublishLoading(true);
    // const endpoint_api = `${FILE_MANAGER_ENDPOINT}/${record.id}`;
    // const updatedRecord = { ...record, is_published: isPublished };
    try {
      if (!isPublished && record) {
        const delete_agent_data = {
          agent_kb_name: getKnowledgeBasePinYinName(record.kb_id),
          username: user_info.username,
          doc_id: record.data_id
        };
        const agent_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/delete_article`;
        await createData(agent_endpoint_api, delete_agent_data);
        const file_manager_update_endpoint = `${FILE_MANAGER_ENDPOINT}/update_is_published`;
        const result = await updateData(file_manager_update_endpoint, [{ doc_id: record.data_id, is_published: isPublished }]);
        setData(data.map(item =>
          item.id === record.id ? { ...record, is_published: result.find(r => r.id === item.id).is_published } : item
        ));
        message.success('文件状态更新成功');
      } else {
        const kb_endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/${record.kb_id}`;
        const kb_data = await fetchData(kb_endpoint_api);
        const kb_data_file_infos = [
          {
            kb_data: kb_data,
            layouts: record.layouts,
            doc_id: record.data_id,
            title: record.name,
            chunk_type: record.chunk_type,
            file_url: record.url
        }
        ];
        const agent_insert_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/layout_insert?username=${user_info.username}`;
        const insert_agent_data = await createKBData({
          endpoint_api: agent_insert_endpoint_api,
          kb_data_file_infos: kb_data_file_infos,
        });
        const file_manager_update_endpoint = `${FILE_MANAGER_ENDPOINT}/update_is_published`;
        const result = await updateData(file_manager_update_endpoint, insert_agent_data.data);
        console.log(result,'success')
        // Check if any of the results have is_published as false
        const failedUpdates = result.filter(item => !item.is_published);
        if (failedUpdates.length > 0) {
          const failedTitles = failedUpdates.map(item => data.find(d => d.id === item.id).name).join(', ');
          message.error(`文件状态更新失败: ${failedTitles}`);
        } else {
          setData(data.map(item =>
            item.id === record.id ? { ...record, is_published: result.find(r => r.id === item.id).is_published } : item
          ));
          message.success('文件状态更新成功');
        }
      }
    } catch (error) {
      message.error('更新文件状态失败');
    } finally {
      setPublishLoading(false);
    }
  };
  

  const handleBulkDelete = async () => {
    try {
      const endpoint_api = `${FILE_MANAGER_ENDPOINT}/bulk`;
      const delete_datas = await deleteBulk(endpoint_api, { file_manager_ids: selectedRowKeys });
      if (delete_datas && delete_datas.length > 0) {
        const doc_ids = delete_datas.map(item => item.data_id);
        const delete_agent_datas = {
          agent_kb_name: getKnowledgeBasePinYinName(delete_datas[0].kb_id),
          username: user_info.username,
          doc_ids: doc_ids
        };
        const agent_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/delete_articles`;
        createData(agent_endpoint_api, delete_agent_datas);
      }
      const skip = (currentPage - 1) * pageSize;
      getData(selectedKnowledgeBase, selectedPublished, skip);
      const updatedData = data.filter((item) => !selectedRowKeys.includes(item.id));
      setData(updatedData);
      setTotal(updatedData.length); // 更新 total 为新的数据长度
      // setData();
      setSelectedRowKeys([]);
      message.success('选中的文件已删除');
    } catch (error) {
      message.error('删除选中的文件失败');
    }
  };

  const handleAlignKnowledgeBase = async () => {
    try {
      const selectedData = data
      .filter(item => selectedRowKeys.includes(item.id))
      .map(item => ({
        ...item,
        agent_kb_name: getKnowledgeBasePinYinName(item.kb_id), // 为每个选中的数据添加 agent_kb_name 字段
      }));
      const alignData = {
        username: user_info.username,
        file_datas: selectedData // 传递选中的数据
      };
      const agent_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/select_articles`;
      const result = await createData(agent_endpoint_api, alignData);
      const filteredResultData = result.data.filter(item => !item.is_published);
      const file_manager_update_endpoint = `${FILE_MANAGER_ENDPOINT}/update_is_published`;
      await updateData(file_manager_update_endpoint, result.data);

      message.success(`${filteredResultData.length}数据未同步知识库`);
      setSelectedRowKeys([]);
    } catch (error) {
      message.error('知识库对齐失败');
    }
  };
  const handlePublishedChange = (value) => {
    setSelectedRowKeys([]);
    setSelectedPublished(value);
    getData(selectedKnowledgeBase, value);
  };
  const handleBulkPublish = async () => {
    setPublishLoading(true);
    try {
      const selectedDataWithKbData = await Promise.all(
        data
          .filter(item => selectedRowKeys.includes(item.id) && !item.is_published)
          .map(async item => {
            const kb_endpoint_api = `${KNOWLEDGE_BASE_ENDPOINT}/${item.kb_id}`;
            const kb_data = await fetchData(kb_endpoint_api);
            
            return {
              kb_data: kb_data,
              layouts: item.layouts,
              doc_id: item.data_id,
              title: item.name,
              chunk_type: item.chunk_type,
              file_url: item.url
            };
          })
      );
  
      if (selectedDataWithKbData.length > 0) {
        const agent_insert_endpoint_api = `${AGENT_KNOWLEDGE_BASE_ENDPOINT}/layout_insert?username=${user_info.username}`;
        const insert_agent_data = await createKBData({
          endpoint_api: agent_insert_endpoint_api,
          kb_data_file_infos: selectedDataWithKbData,
        });
  
        const file_manager_update_endpoint = `${FILE_MANAGER_ENDPOINT}/update_is_published`;
        const result = await updateData(file_manager_update_endpoint, insert_agent_data.data);
  
        // 检查 result 数据中 is_published 为 true 的条目
        const successfulUpdates = result.filter(item => item.is_published);
        if (successfulUpdates.length > 0) {
          // 获取成功发布的文件名，用于提示
          message.success(`成功发布的文件数量: ${successfulUpdates.length}`);
  
          // 取消选中成功发布的项
          const successfulIds = successfulUpdates.map(item => item.id);
          setSelectedRowKeys(prevKeys => prevKeys.filter(key => !successfulIds.includes(key)));
        }
  
        // 如果有失败的更新，警告用户
        const failedUpdates = result.filter(item => !item.is_published);
        if (failedUpdates.length > 0) {
          const failedTitles = failedUpdates.map(item => data.find(d => d.id === item.id).name).join(', ');
          message.warning(`以下文件发布失败: ${failedTitles}`);
        }
  
        // 更新表格数据
        setData(data.map(item => {
          const updatedItem = result.find(updated => updated.id === item.id);
          return updatedItem ? { ...item, is_published: updatedItem.is_published } : item;
        }));
      }
  
      // 重新获取数据
      const skip = (currentPage - 1) * pageSize;
      getData(selectedKnowledgeBase, selectedPublished, skip);
  
    } catch (error) {
      message.error('批量发布失败');
    } finally {
      setPublishLoading(false);
    }
  };
  

  const handleTableChange = (pagination, filters, sorter) => {
    setSortField(sorter.field || 'updated_at');
    setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);
  };

  const getKnowledgeBaseName = (kbId) => {
    const kb = knowledgeBases.find(kb => kb.id === kbId);
    return kb ? kb.name : kbId;
  };

  const getKnowledgeBasePinYinName = (kbId) => {
    const kb = knowledgeBases.find(kb => kb.id === kbId);
    return kb.agent_kb_name;
  };
  const columns = Columns({
    data,
    substring,
    user_info,
    SUPERUSER,
    handleIsPublishedChange,
    handleDelete,
    getKnowledgeBaseName
  });

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys),
    getCheckboxProps: (record) => ({
      disabled: user_info.username !== record.username,
      className: selectedRowKeys.includes(record.id) ? 'selected-row' : '',
    }),
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">文件管理配置</h1>
        {/* <SidebarList /> */}
      </div>
      <div style={{ width: '120px', float: 'right' }}>
          <SidebarList />
        </div>
      <div className="total-number">
        数据总量:<span className="blue-text">{total}</span>;
        已发布:<span className="blue-text">{publishedTotal}</span>
    </div>
      <div className="button-container">
        {/* <button
          className="custom-button"
          onClick={() => navigate(`${FILE_MANAGER_NEW_NAME}`)}
        >
          新增
        </button> */}
        <Popconfirm
          title="确定要删除选中的文件吗？"
          onConfirm={handleBulkDelete}
          okText="确定"
          cancelText="取消"
          disabled={selectedRowKeys.length === 0}
        >
        <button
          className="custom-button delete-button"
          // onClick={handleBulkDelete}
          disabled={selectedRowKeys.length === 0}
        >
          批量删除
        </button>
        </Popconfirm>
        <Tooltip title="用于查看向量知识库中数据是否和管理平台数据一致">
          <button
            className="custom-button align-button"
            onClick={handleAlignKnowledgeBase}
            disabled={selectedRowKeys.length === 0}
          >
            知识库对齐
          </button>
        </Tooltip>
        <button
          className="custom-button publish-button"
          onClick={handleBulkPublish}
          disabled={selectedRowKeys.length === 0}
        >
          批量发布
        </button>
        <div className="selected-count">
          已选中 {selectedRowKeys.length} 项
        </div>
        <Select
          style={{ width: 200, marginRight: 16 }}
          placeholder="选择知识库"
          onChange={handleKnowledgeBaseChange}
          value={selectedKnowledgeBase}
        >
          {knowledgeBases.map(kb => (
            <Option key={kb.id} value={kb.id}>{kb.name}({kb.username})</Option>
          ))}
        </Select>
        <Select
          style={{ width: 120, marginRight: 16 }}
          placeholder="发布状态"
          onChange={handlePublishedChange}
          value={selectedPublished}
        >
          <Option value={true}>已发布</Option>
          <Option value={false}>未发布</Option>
        </Select>
        <Input.Search
          placeholder="搜索文件名"
          allowClear
          onSearch={(value) => {
            setSearchKeyword(value);
            setCurrentPage(1);
            getData(selectedKnowledgeBase, selectedPublished);
          }}
          onChange={(e) => setSearchKeyword(e.target.value)}
          style={{ width: 200, marginRight: 16 }}
        />
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
        }}
        loading={loading}
        rowSelection={rowSelection}
        rowClassName={(record) => (selectedRowKeys.includes(record.id) ? 'selected-row' : 'hover-row')}
        onChange={handleTableChange}
      />
      {(deleteLoading || publishLoading || filterLoading) && <LoadingSpinner />}
    </div>
  );
};

export default FileManagerList;
