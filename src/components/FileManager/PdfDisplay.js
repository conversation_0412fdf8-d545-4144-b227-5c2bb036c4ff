import React, { useState, useEffect } from 'react';
import { updateData as updateDataRouter, fetchData } from '../Routers/Router';
import { FILE_MANAGER_ENDPOINT } from '../Configs/Config';
import {
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  DeleteOutlined,
  PlusOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import TextareaAutosize from 'react-textarea-autosize';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import remarkGfm from 'remark-gfm';
import 'katex/dist/katex.min.css';
import './PdfDisplay.css';
import MarkdownPdfExporter from './MarkdownPdfExporter';

const PdfDisplay = ({ data, show_edit = true }) => {
  const [layouts, setLayouts] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingContent, setEditingContent] = useState({});
  const [editingTypes, setEditingTypes] = useState({});
  const [showExporter, setShowExporter] = useState(false);
  const fileManagerUpdateEndpoint = `${FILE_MANAGER_ENDPOINT}/${data.id}`;

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const fileManagerFetchEndpoint = `${FILE_MANAGER_ENDPOINT}/${data.id}`;
        const initialData = await fetchData(fileManagerFetchEndpoint);
        setLayouts(initialData.layouts || []);
      } catch (error) {
        console.error('数据加载失败', error);
      }
    };

    fetchInitialData();
  }, [data]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        handleCancel();
      }
    };

    if (isEditing) {
      window.addEventListener('keydown', handleKeyDown);
    } else {
      window.removeEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isEditing]);

  const handleEditClick = () => {
    setIsEditing(true);
    const initialEditingContent = {};
    const initialEditingTypes = {};

    layouts.forEach((item, index) => {
      const key = `${index}`;
      if (item.type === 'text') {
        initialEditingContent[key] = item.text;
      }
      initialEditingTypes[key] = item.type;
    });

    setEditingContent(initialEditingContent);
    setEditingTypes(initialEditingTypes);
  };

  const handleSave = async () => {
    try {
      const updatedData = {
        layouts: layouts.map((item, index) => ({
          ...item,
          text:
            item.type === 'text'
              ? editingContent[`${index}`] || item.text
              : item.text,
          type: editingTypes[`${index}`] || item.type,
        })),
      };

      await updateDataRouter(fileManagerUpdateEndpoint, updatedData);
      setLayouts(updatedData.layouts);
      setIsEditing(false);
      console.log('保存成功');
    } catch (error) {
      console.error('保存失败', error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditingContent({});
    setEditingTypes({});
  };

  const handleContentChange = (key, content) => {
    setEditingContent((prev) => ({ ...prev, [key]: content }));
  };

  const handleTypeChange = (key, type) => {
    setEditingTypes((prev) => ({ ...prev, [key]: type }));
  };

  const handleDeleteKey = (e, key) => {
    if (
      e.key === 'Delete' &&
      e.target.selectionStart === 0 &&
      e.target.selectionEnd === e.target.value.length
    ) {
      e.preventDefault();
      setEditingContent((prev) => ({ ...prev, [key]: '' }));
    }
  };

  const handleAddCaption = (index, type) => {
    const newCaption = {
      flags: {},
      type: type,
      points: [
        [0, 0],
        [0, 0],
      ],
      content: '',
      group_id: null,
      page_index: index,
      shape_type: 'rectangle',
      description: '',
    };

    setLayouts((prevLayouts) => {
      const updatedLayouts = [...prevLayouts];
      updatedLayouts.splice(index + 1, 0, newCaption);
      return updatedLayouts;
    });

    const newKey = `${index + 1}`;
    setEditingContent((prev) => ({ ...prev, [newKey]: '' }));
    setEditingTypes((prev) => ({ ...prev, [newKey]: type }));
  };

  const handleDeleteItem = (index) => {
    setLayouts((prevLayouts) => {
      const updatedLayouts = prevLayouts.filter((_, i) => i !== index);
      return updatedLayouts;
    });

    setEditingContent((prev) => {
      const updatedContent = { ...prev };
      Object.keys(updatedContent).forEach((key) => {
        const keyIndex = Number(key);
        if (keyIndex === index) {
          delete updatedContent[key];
        } else if (keyIndex > index) {
          updatedContent[`${keyIndex - 1}`] = updatedContent[key];
          delete updatedContent[key];
        }
      });
      return updatedContent;
    });

    setEditingTypes((prev) => {
      const updatedTypes = { ...prev };
      Object.keys(updatedTypes).forEach((key) => {
        const keyIndex = Number(key);
        if (keyIndex === index) {
          delete updatedTypes[key];
        } else if (keyIndex > index) {
          updatedTypes[`${keyIndex - 1}`] = updatedTypes[key];
          delete updatedTypes[key];
        }
      });
      return updatedTypes;
    });
  };

  const handleExport = () => {
    setShowExporter(true);
  };

  const handleCloseExporter = () => {
    setShowExporter(false);
  };

  const katexOptions = {
    strict: false,
    trust: true,
    throwOnError: false,
    macros: {
      '\\upmu': '\\,\\mathrm{\\mu}',
      '\\mum': '\\,\\mathrm{\\mu m}',
      '\\um': '\\,\\mathrm{\\mu m}',
      '\\cm': '\\,\\mathrm{cm}',
      '\\kg': '\\,\\mathrm{kg}',
      '\\m': '\\,\\mathrm{m}',
      '\\textless': '<',
      '\\N': '\\mathrm{N}',
      '\\upbeta': '\\mathrm{\\beta}',
      '\\upalpha': '\\mathrm{\\alpha}',
    },
    globalGroup: true,
  };

  // Utility function to concatenate arrays with '\n'
  const getConcatenatedText = (text) => {
    if (Array.isArray(text)) {
      return text.join('\n');
    }
    return text;
  };

  const renderMarkdownContent = (content) => (
    <ReactMarkdown
      remarkPlugins={[remarkMath, remarkGfm]}
      rehypePlugins={[[rehypeKatex, katexOptions]]}
      className="markdown-content"
    >
      {content}
    </ReactMarkdown>
  );

  return (
    <div>
      {show_edit && (
        <div className="button-container-display">
          {!isEditing ? (
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <button onClick={handleEditClick} className="icon-button">
                <EditOutlined />
              </button>
              <button
                onClick={handleExport}
                className="icon-button export-button"
                title="导出HTML"
              >
                <DownloadOutlined />
              </button>
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <button
                onClick={handleSave}
                className="icon-button"
                style={{ marginRight: '10px' }}
              >
                <SaveOutlined />
              </button>
              <button onClick={handleCancel} className="icon-button">
                <CloseOutlined />
              </button>
            </div>
          )}
        </div>
      )}

      {showExporter && (
        <MarkdownPdfExporter
          layouts={layouts}
          data={data}
          onClose={handleCloseExporter}
        />
      )}

      {layouts.map((item, index) => {
        const key = `${index}`;
        return (
          <div key={key} style={{ marginBottom: '20px', position: 'relative' }}>
            {isEditing && show_edit ? (
              <>
                {item.type === 'text' ? (
                  <div>
                    <div className="label-delete-container">
                      <select
                        value={editingTypes[key] || item.type}
                        onChange={(e) => handleTypeChange(key, e.target.value)}
                        className="label-select"
                      >
                        <option value="title">标题</option>
                        <option value="text">文本</option>
                        <option value="figure">图片</option>
                        <option value="table">表格</option>
                        <option value="figure caption">图片注解</option>
                        <option value="table caption">表格注解</option>
                      </select>
                      <button
                        onClick={() => handleDeleteItem(index)}
                        className="icon-button delete-caption-button"
                      >
                        <DeleteOutlined />
                      </button>
                    </div>
                    <TextareaAutosize
                      value={editingContent[key] || item.text}
                      onChange={(e) => handleContentChange(key, e.target.value)}
                      onKeyDown={(e) => handleDeleteKey(e, key)}
                      className="editable-textarea"
                    />
                  </div>
                ) : item.type === 'table' || item.type === 'image' ? (
                  <div className="figure-container">
                    {item.type === 'table' && item.table_caption && (
                      <div className="table-caption">
                        {renderMarkdownContent(
                          getConcatenatedText(item.table_caption)
                        )}
                      </div>
                    )}
                    <img
                      src={item.img_path}
                      alt={getConcatenatedText(
                        item.type === 'table'
                          ? item.table_caption
                          : item.img_caption
                      )}
                      style={{ maxWidth: '100%' }}
                    />
                    {item.type === 'image' && item.img_caption && (
                      <div className="image-caption">
                        {renderMarkdownContent(
                          getConcatenatedText(item.img_caption)
                        )}
                      </div>
                    )}
                    {item.type === 'table' && item.table_footnote && (
                      <div className="table-footnote">
                        {renderMarkdownContent(
                          getConcatenatedText(item.table_footnote)
                        )}
                      </div>
                    )}
                    <div className="label-delete-container">
                      <select
                        value={editingTypes[key] || item.type}
                        onChange={(e) => handleTypeChange(key, e.target.value)}
                        className="label-select"
                      >
                        <option value="title">标题</option>
                        <option value="text">文本</option>
                        <option value="figure">图片</option>
                        <option value="table">表格</option>
                      </select>
                      <button
                        onClick={() =>
                          handleAddCaption(
                            index,
                            item.type === 'image'
                              ? 'figure caption'
                              : 'table caption'
                          )
                        }
                        className="icon-button add-caption-button"
                      >
                        <PlusOutlined />
                      </button>
                      <button
                        onClick={() => handleDeleteItem(index)}
                        className="icon-button delete-caption-button"
                      >
                        <DeleteOutlined />
                      </button>
                    </div>
                  </div>
                ) : null}
              </>
            ) : (
              <>
                {item.type === 'text' ? (
                  <div
                    style={{
                      fontWeight: item?.text_level === 1 ? 'bold' : 'normal',
                      fontSize: item?.text_level === 1 ? '16px' : '12px',
                    }}
                  >
                    {renderMarkdownContent(item.text)}
                  </div>
                ) : item.type === 'table' || item.type === 'image' ? (
                  <div className="figure-container">
                    {item.type === 'table' && item.table_caption && (
                      <div className="table-caption">
                        {renderMarkdownContent(
                          getConcatenatedText(item.table_caption)
                        )}
                      </div>
                    )}
                    <img
                      src={item.img_path}
                      alt={getConcatenatedText(
                        item.type === 'table'
                          ? item.table_caption
                          : item.img_caption
                      )}
                      style={{ maxWidth: '100%' }}
                    />
                    {item.type === 'image' && item.img_caption && (
                      <div className="image-caption">
                        {renderMarkdownContent(
                          getConcatenatedText(item.img_caption)
                        )}
                      </div>
                    )}
                    {item.type === 'table' && item.table_footnote && (
                      <div className="table-footnote">
                        {renderMarkdownContent(
                          getConcatenatedText(item.table_footnote)
                        )}
                      </div>
                    )}
                  </div>
                ) : null}
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default PdfDisplay;
