import React from 'react';
import ChartComponent from './ChartComponent';
import styles from './ChartsDisplay.module.css';

const formatDataForChart = (data = {}) => {
  if (!data || typeof data !== 'object') {
    return [];
  }
  return Object.entries(data)
    .map(([key, value]) => ({ 
      name: key || 'Unknown',
      count: typeof value === 'number' ? value : 0
    }))
    .sort((a, b) => b.count - a.count);
};

const formatDailyData = (data = []) => {
  if (!Array.isArray(data)) {
    return [];
  }
  return data.map(item => ({
    name: item?.date || 'Unknown Date',
    count: typeof item?.session_count === 'number' ? item.session_count : 0
  }));
};

const ChartsDisplay = ({ reportData }) => {
  if (!reportData) {
    return (
      <div className={styles.chartContainer}>
        <p>加载中...</p>
      </div>
    );
  }

  const {
    llm_id_report = {},
    prompt_id_report = {},
    kb_id_report = {},
    workflow_name_report = {},
    username_report = {},
    daily_session_report = []
  } = reportData;

  const chartData = {
    dailySession: formatDailyData(daily_session_report),
    username: formatDataForChart(username_report),
    workflow: formatDataForChart(workflow_name_report),
    kb: formatDataForChart(kb_id_report),
    llm: formatDataForChart(llm_id_report),
    prompt: formatDataForChart(prompt_id_report)
  };

  const hasData = Object.values(chartData).some(data => data.length > 0);

  if (!hasData) {
    return (
      <div className={styles.chartContainer}>
        <p>暂无数据</p>
      </div>
    );
  }

  return (
    <div className={styles.chartsGrid}>
      <div className={`${styles.chartContainer} ${styles.doubleWidth}`}>
        <ChartComponent
          title="每日会话次数"
          data={chartData.dailySession}
          dataKey="count"
          chartType="line"
          lineColor="#ff7300"
        />
      </div>
      
      <div className={styles.chartContainer}>
        <ChartComponent
          title="用户分布"
          data={chartData.username}
          dataKey="count"
          chartType="bar"
        />
      </div>
      
      <div className={styles.chartContainer}>
        <ChartComponent
          title="工作流分布"
          data={chartData.workflow}
          dataKey="count"
          chartType="bar"
        />
      </div>
      
      <div className={styles.chartContainer}>
        <ChartComponent
          title="知识库使用"
          data={chartData.kb}
          dataKey="count"
          chartType="bar"
        />
      </div>
      
      <div className={styles.chartContainer}>
        <ChartComponent
          title="大模型使用"
          data={chartData.llm}
          dataKey="count"
          chartType="bar"
        />
      </div>
      
      <div className={styles.chartContainer}>
        <ChartComponent
          title="提示词使用"
          data={chartData.prompt}
          dataKey="count"
          chartType="bar"
        />
      </div>
    </div>
  );
};

export default ChartsDisplay;