.chartsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.chartContainer {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chartContainer h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
}

.doubleWidth {
  grid-column: span 2;
}

/* 因为 recharts 相关的类名是全局的，所以需要保持原样 */
:global(.recharts-cartesian-axis-tick text) {
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 768px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }

  .chartContainer {
    padding: 12px;
  }

  .chartContainer h3 {
    font-size: 16px;
  }

  :global(.recharts-cartesian-axis-tick text) {
    max-width: 70px;
  }
}