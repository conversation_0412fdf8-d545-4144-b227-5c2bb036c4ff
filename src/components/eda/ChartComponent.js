import React, { useEffect, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Bar,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Cell
} from 'recharts';

// Hash string to color function
const hashStringToColor = (str) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const color = (hash & 0x00FFFFFF).toString(16).toUpperCase();
  return "#" + "00000".substring(0, 6 - color.length) + color;
};

// Custom tick component with proper prop types
const CustomTick = ({ x = 0, y = 0, payload = { value: '' } }) => {
  const words = payload.value.split(' ');
  const line1 = words.slice(0, Math.ceil(words.length / 2)).join(' ');
  const line2 = words.slice(Math.ceil(words.length / 2)).join(' ');

  return (
    <g transform={`translate(${x},${y})`}>
      <text x={0} y={0} dy={0} textAnchor="middle" fill="#666">
        <tspan x="0" dy="1.2em">{line1}</tspan>
        <tspan x="0" dy="1.2em">{line2}</tspan>
      </text>
    </g>
  );
};

const ChartComponent = ({ 
  title = '',
  data = [], 
  dataKey = '',
  chartType = "bar",
  lineColor = "#82ca9d" 
}) => {
  const chartRef = useRef(null);

  useEffect(() => {
    const adjustLabelHeight = () => {
      if (chartRef.current) {
        const labels = chartRef.current.querySelectorAll('.recharts-xaxis .recharts-cartesian-axis-tick-value');
        let maxHeight = 0;

        labels.forEach((label) => {
          const height = label.getBoundingClientRect().height;
          maxHeight = Math.max(maxHeight, height);
        });

        const newPadding = maxHeight + 10;
        chartRef.current.style.paddingBottom = `${newPadding}px`;
      }
    };

    const timeoutId = setTimeout(adjustLabelHeight, 100);
    window.addEventListener('resize', adjustLabelHeight);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', adjustLabelHeight);
    };
  }, [data]);

  const chartProps = {
    data,
    margin: { top: 5, right: 30, left: 20, bottom: 5 }
  };

  const axisProps = {
    tick: CustomTick,
    interval: 0,
    minTickGap: 5
  };

  return (
    <div className={`chart-container ${chartType === "line" ? "double-width" : ""}`} ref={chartRef}>
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={350}>
        {chartType === "bar" ? (
          <BarChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" {...axisProps} />
            <YAxis domain={[0, 'auto']} />
            <Tooltip />
            <Bar dataKey={dataKey}>
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={hashStringToColor(entry.name)} />
              ))}
            </Bar>
          </BarChart>
        ) : (
          <LineChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" {...axisProps} />
            <YAxis domain={[0, 'auto']} />
            <Tooltip />
            <Line 
              type="monotone" 
              dataKey={dataKey} 
              stroke={lineColor}
              dot={{ stroke: lineColor, strokeWidth: 2 }}
            />
          </LineChart>
        )}
      </ResponsiveContainer>
    </div>
  );
};

export default ChartComponent;