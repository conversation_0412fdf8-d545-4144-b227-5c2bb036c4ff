/* Home.css */

/* Sider - 侧边栏样式 */
.ant-layout-sider {
  background-color: #001529; /* 深色背景 */
  color: #fff;
}

/* Menu - 菜单样式 */
.ant-menu-inline .ant-menu-item {
  color: hsl(0, 0%, 0%); /* 黑色字体 */
  font-size: 16px;
}
.ant-menu-inline .ant-menu-item:hover {
  background-color: #1890ff; /* 悬停背景颜色 */
}
.ant-menu-inline .ant-menu-item-selected {
  background-color: #1890ff; /* 选中项背景颜色 */
  color: #fff;
}

/* Header - 头部样式 */
.site-layout-background {
  background: #f0f2f5; /* 浅灰色背景 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* 阴影效果 */
  display: flex;
  align-items: center;
  padding: 0 24px;
}

.trigger {
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
  margin-right: 20px;
}
.trigger:hover {
  color: #1890ff; /* 悬停时的颜色 */
}

.page-title-1 {
  font-size: 24px;
  font-weight: bold;
  color: #000; /* 标题字体颜色 */
  margin: 0;
  padding-bottom: 20px;
}

/* 搜索框样式 */
input[type="search"] {
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  width: 200px;
}

/* 下拉选择框样式 */
select {
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-left: 10px;
}

/* Tabs - 标签页样式 */
.ant-tabs-nav-list {
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 10px;
  display: flex;
  justify-content: space-around;
}

.ant-tabs-tab {
  font-size: 14px;
  border-radius: 12px;
  margin-right: 16px;
}
.ant-tabs-tab-active {
  color: #1890ff; /* 选中标签的颜色 */
}
.ant-tabs-ink-bar {
  background-color: #1890ff; /* 下划线颜色 */
}
.ant-tabs-content {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
