// src/components/MainLayout.js
import React from 'react';
import { Layout } from 'antd';
import Logout from './Login/Logout'; // Adjust the import path as necessary
import './MainLayout.css'; // Create and import a CSS file for styling

const { Header, Content } = Layout;

const MainLayout = ({ children }) => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header className="header">
        <div className="logout-container">
          <Logout />
        </div>
      </Header>
      <Content style={{ padding: '0 50px' }}>
        {children}
      </Content>
    </Layout>
  );
};

export default MainLayout;
