import { useState, useEffect } from 'react';

export const useDynamicTheme = () => {
    const [is_night, setIsNight] = useState(false);

    useEffect(() => {
        const checkTime = () => {
            const hour = new Date().getHours();
            const night_time = hour >= 19 || hour < 8;
            setIsNight(night_time);
        };

        checkTime(); // Initial check
        const interval_id = setInterval(checkTime, 60 * 1000); // Check every minute

        return () => clearInterval(interval_id); // Clean up the interval on component unmount
    }, []);

    return {
        background_color: is_night ? '#2b2b2b' : '#FFF',
        text_color: is_night ? '#FFF' : '#000',
        icon_color: is_night ? 'white' : '#000',
        icon_hover_color: is_night ? '#2b2b2b' : '#FFF',
    };
};

export const messageContainerStyleFunction = (background_color, text_color) => ({
    backgroundColor:background_color,
    color: text_color,
    width: '95%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '3px 1px',
    margin: '2px auto',
    boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
    minHeight: '50px',
});

export const workflowCardStyleFunction = (background_color, text_color) => ({
    backgroundColor:background_color,
    color: text_color,
    marginBottom:"16px",
    border:"2px solid #333333",
    cursor:"pointer",
    height:"160px",
    transition:"border-color 0.3s, box-shadow 0.3s",
    padding:"10px",
});
export const messageContentStyleFunction = (background_color, text_color) => ({
    backgroundColor:background_color,
    color: text_color,
    display: "flex",
    flexDirection: "column",
    width: "100%",
    position: 'relative',  // 确保子元素可以绝对定位
});

export const messageAvatarContainerStyleFunction = (background_color, text_color) => ({
    backgroundColor:background_color,
    color: text_color,
    display: "flex",
    flexDirection: "column",
    width: "35px",
    height: "35px",
    marginLeft: "20px",
    marginTop: 0,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: "50%",
});

export const messageTextStyleFunction = (background_color, text_color) => ({
    backgroundColor:background_color,
    color: text_color,
    display: "flex",
    flexGrow: 1,
    padding: 0,
    marginLeft: "20px",
    wordWrap: "break-word",
    textAlign: "left",
    position: "relative"
});

export const messageTextareaStyleFunction = (background_color, text_color) => ({
    backgroundColor:background_color,
    color: text_color,
    width: "100%",
    height: "100px",
    padding: "10px",
    boxSizing: "border-box",
    fontSize: "16px",
});

export const messageContentCopyStyleFunction = (background_color, icon_color, icon_hover_color) => ({
    backgroundColor: background_color,
    color: icon_color,
    zIndex: 10, // 确保图标位于内容之上
    display: 'flex',
    alignItems: 'center',
    gap: '10px', // 图标之间的间距
    position: 'relative', // 改为相对定位
    right: '5px', // 保持右对齐
    bottom: '0px', // 保持底部对齐
    borderRadius: '50%',
    ':hover': {
        color: icon_hover_color,
        transform: 'scale(1.1)', // 稍微放大图标
        cursor: 'pointer',
    },
});
export const messageContentEditStyleFunction = (background_color, icon_color, icon_hover_color) => ({
    position: 'absolute',
    backgroundColor:background_color,
    right: '25px', // 右对齐，留出一些边距
    bottom: '0px', // 底部对齐，留出一些边距
    color: icon_color,
    borderRadius: '50%',
    zIndex: 10, // 确保图标位于内容之上
    ':hover': {
        color: icon_hover_color,
        transform: 'scale(1.1)', // 稍微放大图标
        cursor: 'pointer',
    },
});
export const iconContainerStyleFunction = (background_color, icon_color, icon_hover_color) => ({
    position: 'absolute',
    height:"8px",
    right: '10px',  // 相对于 message_content 左侧的距离
    bottom: '-2px',  // 相对于 message_content 底部的距离
    display: 'flex',
    alignItems: 'center',
    gap: '10px',  // 图标之间的间距
    padding: '5px 10px',
    borderRadius: '12px',
    backgroundColor: background_color,
    color: icon_color,
    zIndex: 10,  // 确保在其他内容之上
});

export const workflowCardsContainerStyleFunction =(background_color, text_color) => ({
    backgroundColor: background_color,  // dark mode or light mode background
    color: text_color,  // text color
    padding: '20px',

    boxSizing:"border-box",
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: "wrap",
    display:'flex'
});
export const workflowCardsWrapperStyleFunction =(background_color, text_color) => ({
    backgroundColor: background_color,  // dark mode or light mode background
    color: text_color,  // text color
    justifyContent: 'space-between',
    position: 'relative', 
    // width: '98%',
    // display:'flex',
    padding: '10px',
    borderRadius:"10px",
    boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)",
    marginBottom: '20px',

});
export const chatMessagesContainerStyleFunction =(background_color, text_color) => ({
    backgroundColor: background_color,  // dark mode or light mode background
    color: text_color,  // text color
    // padding: '10px',
    paddingBottom:"10px",
    overflowY: 'auto',
    maxHeight: '80%',
    boxSizing:"border-box",
    width:"100%",
    flexGrow:1
});
export const workflowDescriptionStyleFunction =(background_color, text_color) => ({
    backgroundColor: background_color,  // dark mode or light mode background
    color: text_color,  // text color
    // padding: '10px',
    display:"flex",
    justifyContent: 'center',
    alignItems: 'center',
    height: '80%',
    textAlign:"center"
});


export const newMessageInputStyleFunction = (background_color, text_color) => ({
    width: '100%',
    height: '60px',
    border: '1px solid #ccc',
    borderRadius: '8px',
    resize: 'none',
    overflow: 'auto',
    padding: '0 45px 0 55px',
    backgroundColor: background_color,
    bottom:"10px",
    color: text_color,
    ':focus': {
        border: '1px solid rgb(217,217,227)',
        boxShadow: '0px 10px 15px 0px rgba(0, 0, 0, 0.3)',
    },
});

export const exampleCardsContainerTextStyleFunction = (background_color, text_color) => ({
    width: '10px',
    textAlign: 'center',
    padding:'10px',
    marginBottom:"10px",
    backgroundColor: background_color,
    color: text_color,
});
export const exampleCardsContainerStyleFunction = (background_color, text_color,icon_hover_color) => ({
    display: 'flex',
    justifyContent: 'center',
    padding:'10px',
    // marginBottom:"10px",
    backgroundColor: background_color,
    color: text_color,
    gap: '15px', // 这里设置卡片之间的间距
    ':hover': {
        color: icon_hover_color,
        transform: 'scale(1.5)', // 稍微放大图标
        cursor: 'pointer',
    },
});

export const exampleCardsStyleFunction = (background_color, text_color) => ({
    display: 'flex',
    justifyContent: 'space-around',
    borderRadius:'8px',
    boxShadow:"0 4px 8px rgba(0, 0, 0, 0.2)",
    transition:' transform 0.2s ease-in-out',
    padding:'15px',
    cursor: "pointer",
    marginBottom:"10px",
    backgroundColor: background_color,
    color: text_color,
});
export const newMessageInputContainerStyleFunction = (background_color, text_color) => ({
    width: '100%',
    height: '20%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    flexDirection: 'column',
    backgroundColor: background_color,
    color: text_color,
    boxShadow: '0px 10px 15px 0px rgba(0, 0, 0, 0.3)',
    borderRadius: '8px',
});

export const newMessageInputInnerStyleFunction = (background_color, text_color) => ({
    width: '90%',
    height: '40px',
    marginLeft: 'center',
    position: 'relative',
    display: 'flex',
    bottom:"15px",
    alignItems: 'center',
    backgroundColor: background_color,
    color: text_color,
});


export const newMessageIconContainerStyleFunction = (background_color, text_color, icon_hover_color) => ({
    position: 'absolute',
    right: '5px', // 右对齐，留出一些边距
    top: "50%",
    bottom:"10px",
    transform:"translateY(-50%)",
    cursor:"pointer",
    display:"flex",
    alignItems:"center",
    justifyContent: 'center',
    width:"30px",
    height:"30px",
    borderRadius:"50%",
    backgroundColor: background_color,
    color: text_color,
    ':hover': {
        color: icon_hover_color,
        backgroundColor: "#0056b3"
    },
});


export const uploadIconContainerStyleFunction = (background_color, text_color) => ({
    cursor: "pointer",
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '30px',
    height: '30px',
    transition: "0.4s",
    borderRadius:"50%",
    backgroundColor: background_color,
    color: text_color,
    ":hover" :{
        backgroundColor: "#e1e1e1"
      }
});


  export const fileUploadContainerStyleFunction = (background_color, text_color) => ({
    width: '30px',
    padding: "8px",
    position:"absolute",
    top: "50%",
    left:"10px",
    transform: "translateY(-50%)",
    backgroundColor: background_color,
    color: text_color,
});

export const chatContainerStyleFunction = (background_color, text_color,width="55%") => ({
    flexGrow: 1,
    height: "100%",
    display:"flex",
    flexDirection: "column",
    width:width,
    backgroundColor: background_color,
    color: text_color,
});

export const messageInputContainerStyleFunction = (background_color, text_color,width="55%") => ({
    flexGrow: 1,
    height: "105vh",
    display:"flex",
    flexDirection: "column",
    width:width,
    backgroundColor: background_color,
    color: text_color,
});

export const chatGptLogoContainerStyleFunction = (background_color, text_color) => ({
    flexGrow: 1,
    height: "90%",
    display:"flex",
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: background_color,
    color: text_color,
});

export const chatGptLogoStyleFunction = (background_color, text_color) => ({
    fontWeight:"bold",
    fontSize:"100px",
    backgroundColor: background_color,
    color: text_color,
});
export const fileNameStyleFunction = (background_color, text_color) => ({
    fontSize: "14px",
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    backgroundColor: background_color,
    color: text_color,
});

export const deleteFileIconStyleFunction = (background_color, text_color) => ({
    position: "absolute",
    top: "-5px",
    right: "-5px",
    cursor: "pointer",
    width: "20px",
    height: "20px",
    fontSize:"large",
    backgroundColor: background_color,
    color: text_color,
});

export const FileIconStyleFunction = (background_color, text_color) => ({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "40px", 
    borderRadius: "16px 0 0 16px", 
    backgroundColor: background_color,
    color: text_color,
});

export const FileIconSvgStyleFunction = (background_color, text_color) => ({
    width: "20px",
    height: "20px",
    backgroundColor: background_color,
    color: text_color,
});