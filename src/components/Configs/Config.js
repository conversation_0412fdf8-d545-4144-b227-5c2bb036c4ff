
export const API_BASE_URL = '192.168.98.43:8800';
export const MANAGER_API_BASE = '192.168.98.43:8880'
export const MANAGER_API_BASE_URL = `http://${MANAGER_API_BASE}`;
export const TTS_CLONE_API_BASE_URL = 'http://192.168.99.100:9999'



// export const API_BASE_URL = 'flint.hsmap.com';
// export const MANAGER_API_BASE = 'llm-agent.aihuoshi.net'
// export const MANAGER_API_BASE_URL = `https://${MANAGER_API_BASE}`;
// export const TTS_CLONE_API_BASE_URL = 'https:///fish-speech.aimed.cn'

const isSecure = MANAGER_API_BASE_URL.startsWith('https');

// 构建协议前缀
const httpProtocol = isSecure ? 'https' : 'http';
const wsProtocol = isSecure ? 'wss' : 'ws';

export const TTS_API_BASE_URL = `${MANAGER_API_BASE_URL}/api/v1/audio/tts`;


// markdown
export const MD_ENDPOINT = 'https://md-editor.aihuoshi.net/?id=';
export const TEST = 'test';

export const PERSONAL_SPACE_NAME = "/personal-spaces";
export const DASHBOARD_API_NAME = "/dashboard";
export const LOGIN_API_NAME = '/login';
export const REGISTER_API_NAME = '/register'
export const WORKFLOW_API_NAME = '/workflow';
export const WORKFLOW_TABLE_API_NAME = '/workflow_table';
export const FILE_MANAGER_API_NAME = '/file-manager';
export const FILE_MANAGERS_NAME = "/file-managers"
export const FILE_MANAGER_NEW_NAME = "/file-managers/new"
export const FILE_MANAGER_EDIT_NAME = "/file-managers/edit"
export const KNOWLEDGE_BASES_NAME = "/knowledge-bases"
export const KNOWLEDGE_BASE_NAME = '/knowledge-base'
export const KNOWLEDGE_BASE_NEW_NAME = '/knowledge-base/new'
export const KNOWLEDGE_BASE_DETAIL_NAME = '/knowledge-base/new'
export const KNOWLEDGE_BASE_EDIT_NAME = '/knowledge-base/edit'
export const LLMS_NAME = "/llms"
export const LLM_NEW_NAME = '/llm/new'
export const LLM_DETAIL_NAME = '/llm/new'
export const LLM_EDIT_NAME = '/llm/edit'
export const PROMPTS_NAME = "/prompts"
export const PROMPT_NEW_NAME = '/prompt/new'
export const PROMPT_EDIT_NAME = '/prompt/edit'
export const WORKFLOWS_NAME = "/workflows"
export const WORKFLOW_NEW_NAME = '/workflow/new'
export const WORKFLOW_EDIT_NAME = '/workflow/edit'
export const USERS_NAME = "/users"
export const USER_NEW_NAME = '/user/new'
export const USER_EDIT_NAME = '/user/edit'
export const LLM_DIALOGUES_NAME = "/llm-dialogues"
export const LLM_DIALOGUE_NEW_NAME = '/llm-dialogue/new'
export const LLM_DIALOGUE_EDIT_NAME = '/llm-dialogue/edit'
export const USER_ASSOCIATIONS_NAME = "/user-associations"
export const USER_ASSOCIATION_NEW_NAME = '/user-association/new'
export const USER_ASSOCIATION_EDIT_NAME = '/user-associations/edit'
export const AGENTS_NAME = "/agents"
export const AGENT_NEW_NAME = '/agent/new'
export const AGENT_EDIT_NAME = '/agent/edit'
export const KB_ASSOCIATION_FILE_NAME = '/kb-association-files'
export const WORKSPACES_NAME = "/plugin/workspaces"
export const WORKSPACE_NEW_NAME = '/plugin/workspace/new'
export const WORKSPACE_EDIT_NAME = '/plugin/workspace/edit'
export const TOOLS_NAME = "/plugin/workspace/tools"
export const TOOL_NEW_NAME = '/plugin/workspace/tools/new'
export const TOOL_EDIT_NAME = '/plugin/workspace/tools/edit'

export const ARTICLE_NAME = "/content-platform/articles"
export const ARTICLE_EDIT_NAME = "/content-platform/article/edit"
export const LOGOUT_NAME = '/logout'
// PRODUCT
export const PRODUCT_AUDIO_PODCAST_NAME = '/product/audio/podcast'
export const PRODUCT_AUDIO_CLONE_NAME = '/product/audio/clone'
export const PRODUCT_NAME = '/product'
export const MCP_SERVER_NAME = '/mcp-servers'

// agent name
export const QU = "QU";
export const QA = "QA";
export const SEARCH = "Search";
export const OUTLINE = "OUTLINE";
export const QUERY_UNDERSTAND_NAME = "问题理解";
export const GENERATE_ANSWER_NAME = '内容生成';
export const ANSWER_REASONING_NAME = '答案推理';
export const SEARCH_NAME = "搜索";
export const OUTLINE_NAME = "大纲生成";
export const IMAGE_GENERATOR_NAME = "图片生成";
export const POPULAR_SCIENCE_IMITATING_WRITING_NAME = "科普仿写";
export const POLISH_NAME = "润色";
export const START_NODE_NAME = "开始节点";
export const AGENT_ROUTER_NAME = "路由节点";
export const ARTICLE_FUSION_NAME = '文章融合';
export const TOOL_NODE_NAME = '工具';
export const MCP_RESOURCE_NAME = 'MCP资源'
export const DEEP_RESEARCH_NAME = '深度研究';
export const INTERRUPTION_NODE_NAME = '用户交互';
export const AUTO_DEEP_RESEARCH_NODE_NAME = '自主搜索';
export const FILE_PARSE_NAME = '文件解析'
export const LLM_NAME = '大模型';
export const KNOWLEDGE_BASE_LLM_NAME = '知识库';
export const REFLECTION_NAME = "反思";
export const TAB_WORKFLOWS = "工作流";
export const TAB_AGENTS = '智能体';
export const USER = 'user';
export const ASSISTANT = 'assistant';
export const NEW_WORKFLOW = 'new_workflow';
export const SUPERUSER = 'admin';
export const ENTERPRISE = 'enterprise';

// export const AGENT_LLM_REQUEST_ENDPOINT = `wss://${API_BASE_URL}/ws/rewrite_query_stream`
// export const AGENT_SSE_LLM_REQUEST_ENDPOINT = `https://${API_BASE_URL}/sse/rewrite_query_stream`;
// export const AGENT_SSE_LLM_UPLOAD_DATA_ENDPOINT = `https://${API_BASE_URL}/sse/upload_data`;
// export const MEMORY_INSERT_ENDPOINT = `https://${API_BASE_URL}/api/v1/memory/insert/`;
// export const MEMORY_SELECT_ENDPOINT = `https://${API_BASE_URL}/api/v1/memory/select/`;
// export const MEMORY_DELETE_ENDPOINT = `https://${API_BASE_URL}/api/v1/memory/delete/`;
// export const MEMORY_UPDATE_TITLE_ENDPOINT = `https://${API_BASE_URL}/api/v1/memory/update_title/`;
// export const MEMORY_UPDATE_FEEDBACK_ENDPOINT = `https://${API_BASE_URL}/api/v1/memory/update_feedback/`;
// export const AGENT_KNOWLEDGE_BASE_ENDPOINT = `https://${API_BASE_URL}/api/v1/knowledge_bases`;
// export const RETRIEVALS_ENDPOINT = `https://${API_BASE_URL}/api/v1/retrievals/`;
// export const LLM_CHAT_ENDPOINT = `https://${API_BASE_URL}/api/v1/llms/`;

export const AGENT_LLM_REQUEST_ENDPOINT = `${wsProtocol}://${API_BASE_URL}/ws/rewrite_query_stream`
export const AGENT_SSE_LLM_REQUEST_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/sse/rewrite_query_stream`;
export const AGENT_SSE_LLM_UPLOAD_DATA_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/sse/upload_data`;
export const MEMORY_INSERT_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/memory/insert/`;
export const MEMORY_SELECT_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/memory/select/`;
export const MEMORY_DELETE_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/memory/delete/`;
export const MEMORY_UPDATE_TITLE_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/memory/update_title/`;
export const MEMORY_UPDATE_FEEDBACK_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/memory/update_feedback/`;
export const AGENT_KNOWLEDGE_BASE_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/knowledge_bases`;
export const RETRIEVALS_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/retrievals/`;
export const LLM_CHAT_ENDPOINT = `${httpProtocol}://${API_BASE_URL}/api/v1/llms/`;
export const EVENT_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/events`;


// export const WORKFLOWS_ENDPOINT = `${API_BASE_URL}/workflows/`;
export const REGISTER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/users/register/`;
export const LOGIN_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/users/login/`;
export const WORKFLOWS_USER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/workflows/user/`;
export const WORKFLOWS_UPDATE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/workflows/`;
export const WORKFLOWS_DATABASE_COUNT_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/workflows/database/count`;
export const USER_CONFORM_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/users/me`;
// 文件管理
export const FETCH_FILES_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/files`;

// 创建向量数据库
export const KNOWLEDGE_BASE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/knowledge_bases`;
export const LLM_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/llms`;
export const PROMPT_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/prompts`;
export const WORKFLOW_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/workflows`;
export const USER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/users`;
export const LLM_DIALOGUE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/llm_dialogues`;
export const USER_ASSOCIATION_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/user-associations`;
export const AGENT_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/agents`;
export const FILE_MANAGER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/file-managers`;
export const KB_ASSOCIATION_FILE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/kb-association-files`;
export const ARTICLE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/articles`;
export const WORKSPACE_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/plugin/workspaces`;
export const TOOL_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/plugin/workspaces/tools`;
export const INDICATOR_SCHEMA_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/indicator_schema`;
export const MCP_SERVER_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/mcp-servers`;

// Subscription endpoints
export const SUBSCRIPTION_PLAN_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/subscription-plans`;
export const USER_SUBSCRIPTION_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/user-subscriptions`;
export const PAYMENT_RECORD_ENDPOINT = `${MANAGER_API_BASE_URL}/api/v1/payment-records`;
