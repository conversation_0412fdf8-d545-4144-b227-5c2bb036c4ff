import { fetchData } from "../Routers/Router";
import { 
    KNOWLEDGE_BASE_ENDPOINT,LLM_ENDPOINT, PROMPT_ENDPOINT,TOOL_ENDPOINT

 } from "../Configs/Config";
 
export const fetchDataIfExists = async (node, endpoint, selectKey) => {
    if (node.data?.selects?.[selectKey]) {
        return await fetchData(`${endpoint}/query_by_name/${node.data.selects[selectKey]}`);
    }
    return null;
};

export const fetchKbData = async (workflow) => {
    if (!workflow?.nodes) {
        console.log('workflow or workflow.nodes is null or undefined');
        return null;
    }
    // const fetchDataIfExists = async (node, endpoint, selectKey) => {
    //     if (node.data?.selects?.[selectKey]) {
    //         return await fetchData(`${endpoint}/query_by_name/${node.data.selects[selectKey]}`);
    //     }
    //     return null;
    // };

    const kbPromises = workflow.nodes.map(async (node) => {
        const kb_data = await fetchDataIfExists(node, KNOWLEDGE_BASE_ENDPOINT, 'kb_name');
        const llm_data = await fetchDataIfExists(node, LLM_ENDPOINT, 'llm_name');
        const positive_prompt = await fetchDataIfExists(node, PROMPT_ENDPOINT, 'positive_prompt');
        const reflection_prompt = await fetchDataIfExists(node, PROMPT_ENDPOINT, 'reflection_prompt');
        const negative_prompt = await fetchDataIfExists(node, PROMPT_ENDPOINT, 'negative_prompt');
        const tool_data = await fetchDataIfExists(node, TOOL_ENDPOINT, 'tool_name');
  
        return {
            ...node,
            data: {
                ...node.data,
                selects: {
                    ...node.data.selects,
                    id: node.id,
                    agent_name: node.type,
                    kb_data: kb_data ? kb_data : null,
                    llm_data: llm_data ? llm_data : null,
                    positive_prompt_data: positive_prompt ? positive_prompt : null,
                    reflection_prompt_data: reflection_prompt ? reflection_prompt : null,
                    negative_prompt_data: negative_prompt ? negative_prompt : null,
                    tool_data: tool_data ? tool_data : null,
                }
            }
        };
    });

    return await Promise.all(kbPromises);
};
