import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Checkbox, Button, message, Spin, Alert, Space, Tag, Radio, Card, Upload, Divider, Tooltip } from 'antd';
import { SearchOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { createData, updateData, checkPackage, installPackage } from '../Routers/Router';
import styles from './McpServerForm.module.css';
import { MCP_SERVER_ENDPOINT } from '../Configs/Config';
import FileUploader from './FileUploader';
import { parseExamplesData, cleanExamplesData, createNewExample, getExampleHelpContent } from './McpServerExampleHelper';
import { uploadImageToServer } from '../Workflow/Agents/workflows/uploadUtils';

const { Option } = Select;
const { TextArea } = Input;

const McpServerForm = ({ visible, onCancel, onSuccess, initialValues, apiEndpointBase }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [actionLoading, setActionLoading] = useState(false);
    const [checkingPackage, setCheckingPackage] = useState(false);
    const [selectedTransport, setSelectedTransport] = useState(initialValues?.transport || 'stdio');
    const [selectedCommand, setSelectedCommand] = useState(
        initialValues?.transport === 'stdio' ? initialValues?.command || 'python' : ''
    );
    const [selectedMcpType, setSelectedMcpType] = useState(initialValues?.mcp_type || 'tool');
    const [inputMode, setInputMode] = useState('text');
    const [uploadedContent, setUploadedContent] = useState('');
    const [examples, setExamples] = useState([]);
    const [debugInfo, setDebugInfo] = useState('');

    console.log('Form mounted/updated with initialValues:', initialValues);

    const isEditing = !!initialValues;
    const modalTitle = isEditing ? '修改 MCP Server' : '添加 MCP Server';

    const parsePackageString = (pkgString) => {
        if (!pkgString || typeof pkgString !== 'string') {
            return { name: null, required_version: null, specifier: null };
        }
        const trimmedPkgString = pkgString.trim();
        const matchVersion = trimmedPkgString.match(/^([a-zA-Z0-9._-]+)\s*([>=<~!]=?)\s*([a-zA-Z0-9._*+!-]+)/);
        if (matchVersion) {
            return { name: matchVersion[1], required_version: `${matchVersion[2]}${matchVersion[3]}`, specifier: trimmedPkgString };
        }
        if (/^[a-zA-Z0-9._-]+$/.test(trimmedPkgString)) {
            return { name: trimmedPkgString, required_version: null, specifier: trimmedPkgString };
        }
        console.warn(`Could not parse package string: "${pkgString}"`);
        return { name: null, required_version: null, specifier: trimmedPkgString };
    };

    useEffect(() => {
        if (initialValues && (initialValues.mcp_type === 'prompt' || initialValues.mcp_type === 'resource')) {
            console.log('Setting initial content directly:', initialValues.content);
            setUploadedContent(initialValues.content || '');
            form.setFieldsValue({
                content: initialValues.content
            });
        }
    }, [initialValues, form]);

    useEffect(() => {
        if (visible) {
            console.log('Modal visible, current form values:', form.getFieldsValue());
            setCheckingPackage(false);
            if (isEditing && initialValues) {
                const initialType = initialValues.mcp_type || 'tool';
                setSelectedMcpType(initialType);
                
                if (initialType === 'prompt' || initialType === 'resource') {
                    console.log('Setting form values for prompt/resource:', initialValues);
                    setUploadedContent(initialValues.content || '');
                    const formValues = {
                        ...initialValues,
                        content: initialValues.content
                    };
                    console.log('Setting form values:', formValues);
                    form.setFieldsValue(formValues);
                    
                    setTimeout(() => {
                        const currentValues = form.getFieldsValue();
                        console.log('Form values after set:', currentValues);
                        if (!currentValues.content) {
                            console.log('Content still empty, trying to set again');
                            form.setFieldsValue({
                                content: initialValues.content
                            });
                        }
                    }, 100);
                } else {
                    const initialTransport = initialValues.transport || 'stdio';
                    const initialCommand = initialValues.command || 'python';
                    
                    const preparedValues = {
                        ...initialValues,
                        mcp_type: initialType,
                        args: initialType === 'tool' && initialValues.args ? JSON.stringify(initialValues.args, null, 2) : '',
                        env: initialType === 'tool' && initialValues.env ? JSON.stringify(initialValues.env, null, 2) : '',
                        package_name: initialType === 'tool' && initialTransport === 'stdio' ? initialValues.package_name || '' : undefined,
                        command: initialType === 'tool' && initialTransport === 'stdio' ? initialCommand : undefined,
                        url: initialType === 'tool' && initialTransport !== 'stdio' ? initialValues.url : undefined,
                    };
                    form.setFieldsValue(preparedValues);
                    setSelectedTransport(initialTransport);
                    setSelectedCommand(initialType === 'tool' && initialTransport === 'stdio' ? initialCommand : '');
                }
                const parsedExamples = parseExamplesData(initialValues.examples);
                setExamples(parsedExamples);
                setDebugInfo(`加载了 ${parsedExamples.length} 个示例`);
            } else {
                form.resetFields();
                form.setFieldsValue({
                    mcp_type: 'tool',
                    is_active: true,
                    transport: 'stdio',
                    command: 'python',
                    package_name: '',
                    args: '',
                    env: '',
                    content: '',
                });
                setSelectedMcpType('tool');
                setSelectedTransport('stdio');
                setSelectedCommand('python');
                setUploadedContent('');
                setExamples([]);
                setDebugInfo('');
            }
        }
    }, [visible, initialValues, form, isEditing]);

    useEffect(() => {
        console.log('uploadedContent changed:', uploadedContent);
        if (uploadedContent) {
            console.log('Setting form content from uploadedContent');
            form.setFieldsValue({
                content: uploadedContent
            });
            form.validateFields(['content']);
            setInputMode('text');
        }
    }, [uploadedContent, form]);

    const handleMcpTypeChange = (value) => {
        setSelectedMcpType(value);
        if (value === 'tool') {
            form.setFieldsValue({
                content: undefined,
                transport: 'stdio',
                command: 'python',
                package_name: '',
                args: '',
                env: '',
                url: undefined,
            });
            setSelectedTransport('stdio');
            setSelectedCommand('python');
        } else {
            form.setFieldsValue({
                transport: undefined,
                command: undefined,
                package_name: undefined,
                args: undefined,
                env: undefined,
                url: undefined,
                content: form.getFieldValue('content') || '',
            });
            setSelectedTransport('');
            setSelectedCommand('');
        }
    };

    const handleTransportChange = (value) => {
        setSelectedTransport(value);
        if (value === 'stdio') {
             form.setFieldsValue({
                 url: undefined,
                 command: form.getFieldValue('command') || 'python',
                 package_name: form.getFieldValue('package_name') || '',
                 args: form.getFieldValue('args') || '',
                 env: form.getFieldValue('env') || '',
             });
             setSelectedCommand(form.getFieldValue('command') || 'python');
        } else {
            form.setFieldsValue({
                command: undefined,
                package_name: undefined,
                args: undefined,
                env: undefined,
                url: form.getFieldValue('url') || '',
             });
             setSelectedCommand('');
        }
    };

    const handleCommandChange = (value) => {
        setSelectedCommand(value);
    };

    const validateJsonString = (input, type = 'any') => {
        if (!input || input.trim() === '') return { isValid: true, parsed: type === 'array' ? [] : (type === 'object' ? {} : null) };
        try {
            const parsed = JSON.parse(input);
            if (type === 'array' && !Array.isArray(parsed)) {
                return { isValid: false, error: '输入必须是有效的JSON数组 (e.g., ["a", "b"]) 或留空.' };
            }
            if (type === 'object' && (typeof parsed !== 'object' || Array.isArray(parsed) || parsed === null)) {
                return { isValid: false, error: '输入必须是有效的JSON对象 (e.g., {"key": "value"}) 或留空.' };
            }
            return { isValid: true, parsed: parsed };
        } catch (e) {
            return { isValid: false, error: `无效的JSON格式: ${e.message}` };
        }
    };

    const checkAndInstallPackageIfNeeded = async (packageNameSpecifier) => {
        if (!packageNameSpecifier || typeof packageNameSpecifier !== 'string' || packageNameSpecifier.trim() === '') {
            return { proceed: true };
        }
        if (!(selectedTransport === 'stdio' && (selectedCommand === 'python' || selectedCommand === 'uv'))) {
            return { proceed: true };
        }

        if (!MCP_SERVER_ENDPOINT) {
            message.error("配置错误: 无法找到包管理API端点 (MCP_SERVER_ENDPOINT).");
            return { proceed: false };
        }
        setActionLoading(true);
        message.info({ content: `正在检查包 "${packageNameSpecifier}"...`, key: 'pkgAction', duration: 0 });
        let needsInstall = false;
        let installReason = '';
        const checkApiEndpoint = `${MCP_SERVER_ENDPOINT}/pip/check`;
        const installApiEndpoint = `${MCP_SERVER_ENDPOINT}/pip/install`;
        const { name, required_version, specifier } = parsePackageString(packageNameSpecifier);
        const effectiveName = name || specifier;
        const effectiveSpecifier = specifier || effectiveName;

        if (!effectiveName) {
             message.error(`无效的包名: "${packageNameSpecifier}"`);
             setActionLoading(false);
             message.destroy('pkgAction');
             return { proceed: false };
        }

        try {
            const result = await checkPackage(checkApiEndpoint, effectiveName, required_version);
            if (!result.exists || (required_version && !result.version_ok)) {
                needsInstall = true;
                installReason = !result.exists ? '不存在' : `版本不符 (需要: ${required_version || '任意'}, 当前: ${result.current_version || '未知'})`;
            }
            message.destroy('pkgAction');

            if (needsInstall) {
                const userConfirmed = await new Promise(resolve => {
                    Modal.confirm({
                        title: '包安装确认',
                        content: (
                            <div>
                                <p>包 <code>{effectiveSpecifier}</code> {installReason}.</p>
                                <p>是否尝试自动安装/更新此包?</p>
                                <p><small>注意: 这将使用服务器环境的包管理器 (例如 pip).</small></p>
                            </div>
                        ),
                        okText: '安装',
                        cancelText: '取消',
                        onOk: () => resolve(true),
                        onCancel: () => resolve(false),
                        width: 450,
                    });
                });

                if (!userConfirmed) {
                    message.warning('用户取消安装，操作已停止。');
                    setActionLoading(false);
                    return { proceed: false };
                }

                message.info({ content: `正在安装包 "${effectiveSpecifier}"...`, key: 'pkgAction', duration: 0 });
                try {
                    const installResult = await installPackage(installApiEndpoint, effectiveSpecifier);
                    message.destroy('pkgAction');
                    if (!installResult.success) {
                        Modal.error({
                            title: '包安装失败',
                            content: (
                                <div>
                                    <p>未能成功安装包 <code>{effectiveSpecifier}</code>.</p>
                                    <p>原因: {installResult.message || '未知错误，请查看日志'}</p>
                                    <p>请检查环境或手动安装后重试。</p>
                                </div>
                            ),
                            width: 450,
                        });
                        setActionLoading(false);
                        return { proceed: false };
                    } else {
                        message.success(`包 "${effectiveSpecifier}" 已成功安装/更新!`, 3);
                    }
                } catch (error) {
                    message.destroy('pkgAction');
                    console.error(`安装包 "${effectiveSpecifier}" 时出错:`, error);
                    message.error(`安装包 "${effectiveSpecifier}" 时出错: ${error.message || '请查看控制台'}`);
                    setActionLoading(false);
                    return { proceed: false };
                }
            } else {
                message.success(`包 "${effectiveSpecifier}" 已存在且版本符合要求。`, 3);
            }
            setActionLoading(false);
            return { proceed: true };
        } catch (error) {
             message.destroy('pkgAction');
            console.error("包检查/安装流程出错:", error);
            message.error(`检查包 "${effectiveName}" 时出错: ${error.message || '请查看控制台'}`);
            setActionLoading(false);
            return { proceed: false };
        }
    };

    const handleManualPackageCheck = async () => {
        const packageNameSpecifier = form.getFieldValue('package_name');

        if (!packageNameSpecifier || packageNameSpecifier.trim() === '') {
            message.warning('请输入要检查的包名.');
            return;
        }

        if (!MCP_SERVER_ENDPOINT) {
            message.error("配置错误: 无法找到包管理API端点 (MCP_SERVER_ENDPOINT).");
            return;
        }

        setCheckingPackage(true);
        message.loading({ content: `正在检查包 "${packageNameSpecifier}"...`, key: 'manualCheck', duration: 0 });

        const { name, required_version, specifier } = parsePackageString(packageNameSpecifier);
        const effectiveName = name || specifier;
        const checkApiEndpoint = `${MCP_SERVER_ENDPOINT}/pip/check`;

        if (!effectiveName) {
            message.error({ content: `无法解析包名: "${packageNameSpecifier}"`, key: 'manualCheck', duration: 3 });
            setCheckingPackage(false);
            return;
        }


        try {
            console.log(`Manually checking package via API: ${checkApiEndpoint}`, { name: effectiveName, required_version });
            const result = await checkPackage(checkApiEndpoint, effectiveName, required_version);
            console.log(`Manual check result for ${effectiveName}:`, result);

            if (result.exists) {
                let msg = `包 "${effectiveName}" 已存在.`;
                if (result.current_version) {
                    msg += ` 当前版本: ${result.current_version}.`;
                }
                if (required_version) {
                    if (result.version_ok) {
                        msg += ` 版本符合要求 (${required_version}).`;
                        message.success({ content: msg, key: 'manualCheck', duration: 4 });
                    } else {
                        msg += ` 版本不符合要求 (${required_version}).`;
                        message.warning({ content: msg, key: 'manualCheck', duration: 5 });
                    }
                } else {
                    message.success({ content: msg, key: 'manualCheck', duration: 4 });
                }
            } else {
                message.error({ content: `包 "${effectiveName}" 不存在.`, key: 'manualCheck', duration: 4 });
            }
        } catch (error) {
            console.error(`手动检查包 "${effectiveName}" 时出错:`, error);
            message.error({ content: `检查包时出错: ${error.message || '请查看控制台'}`, key: 'manualCheck', duration: 4 });
        } finally {
            setCheckingPackage(false);
        }
    };

    const showExampleHelp = () => {
        Modal.info({
            title: '示例配置说明',
            content: getExampleHelpContent(),
            width: 550,
        });
    };

    const handleSubmit = async () => {
        if (!apiEndpointBase) {
            message.error("配置错误: MCP Server API端点丢失.");
            return;
        }

        try {
            const values = await form.validateFields();
            setLoading(true);

            let apiData = { ...values };
            let proceedWithSave = true;

            if (apiData.mcp_type === 'tool') {
                delete apiData.content;

                const shouldCheckPackage = !isEditing &&
                                          apiData.transport === 'stdio' &&
                                          (apiData.command === 'python' || apiData.command === 'uv') &&
                                          apiData.package_name &&
                                          apiData.package_name.trim() !== '';

                if (shouldCheckPackage) {
                    console.log("Running package check/install for new 'tool' server...");
                    const packageCheckResult = await checkAndInstallPackageIfNeeded(apiData.package_name);
                    proceedWithSave = packageCheckResult.proceed;
                } else if (isEditing) {
                    console.log("Skipping automatic package check/install during edit or for non-applicable tool types.");
                }

                if (!proceedWithSave) {
                    setLoading(false);
                    return;
                }

                if (apiData.transport === 'stdio') {
                    const argsValidation = validateJsonString(apiData.args, 'array');
                    if (!argsValidation.isValid) {
                        form.setFields([{ name: 'args', errors: [argsValidation.error] }]);
                        setLoading(false); return;
                    }
                    apiData.args = argsValidation.parsed;

                    const envValidation = validateJsonString(apiData.env, 'object');
                    if (!envValidation.isValid) {
                        form.setFields([{ name: 'env', errors: [envValidation.error] }]);
                        setLoading(false); return;
                    }
                    apiData.env = envValidation.parsed;
                    apiData.package_name = apiData.package_name || null;
                    delete apiData.url;
                } else {
                    delete apiData.command;
                    delete apiData.args;
                    delete apiData.env;
                    delete apiData.package_name;
                }

            } else {
                delete apiData.transport;
                delete apiData.command;
                delete apiData.package_name;
                delete apiData.args;
                delete apiData.env;
                delete apiData.url;
                proceedWithSave = true;
            }

            if (!proceedWithSave) {
                setLoading(false);
                return;
            }

            // 处理 examples 数据
            const cleanedExamples = cleanExamplesData(examples);
            apiData.examples = cleanedExamples;
            console.log('准备提交的 examples 数据:', cleanedExamples);

            try {
                if (isEditing) {
                    const updateEndpoint = `${apiEndpointBase}/${initialValues.id}`;
                    await updateData(updateEndpoint, apiData);
                    message.success('MCP Server 更新成功');
                } else {
                    delete apiData.id;
                    const createEndpoint = apiEndpointBase;
                    await createData(createEndpoint, apiData);
                    message.success('MCP Server 创建成功');
                }
                onSuccess();

            } catch (apiError) {
                console.error('API save/update failed:', apiError);
            }

        } catch (errorInfo) {
            if (errorInfo.errorFields) {
                console.log('表单校验失败:', errorInfo);
                message.error('请检查表单字段是否有误.');
            } else {
                console.error('保存 MCP Server 时发生意外错误:', errorInfo);
            }
        } finally {
            setLoading(false);
        }
    };

    const jsonValidator = (type) => (_, value) => {
        const result = validateJsonString(value, type);
        if (result.isValid) {
            return Promise.resolve();
        } else {
            return Promise.reject(new Error(result.error));
        }
    };

    const handleUploadSuccess = (content) => {
        console.log('Upload success, content:', content);
        form.setFieldsValue({
            content: content
        });
        setUploadedContent(content);
        setInputMode('text');
    };

    const handleInputModeChange = (mode) => {
        setInputMode(mode);
        if (mode === 'text' && uploadedContent) {
            form.setFieldsValue({
                content: uploadedContent
            });
        }
    };

    return (
        <Modal
            title={modalTitle}
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={800}
            destroyOnClose={true}
            maskClosable={false}
            className={styles.mcpServerFormModal}
        >
            <Spin spinning={loading || actionLoading}>
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSubmit}
                >
                    <Form.Item
                        name="name"
                        label="名称"
                        rules={[{ required: true, message: '请输入服务名称!' }, { type: 'string', max: 100, message: '名称不能超过100个字符' }]}
                    >
                        <Input placeholder="例如: math_solver, weather_api, my_prompt" disabled={isEditing}/>
                    </Form.Item>
                    <Form.Item name="description" label="描述">
                        <TextArea rows={2} placeholder="此服务的可选描述 (例如: 功能, 用途)" />
                    </Form.Item>

                    <Form.Item name="mcp_type" label="MCP类型" rules={[{ required: true, message: '请选择MCP类型!' }]}>
                        <Select onChange={handleMcpTypeChange} placeholder="选择类型">
                            <Option value="tool">
                                <Tag color="blue">Tool</Tag> (Executable Service)
                            </Option>
                            <Option value="prompt">
                                <Tag color="purple">Prompt</Tag> (Text Template)
                            </Option>
                            <Option value="resource">
                                <Tag color="orange">Resource</Tag> (Static Data/Config)
                            </Option>
                        </Select>
                    </Form.Item>

                    {selectedMcpType === 'tool' && (
                        <>
                            <Form.Item name="transport" label="Transport类型" rules={[{ required: true, message: '请为 Tool 选择Transport类型!' }]}>
                                <Select onChange={handleTransportChange} placeholder="选择Transport类型">
                                    <Option value="stdio">stdio (标准输入/输出)</Option>
                                    <Option value="sse">sse (Server-Sent Events)</Option>
                                </Select>
                            </Form.Item>

                            {selectedTransport === 'stdio' && (
                                <>
                                    <Form.Item name="command" label="执行器" rules={[{ required: true, message: '请为 stdio 选择一个执行器!' }]}>
                                        <Select placeholder="选择执行器" onChange={handleCommandChange}>
                                            <Option value="python">python</Option>
                                            <Option value="uv">uv (uv/uvx)</Option>
                                            <Option value="node">node</Option>
                                            <Option value="npx">npx</Option>
                                            <Option value="docker">docker</Option>
                                        </Select>
                                    </Form.Item>

                                    {(selectedCommand === 'python' || selectedCommand === 'uv') && (
                                        <Form.Item
                                            name="package_name"
                                            label="包名 (Python/uv)"
                                            tooltip={isEditing ? '编辑包名. 点击右侧按钮检查包是否存在及版本.' : '输入所需的Python包名, 可包含版本说明. 创建时系统将检查并提示安装.'}
                                            rules={[
                                                { required: true, message: '请提供Python/uv包名.' },
                                                { pattern: /^[a-zA-Z0-9._\-<>=~!*+!]*$/, message: '包名包含无效字符' }
                                            ]}
                                        >
                                            <Space.Compact style={{ width: '100%' }}>
                                                <Input placeholder='例如: mcp_simple_pubmed 或 requests>=2.20' />
                                                <Button
                                                    icon={<SearchOutlined />}
                                                    onClick={handleManualPackageCheck}
                                                    loading={checkingPackage}
                                                    disabled={loading || actionLoading}
                                                >
                                                    检查包
                                                </Button>
                                            </Space.Compact>
                                        </Form.Item>
                                    )}

                                    <Form.Item
                                        name="args"
                                        label="参数 (JSON 数组)"
                                        tooltip='输入有效的JSON字符串数组, 例如: ["-m", "mcp_simple_pubmed"]. 如果没有参数请留空.'
                                        rules={[{ validator: jsonValidator('array') }]}
                                    >
                                        <TextArea rows={3} placeholder='例如:["-m", "mcp_simple_pubmed"] 或留空' className={styles.jsonTextArea} />
                                    </Form.Item>
                                    <Form.Item
                                        name="env"
                                        label="环境变量 (JSON 对象)"
                                        tooltip='输入有效的JSON对象, 例如: {"API_KEY": "123", "MODE": "dev"}. 如果没有额外的环境变量请留空.'
                                        rules={[{ validator: jsonValidator('object') }]}
                                    >
                                        <TextArea rows={3} placeholder='例如: {"API_KEY": "your-key"} 或留空' className={styles.jsonTextArea} />
                                    </Form.Item>
                                </>
                            )}

                            {selectedTransport && selectedTransport !== 'stdio' && (
                                <Form.Item
                                    name="url"
                                    label="服务地址 (URL)"
                                    rules={[ { required: true, message: `URL是 ${selectedTransport.toUpperCase()} 类型所必须的!` }, { type: 'url', warningOnly: true, message: '请输入有效的URL' }, { validator: (_, value) => { if (!value || /^(https?|wss?):\/\/.+/.test(value)) { return Promise.resolve(); } return Promise.reject(new Error('URL 必须以 http(s):// 或 ws(s):// 开头')); } } ]}
                                >
                                    <Input placeholder={`例如: http://localhost:5000/${selectedTransport}_endpoint`} />
                                </Form.Item>
                            )}
                        </>
                    )}

                    {(selectedMcpType === 'prompt' || selectedMcpType === 'resource') && (
                        <Form.Item
                            name="content"
                            label={selectedMcpType === 'prompt' ? "Prompt 内容" : "Resource 内容"}
                            rules={[{ required: true, message: '内容不能为空!' }]}
                            tooltip={selectedMcpType === 'prompt' ? "输入 Prompt 模板文本." : "输入 Resource 的静态内容"}
                            shouldUpdate={(prevValues, curValues) => prevValues.content !== curValues.content}
                        >
                            <div>
                                <div style={{ marginBottom: 16 }}>
                                    <Radio.Group 
                                        value={inputMode} 
                                        onChange={(e) => handleInputModeChange(e.target.value)}
                                        buttonStyle="solid"
                                    >
                                        <Radio.Button value="text">文本输入</Radio.Button>
                                        <Radio.Button value="upload">文件上传</Radio.Button>
                                    </Radio.Group>
                                </div>
                                
                                {inputMode === 'upload' ? (
                                    <FileUploader onUploadSuccess={handleUploadSuccess} />
                                ) : (
                                    <TextArea
                                        rows={10}
                                        placeholder={selectedMcpType === 'prompt' ? "在此输入您的Prompt模板..." : "在此输入Resource内容..."}
                                        className={styles.jsonTextArea}
                                        defaultValue={uploadedContent || initialValues?.content}
                                    />
                                )}
                            </div>
                        </Form.Item>
                    )}

                    <div className={styles.switchGroup}>
                        <Form.Item 
                            name="is_active" 
                            valuePropName="checked"
                            initialValue={true}
                            label="启用"
                        >
                            <Checkbox>是否启用此 MCP Server</Checkbox>
                        </Form.Item>
                        
                        <Form.Item 
                            name="is_published" 
                            valuePropName="checked"
                            initialValue={false}
                            label="发布"
                        >
                            <Checkbox>是否对外发布此 MCP Server</Checkbox>
                        </Form.Item>
                    </div>

                    <Divider orientation="center" className={styles.divider}>
                        示例配置 {examples.length > 0 ? `(${examples.length})` : ''}
                        <Tooltip title="查看示例配置说明">
                            <QuestionCircleOutlined 
                                onClick={showExampleHelp} 
                                style={{ marginLeft: '8px', cursor: 'pointer', color: '#1890ff' }}
                            />
                        </Tooltip>
                        {debugInfo && <span style={{ fontSize: '12px', color: '#888', marginLeft: '10px' }}>{debugInfo}</span>}
                    </Divider>
                    
                    <div className={styles.examplesSection}>
                        <div style={{ marginBottom: 16 }}>
                            <Button 
                                type="dashed" 
                                onClick={() => {
                                    setExamples([...examples, createNewExample()]);
                                    setDebugInfo(`已添加新示例，当前共 ${examples.length + 1} 个示例`);
                                }}
                                icon={<PlusOutlined />}
                            >
                                添加示例
                            </Button>
                        </div>
                        
                        {examples.length === 0 && (
                            <div style={{ textAlign: 'center', padding: '20px', color: '#888' }}>
                                暂无示例数据，请点击"添加示例"按钮创建
                            </div>
                        )}
                        
                        {examples.map((example, index) => (
                            <Card 
                                key={index} 
                                style={{ marginBottom: 16 }}
                                title={`示例 ${index + 1}`}
                                extra={
                                    <Button
                                        type="text"
                                        danger
                                        onClick={() => {
                                            setExamples(examples.filter((_, idx) => idx !== index));
                                            setDebugInfo(`已删除示例，当前剩余 ${examples.length - 1} 个示例`);
                                        }}
                                    >
                                        删除
                                    </Button>
                                }
                            >
                                <Form.Item
                                    label="示例文本"
                                    style={{ marginBottom: 16 }}
                                >
                                    <TextArea
                                        value={example.text}
                                        onChange={(e) => {
                                            const newExamples = [...examples];
                                            newExamples[index].text = e.target.value;
                                            setExamples(newExamples);
                                        }}
                                        rows={4}
                                        placeholder="请输入示例文本"
                                    />
                                </Form.Item>
                                
                                <Form.Item
                                    label="可对话"
                                    style={{ marginBottom: 16 }}
                                >
                                    <Checkbox
                                        checked={example.conversable}
                                        onChange={(e) => {
                                            const newExamples = [...examples];
                                            newExamples[index].conversable = e.target.checked;
                                            setExamples(newExamples);
                                        }}
                                    >
                                        是否允许对话
                                    </Checkbox>
                                </Form.Item>
                                
                                <Form.Item
                                    label="附件"
                                >
                                    <Upload
                                        listType="picture-card"
                                        fileList={(example.attachments || []).map(att => ({
                                            uid: att.url,
                                            name: att.filename,
                                            status: 'done',
                                            url: att.url,
                                            thumbUrl: att.url
                                        }))}
                                        onRemove={(file) => {
                                            const newExamples = [...examples];
                                            newExamples[index].attachments = (newExamples[index].attachments || []).filter(
                                                att => att.url !== file.uid
                                            );
                                            setExamples(newExamples);
                                            return true;
                                        }}
                                        beforeUpload={async (file) => {
                                            try {
                                                setLoading(true);
                                                const url = await uploadImageToServer(URL.createObjectURL(file), file.name);
                                                
                                                const newExamples = [...examples];
                                                if (!newExamples[index].attachments) {
                                                    newExamples[index].attachments = [];
                                                }
                                                
                                                newExamples[index].attachments.push({
                                                    content: file.name,
                                                    url: url,
                                                    filename: file.name
                                                });
                                                
                                                setExamples(newExamples);
                                                message.success(`${file.name} 上传成功`);
                                            } catch (error) {
                                                console.error('上传失败:', error);
                                                message.error(`${file.name} 上传失败`);
                                            } finally {
                                                setLoading(false);
                                            }
                                            return false;
                                        }}
                                    >
                                        <div>
                                            <PlusOutlined />
                                            <div style={{ marginTop: 8 }}>上传</div>
                                        </div>
                                    </Upload>
                                </Form.Item>
                            </Card>
                        ))}
                    </div>

                    <div className={styles.formActions}>
                        <Button onClick={onCancel}>取消</Button>
                        <Button 
                            type="primary" 
                            htmlType="submit" 
                            loading={loading}
                            disabled={actionLoading}
                        >
                            保存
                        </Button>
                    </div>
                </Form>
            </Spin>
        </Modal>
    );
};

export default McpServerForm;