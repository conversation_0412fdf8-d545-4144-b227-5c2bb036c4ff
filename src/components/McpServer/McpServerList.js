import React, { useState, useEffect, useCallback } from 'react';
// Added Tag to imports
import { Table, Button, Space, Popconfirm, message, Tag, Tooltip, Input, Switch } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, GlobalOutlined } from '@ant-design/icons';
import { fetchBulk, deleteData, deleteBulk, formatDate, updateData } from '../Routers/Router';
import McpServerForm from './McpServerForm';
import styles from './McpServerList.module.css';
import { useSelector } from 'react-redux';
import { MCP_SERVER_ENDPOINT } from '../Configs/Config';
import SidebarList from '../SidebarList';
const { Search } = Input;

const McpServerList = () => {
    const [dataSource, setDataSource] = useState([]);
    const [loading, setLoading] = useState(false);
    const [updatingRecord, setUpdatingRecord] = useState(null);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [editingRecord, setEditingRecord] = useState(null);
    const [filters, setFilters] = useState({});

    const { user_info } = useSelector((state) => state.user);
    const sortField = 'updated_at';
    const sortOrder = 'desc';
    // --- Fetch Data Function --- (No changes needed here unless adding backend filtering for mcp_type)
    const fetchData = useCallback(async (params = {}) => {
        if (!user_info?.username) {
             console.warn("Username not available yet, skipping fetch.");
             setLoading(false);
             return;
         }

        setLoading(true);
        try {
            const backendParams = {
                 skip: ((params.current || pagination.current) - 1) * (params.pageSize || pagination.pageSize),
                 limit: params.pageSize || pagination.pageSize,
                 ...filters,
            };

            const endpoint_api = `${MCP_SERVER_ENDPOINT}/bulk?username=${user_info.username}&sort_by=${sortField}&sort_order=${sortOrder}`;
            const result = await fetchBulk(endpoint_api, backendParams);

            setDataSource(result.data || []);
            setPagination(prev => ({
                ...prev,
                total: result.count || 0,
                current: params.current || pagination.current,
                pageSize: params.pageSize || pagination.pageSize,
            }));
        } catch (error) {
            message.error(`Failed to load MCP Servers list: ${error.message}`);
            setDataSource([]);
            setPagination(prev => ({ ...prev, current: 1, total: 0 }));
        } finally {
            setLoading(false);
        }
    }, [pagination.current, pagination.pageSize, filters, user_info?.username]);

    useEffect(() => {
        if (user_info?.username) {
             fetchData();
        }
    }, [fetchData, user_info?.username]);

    // --- handleTableChange --- (No changes needed unless adding frontend/backend filtering for mcp_type)
    const handleTableChange = (paginationParams, tableFilters, sorter) => {
        const newFilters = { ...filters };
        // --- Placeholder for adding mcp_type filter mapping ---
        // if (tableFilters.mcp_type) {
        //     newFilters.mcp_type_in = tableFilters.mcp_type.join(',');
        // } else {
        //     delete newFilters.mcp_type_in;
        // }
        // --- End Placeholder ---

        setFilters(newFilters);
        setPagination(prev => ({
            ...prev,
            current: paginationParams.current,
            pageSize: paginationParams.pageSize,
        }));
        // fetchData triggered by useEffect dependencies
    };

    // --- handleSearch --- (Remains the same, searches by name)
    const handleSearch = (value) => {
        const newFilters = { ...filters, name__like: value || undefined };
        if (!value) {
            delete newFilters.name__like;
        }
        setFilters(newFilters);
        setPagination(prev => ({ ...prev, current: 1 }));
    };

    // --- Add/Edit/Delete Handlers --- (No changes needed, edit passes the full record)
    const handleAdd = () => {
        setEditingRecord(null);
        setIsModalVisible(true);
    };

    const handleEdit = (record) => {
        // Pass the full record, which now includes mcp_type and content
        setEditingRecord(record);
        setIsModalVisible(true);
    };

    const handleDelete = async (id) => {
        setLoading(true);
        try {
            const endpoint_api = `${MCP_SERVER_ENDPOINT}/${id}`;
            await deleteData(endpoint_api);
            message.success('MCP Server deleted successfully');
            const newTotal = pagination.total - 1;
            const newCurrent = (dataSource.length === 1 && pagination.current > 1)
                                ? pagination.current - 1
                                : pagination.current;
            fetchData({ current: newCurrent, pageSize: pagination.pageSize });
            setSelectedRowKeys(keys => keys.filter(key => key !== id));
        } catch (error) {
            message.error(`Failed to delete MCP Server: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    // 新增：处理切换发布状态的函数
    const handleTogglePublished = async (record) => {
        if (updatingRecord === record.id) {
            // 防止重复点击
            return;
        }

        setUpdatingRecord(record.id);
        try {
            const endpoint_api = `${MCP_SERVER_ENDPOINT}/${record.id}`;
            // 只更新发布状态，其他字段保持不变
            const updatedData = {
                is_published: !record.is_published
            };
            
            await updateData(endpoint_api, updatedData);
            
            // 更新本地数据，避免重新加载整个列表
            setDataSource(prev => prev.map(item => 
                item.id === record.id 
                    ? { ...item, is_published: !item.is_published } 
                    : item
            ));
            
            message.success(`${record.name} ${!record.is_published ? '已发布' : '已取消发布'}`);
        } catch (error) {
            message.error(`修改发布状态失败: ${error.message}`);
        } finally {
            setUpdatingRecord(null);
        }
    };

    const handleBulkDelete = async () => {
         if (selectedRowKeys.length === 0) {
             message.warning('Please select servers to delete.');
             return;
         }
         setLoading(true);
         try {
            const endpoint_api = `${MCP_SERVER_ENDPOINT}/bulk`;
            // Body structure depends on backend, assuming it expects 'mcp_server_ids'
            await deleteBulk(endpoint_api, { mcp_server_ids: selectedRowKeys });
            message.success(`${selectedRowKeys.length} MCP Server(s) deleted successfully`);
            setSelectedRowKeys([]);
            fetchData({ current: 1, pageSize: pagination.pageSize });
         } catch (error) {
             message.error(`Failed to delete MCP Servers: ${error.message}`);
         } finally {
             setLoading(false);
         }
     };

    // --- handleModalClose --- (No changes needed)
    const handleModalClose = async (refresh = false) => {
        setIsModalVisible(false);
        setEditingRecord(null);
        if (refresh) {
            const pageToFetch = editingRecord ? pagination.current : 1;
            try {
                await fetchData({ current: pageToFetch, pageSize: pagination.pageSize });
            } catch (error) {
                 message.error(`Failed to refresh server list after action: ${error.message}`);
            }
        }
    };

    // --- rowSelection --- (No changes needed)
    const onSelectChange = (newSelectedRowKeys) => {
        setSelectedRowKeys(newSelectedRowKeys);
    };
    const rowSelection = { selectedRowKeys, onChange: onSelectChange };
    const hasSelected = selectedRowKeys.length > 0;

    // --- Columns Definition ---
    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: 180,
            fixed: 'left', // Keep name visible
            ellipsis: true,
        },
        // --- NEW Column for MCP Type ---
        {
            title: '类型',
            dataIndex: 'mcp_type',
            key: 'mcp_type',
            width: 100,
            // Optional: Add filters if backend supports filtering by mcp_type
            // filters: [
            //     { text: 'Tool', value: 'tool' },
            //     { text: 'Prompt', value: 'prompt' },
            //     { text: 'Resource', value: 'resource' },
            // ],
            render: (mcpType) => {
                const type = mcpType || 'tool'; // Default to tool if missing
                let color = 'blue';
                let text = 'Tool';
                if (type === 'prompt') {
                    color = 'purple';
                    text = 'Prompt';
                } else if (type === 'resource') {
                    color = 'orange';
                    text = 'Resource';
                }
                return <Tag color={color}>{text}</Tag>;
            }
        },
        {
            title: 'Transport',
            dataIndex: 'transport',
            key: 'transport',
            width: 100,
            // Only render transport if mcp_type is 'tool'
            render: (transport, record) =>
                record.mcp_type === 'tool' ? (
                    <Tag>{transport?.toUpperCase() || 'N/A'}</Tag>
                 ) : (
                     <Tag>N/A</Tag>
                 ),
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            width: 230,
            ellipsis: true,
            render: (text) => <Tooltip title={text}>{text || '-'}</Tooltip>,
        },
        {
            title: '状态',
            dataIndex: 'is_active',
            key: 'is_active',
            width: 90,
            render: (isActive) => (
                <Tag color={isActive ? 'green' : 'red'}>
                    {isActive ? 'Active' : 'Inactive'}
                </Tag>
            ),
            // filters: [ // Keep if needed
            //     { text: 'Active', value: true },
            //     { text: 'Inactive', value: false },
            // ],
        },
        // 新增：发布状态列
        {
            title: '发布',
            dataIndex: 'is_published',
            key: 'is_published',
            width: 90,
            render: (isPublished, record) => (
                <Switch
                    checkedChildren="已发布"
                    unCheckedChildren="未发布"
                    checked={!!isPublished}
                    loading={updatingRecord === record.id}
                    onChange={() => handleTogglePublished(record)}
                />
            ),
        },
        {
            title: '创建者',
            dataIndex: 'username',
            key: 'username',
            width: 120,
            render: (username) => username || 'N/A',
        },
        {
            title: '更新时间',
            dataIndex: 'updated_at',
            key: 'updated_at',
            width: 160,
            render: (text) => formatDate(text), // Using imported formatDate
            // sorter: true, // Keep if needed
        },
        {
            title: '操作',
            key: 'actions',
            fixed: 'right',
            width: 120,
            render: (_, record) => (
                <Space size="small">
                    <Tooltip title="Edit">
                        <Button
                            type="primary"
                            icon={<EditOutlined />}
                            size="small"
                            onClick={() => handleEdit(record)} // Passes full record
                            aria-label={`Edit ${record.name}`}
                        />
                    </Tooltip>
                    <Popconfirm
                        title={`确认删除 "${record.name}"?`}
                        onConfirm={() => handleDelete(record.id)}
                        okText="Yes"
                        cancelText="No"
                    >
                         <Tooltip title="Delete">
                            <Button
                                type="primary"
                                danger
                                icon={<DeleteOutlined />}
                                size="small"
                                aria-label={`Delete ${record.name}`}
                            />
                        </Tooltip>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    return (
        <div className={styles.container}>
             <div className="header-container">
                <h1 className="page-title">MCP服务列表</h1>
                <SidebarList />
            </div>
            <div className={styles.toolbar}>
                 <Space>
                     <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                        新增
                    </Button>
                     {hasSelected && (
                         <Popconfirm
                             title={`Delete ${selectedRowKeys.length} selected servers?`}
                             onConfirm={handleBulkDelete}
                             okText="Yes"
                             cancelText="No"
                             disabled={!hasSelected || loading}
                         >
                             <Button danger disabled={!hasSelected || loading} icon={<DeleteOutlined />}>
                                 Delete Selected ({selectedRowKeys.length})
                             </Button>
                         </Popconfirm>
                     )}
                 </Space>
                 <Space>
                    <Search
                        placeholder="Search by name..."
                        onSearch={handleSearch}
                        style={{ width: 250 }}
                        allowClear
                        enterButton
                        loading={loading}
                    />
                    {/* Add more AntD filter components here if needed (e.g., for MCP Type) */}
                </Space>
            </div>

            <Table
                rowKey="id"
                columns={columns}
                dataSource={dataSource}
                loading={loading}
                pagination={pagination}
                rowSelection={rowSelection}
                onChange={handleTableChange}
                // Increased scroll width slightly to accommodate the new column
                scroll={{ x: 1500 }}
                className={styles.mcpServerTable}
            />

            {isModalVisible && (
                 <McpServerForm
                    visible={isModalVisible}
                    onCancel={() => handleModalClose(false)}
                    onSuccess={() => handleModalClose(true)}
                    initialValues={editingRecord} // Passes the full record including new fields
                    apiEndpointBase={MCP_SERVER_ENDPOINT}
                />
            )}
        </div>
    );
};

export default McpServerList;