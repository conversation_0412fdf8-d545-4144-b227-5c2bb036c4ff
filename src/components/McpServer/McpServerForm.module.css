/* Add specific styles for the form if needed */
/* For example, spacing for conditional fields */
.conditionalGroup {
    padding: 10px;
    margin-bottom: 15px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
}

/* Style JSON text areas if desired */
.jsonTextArea textarea {
    font-family: monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

.form {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.jsonTextArea {
  font-family: monospace;
  white-space: pre;
  overflow-x: auto;
}

.mcpServerFormModal {
  width: 800px;
  max-width: 90vw;
}

.switchGroup {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.divider {
  margin: 24px 0;
  font-size: 14px;
}

.examplesSection {
  margin-bottom: 24px;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

@media (max-width: 768px) {
  .form {
    padding: 16px;
  }
  
  .switchGroup {
    flex-direction: column;
    gap: 10px;
  }
}