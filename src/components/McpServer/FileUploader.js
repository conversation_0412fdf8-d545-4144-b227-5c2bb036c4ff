import React, { useState } from 'react';
import { Upload, Button, message, Spin } from 'antd';
import { UploadOutlined, InboxOutlined } from '@ant-design/icons';
import styles from './FileUploader.module.css';
import {FILE_MANAGER_ENDPOINT} from '../Configs/Config';

const { Dragger } = Upload;

const FileUploader = ({ onUploadSuccess }) => {
  const [loading, setLoading] = useState(false);
  const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
  const handleUpload = async (file) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${FILE_MANAGER_ENDPOINT}/upload_file_chat_v2/`, {
        method: 'POST',
        body: formData,
        headers: {
            'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(data,'data')
      if (data.content) {
        onUploadSuccess(data.content);
        message.success('文件上传成功');
      } else {
        message.error('文件上传失败：未获取到内容');
      }
    } catch (error) {
      console.error('上传失败:', error);
      message.error('文件上传失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
    return false; // 阻止默认上传行为
  };

  return (
    <div className={styles.uploadContainer}>
      <Dragger
        beforeUpload={handleUpload}
        showUploadList={false}
        accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.wav,.mp3,.mp4,.webm"
        multiple={false}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
        <p className="ant-upload-hint">
          支持的文件格式: PDF, DOC, DOCX, PPT, PPTX, TXT, WAV, MP3, MP4, WEBM
        </p>
      </Dragger>
    </div>
  );
};

export default FileUploader; 