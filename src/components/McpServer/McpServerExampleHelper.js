/**
 * MCP Server 示例配置的工具函数
 */

/**
 * 从后端解析示例数据，确保格式一致
 * @param {any} rawExamples - 从后端获取的原始示例数据
 * @returns {Array} - 处理后的示例数组
 */
export const parseExamplesData = (rawExamples) => {
  console.log('解析 MCP Server 示例数据，原始数据类型:', typeof rawExamples);
  
  // 如果是 undefined 或 null，返回空数组
  if (rawExamples == null) {
    console.log('示例数据为空，返回空数组');
    return [];
  }
  
  let parsedExamples = rawExamples;
  
  // 如果是字符串，尝试解析 JSON
  if (typeof rawExamples === 'string') {
    try {
      parsedExamples = JSON.parse(rawExamples);
      console.log('成功从字符串解析示例数据');
    } catch (err) {
      console.error('解析示例数据字符串失败:', err);
      return [];
    }
  }
  
  // 确保是数组
  if (!Array.isArray(parsedExamples)) {
    console.warn('示例数据不是数组格式，返回空数组');
    return [];
  }
  
  // 处理每个示例，确保符合期望的格式
  return parsedExamples.map(example => ({
    text: example.text || '',
    conversable: example.conversable === undefined ? true : example.conversable,
    attachments: Array.isArray(example.attachments) ? example.attachments : []
  }));
};

/**
 * 清理示例数据，确保提交给后端的数据格式一致
 * @param {Array} examples - 前端的示例数组
 * @returns {Array} - 清理后的示例数组
 */
export const cleanExamplesData = (examples) => {
  if (!Array.isArray(examples)) {
    console.warn('cleanExamplesData: 输入不是数组，返回空数组');
    return [];
  }
  
  return examples.map(example => ({
    text: example.text || '',
    conversable: !!example.conversable,
    attachments: Array.isArray(example.attachments) ? 
      example.attachments.map(att => ({
        content: att.content || '',
        url: att.url || '',
        filename: att.filename || ''
      })) : []
  }));
};

/**
 * 创建一个新的空示例对象
 * @returns {Object} - 新的示例对象
 */
export const createNewExample = () => ({
  text: '',
  conversable: true,
  attachments: []
});

/**
 * 生成示例的帮助信息弹窗内容
 * @returns {Object} - Modal.info 的 content 参数
 */
export const getExampleHelpContent = () => (
  <div>
    <p>示例是用于演示 MCP Server 功能的样本数据，可以包含以下内容：</p>
    <ul>
      <li><b>示例文本</b>：需要展示的文本内容</li>
      <li><b>可对话</b>：是否允许用户与该示例进行交互</li>
      <li><b>附件</b>：可以上传图片等附件，支持各种格式</li>
    </ul>
    <p>每个 MCP Server 可以添加多个示例，用于不同场景的演示。</p>
    <p>添加的示例将与 MCP Server 数据一起被保存。</p>
  </div>
); 