import { createSlice } from '@reduxjs/toolkit';



const getUserInfoFromLocalStorage = () => {
    const userInfoString = localStorage.getItem('user_info');
    if (typeof userInfoString === 'object' && userInfoString !== null) {
        return userInfoString
    } else {
      try {
        const parsedUserInfo = JSON.parse(userInfoString);
        return parsedUserInfo
      } catch (error) {
        console.error('Failed to parse userInfoString:', error);
        return {}
      }
    }
};
const initialState = {
    user_info:getUserInfoFromLocalStorage()||{}
};

export const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        setUserInfo: (state, action) => {
            state.user_info = action.payload;
            // 更新localStorage中的用户名
            localStorage.setItem('user_info', JSON.stringify(state.user_info));
        },
        logoutUserInfo: (state) => {
            state.user_info = {};
            // 清除localStorage中的用户名
            localStorage.removeItem('user_info');
        },
    },
});

// Action creators are generated for each case reducer function
export const { setUserInfo,logoutUserInfo } = userSlice.actions;

export default userSlice.reducer;
