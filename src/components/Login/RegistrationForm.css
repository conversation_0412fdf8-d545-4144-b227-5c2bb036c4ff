.registration-container {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 300px;
    /* No additional margin or padding that would disrupt centering */
}

.registration-title {
    margin-bottom: 30px;
    color: #333;
    font-size: 24px;
    text-align: center;
}

.registration-form {
    display: flex;
    flex-direction: column;
}

.input-container {
    margin-bottom: 20px;
}

.input-container input {
    width: 100%;
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
    box-sizing: border-box; /* Ensure padding doesn't affect input size */
}

.registration-button, .toggle-button {
    background-color: #28a745; /* Different color for distinction */
    color: white;
    padding: 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 18px;
    transition: background-color 0.3s;
    margin-top: 10px;
}

.registration-button:hover, .toggle-button:hover {
    background-color: #1c7430; /* Darker green on hover */
}

.toggle-button {
    background-color: #ffc107; /* Yellow for toggle to login button */
}

.toggle-button:hover {
    background-color: #e0a800;
}
