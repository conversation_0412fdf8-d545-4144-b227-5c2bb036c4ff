import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import './loginForm.css';
import { REGISTER_API_NAME,WORKFLOWS_NAME } from '../Configs/Config';
import { setUserInfo } from './userSlice'; // 导入并重命名setUsername
// import { setConversations } from '../Dashboard/dashboardSlice';
import { useDispatch } from 'react-redux';
import {login } from '../Dashboard/Chat/utils/apiUtil';


const LoginForm = ({ prefillUsername = '', prefillPassword = '' }) => {
    const [username, setUsername] = useState(prefillUsername);
    const [password, setPassword] = useState(prefillPassword);
    const location = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const handleGoToRegister = () => {
        navigate(`${REGISTER_API_NAME}`);  // 使用注册页面的路由路径
    };

    const handleLogin = async (event) => {
        event.preventDefault();
        try {
            
            const login_info = await login(username, password);
            localStorage.setItem('token', login_info.access_token);  // Store the access token in localStorage
            // 更新 Redux 和 localStorage
            dispatch(setUserInfo(login_info)); // 使用重命名的 
            const from = location.state?.from || `${WORKFLOWS_NAME}`;
            navigate(from, { replace: true });
        }catch (error) {
                console.error('Login attempt failed:', error);
            }
    };

    return (
        <div className="login-background">
            <div className="login-container">
                <h2 className="login-title">欢迎使用管理系统</h2>
                <form onSubmit={handleLogin} className="login-form">
                    <div className="input-container">
                        <input
                            id="username"
                            type="text"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder="用户名"
                            className="login-input"
                        />
                    </div>
                    <div className="input-container">
                        <input
                            id="password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="密码"
                            className="login-input"
                        />
                    </div>
                    <button type="submit" className="login-button">登录</button>
                    <button type="button" onClick={handleGoToRegister} className="toggle-button">无账户？注册</button>
                </form>
            </div>
        </div>
    );
};

export default LoginForm;
