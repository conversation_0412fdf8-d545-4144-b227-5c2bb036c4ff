.login-background {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('./dagu.png'); /* 假设图片名称为background-image.png */
  background-size: cover;
  background-position: center;
}

.login-container {
  background: rgba(255, 255, 255, 0.8);
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: 300px;
}

.login-title {
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
  text-align: center;
}

.login-form {
  display: flex;
  flex-direction: column;
}

.input-container {
  margin-bottom: 20px;
}

.input-container input.login-input {
  width: 100%;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  box-sizing: border-box; /* 添加box-sizing确保padding不会增加输入框的大小 */
}

.login-button {
  background-color: #007bff;
  color: white;
  padding: 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.3s;
}

.login-button:hover {
  background-color: #0056b3;
}

.logout-button {
  background-color: #007bff; /* 设置按钮的背景颜色为蓝色 */
  color: white;
  border: none;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 12px; /* 调整字体大小 */
  /* margin: 2px 1px; */
  cursor: pointer;
  border-radius: 2px;
}

.logout-button:hover {
  background-color: #0056b3; /* 设置按钮悬停时的背景颜色为深蓝色 */
}
