// import { useState, useEffect, useCallback } from 'react';

// export const useAuth = () => {
//   const [isAuthenticated, setIsAuthenticated] = useState(false);

//   useEffect(() => {
//     // 在组件挂载时检查用户是否已登录
//     checkAuthStatus();
//   }, []);

//   const checkAuthStatus = useCallback(() => {
//     // 这里可以检查 localStorage, sessionStorage 或发送请求到服务器
//     // 为了示例，我们只检查 localStorage
//     const token = localStorage.getItem('token');
//     setIsAuthenticated(!!token);
//   }, []);

//   return {
//     isAuthenticated,
//     checkAuthStatus,
//   };
// };