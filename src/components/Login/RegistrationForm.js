// RegistrationForm.js
import React, { useState } from 'react';
import { register } from '../Dashboard/Chat/utils/apiUtil';
import './RegistrationForm.css';
import { useNavigate } from 'react-router-dom';
import { LOGIN_API_NAME } from '../Configs/Config';

const RegistrationForm = ({ setLoginCredentials }) => {
    const [username, setUsername] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const navigate = useNavigate();

    const handleRegister = async (event) => {
        event.preventDefault();
        setLoginCredentials(username, password);
        await register({username,email,password});
        navigate(`${LOGIN_API_NAME}`);
    };

    return (
        <div className="login-background">
            <div className="registration-container">
                <h2 className="registration-title">注册新账户</h2>
                <form onSubmit={handleRegister} className="registration-form">
                    <div className="input-container">
                        <input
                            type="text"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder="用户名"
                            required
                        />
                    </div>
                    <div className="input-container">
                        <input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder="电子邮箱"
                            required
                        />
                    </div>
                    <div className="input-container">
                        <input
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="密码"
                            required
                        />
                    </div>
                    <button type="submit" className="registration-button">注册</button>
                    <button type="button" onClick={() => navigate(`${LOGIN_API_NAME}`)} className="toggle-button">已有账户？登录</button>
                </form>
            </div>
    </div>
    );
};

export default RegistrationForm;
