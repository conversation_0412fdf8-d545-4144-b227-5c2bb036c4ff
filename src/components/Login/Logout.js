// Logout.js
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { LOGIN_API_NAME } from '../Configs/Config';
import { logoutUserInfo } from './userSlice';
import './loginForm.css';
const Logout = () => {
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleLogout = () => {
    if (window.confirm('确定要退出登录吗？')) {
      setIsLoggingOut(true);
      
      // 执行注销操作
      localStorage.removeItem('token');
      dispatch(logoutUserInfo());
      // localStorage.removeItem('user_info');
      
      // 模拟异步操作
      setTimeout(() => {
        setIsLoggingOut(false);
        navigate(LOGIN_API_NAME);
      }, 1000);
    }
  };

  return (
    <button 
      onClick={handleLogout} 
      className={`logout-button ${isLoggingOut ? 'logging-out' : ''}`}
      disabled={isLoggingOut}
    >
      {isLoggingOut ? '正在退出...' : '退出登录'}
    </button>
  );
};

export default Logout;