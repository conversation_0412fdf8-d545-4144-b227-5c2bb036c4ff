// src/services/WebSocketService.js
export const chatClient = async ({ queryBody, websocket_url, onStream }) => {
    console.log('Generation Query:', queryBody);
    console.log(websocket_url, 'websocket_url');
  
    return new Promise((resolve, reject) => {
      let isResolved = false;
  
      const ws = new WebSocket(websocket_url);
  
      const connectionTimeout = setTimeout(() => {
        if (!isResolved) {
          ws.close();
          reject(new Error('WebSocket connection timeout'));
        }
      }, 1000000); // Consider reducing the timeout duration if necessary
  
      ws.onopen = () => {
        console.log('WebSocket Connected');
        try {
          ws.send(JSON.stringify(queryBody));
          console.log('Sent request:', queryBody);
        } catch (sendError) {
          console.error('Error sending message:', sendError);
          clearTimeout(connectionTimeout);
          reject(sendError);
        }
      };
  
      ws.onmessage = (event) => {
        try {
          const responseData = JSON.parse(event.data);
          // console.log('Received message:', responseData);
  
          // Pass the entire responseData to the onStream callback
          if (onStream && typeof onStream === 'function') {
            onStream(responseData);
          }
  
          if (responseData.is_completed) {
            console.log('Received completion signal');
            ws.close();
            clearTimeout(connectionTimeout);
            isResolved = true;
            resolve(responseData);
          }
        } catch (error) {
          console.error('Error processing message:', error);
          clearTimeout(connectionTimeout);
          ws.close();
          reject(error);
        }
      };
  
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        clearTimeout(connectionTimeout);
        if (!isResolved) {
          reject(new Error('WebSocket encountered an error'));
        }
      };
  
      ws.onclose = (event) => {
        console.log('WebSocket connection closed:', event.reason);
        clearTimeout(connectionTimeout);
        if (!isResolved) {
          reject(new Error(`WebSocket closed unexpectedly: ${event.reason}`));
        }
      };
    });
  };
  