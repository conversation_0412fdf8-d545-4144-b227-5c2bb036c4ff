import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Spin, message, Button, Modal, Radio } from 'antd';
import { fetchData } from '../Routers/Router';
import { ARTICLE_ENDPOINT } from '../Configs/Config';
import FileViewer from './FileViewerShare';
import styles from './UserArticleDetail.module.css';  // Using CSS Module

const UserArticleDetail = () => {
  const { id } = useParams(); // Get article ID from route
  const [article, setArticle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hasPaid, setHasPaid] = useState(false); // Payment status
  const [isModalVisible, setIsModalVisible] = useState(false); // Payment modal control
  const [isDownloadModalVisible, setIsDownloadModalVisible] = useState(false); // Download confirmation modal
  const [fileToDownload, setFileToDownload] = useState(null); // Current file to download
  const [selectedFormat, setSelectedFormat] = useState(null); // Selected download format

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        const result = await fetchData(`${ARTICLE_ENDPOINT}/${id}`);
        setArticle(result);
        
        // Extract the first URL for FileViewer (preferably PDF)
        const pdfUrlObj = result.url.find(file => file.type === 'pdf');
        const defaultUrlObj = pdfUrlObj || result.url[0];
        setFileToDownload(defaultUrlObj ? defaultUrlObj.url : null); // Ensure URL exists
      } catch (error) {
        console.error('Failed to fetch article details:', error);
        message.error('Failed to fetch article details');
      } finally {
        setLoading(false);
      }
    };

    fetchArticle();
  }, [id]);

  // Simulate payment process
  const handlePayment = () => {
    setTimeout(() => {
      message.success('Payment successful');
      setHasPaid(true); // Update payment status after success
      setIsModalVisible(false); // Hide payment modal
      setIsDownloadModalVisible(true); // Show download confirmation modal
    }, 2000); // Assume payment takes 2 seconds
  };

  // Handle download confirmation modal
  const handleDownload = () => {
    if (fileToDownload && selectedFormat) {
      let downloadUrl = fileToDownload;

      // Optionally, modify the download URL based on selected format
      // For example, convert PDF to Word if needed
      // This example assumes the URLs for different formats are available
      const formatObj = article.url.find(file => file.type === selectedFormat);
      if (formatObj) {
        downloadUrl = formatObj.url;
      } else {
        message.warning(`Selected format ${selectedFormat} is not available`);
        return;
      }

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', `file.${selectedFormat}`); // Set file name and format
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setIsDownloadModalVisible(false); // Hide download confirmation modal
    } else {
      message.warning('Please select a download format');
    }
  };

  // File download button, show download confirmation modal after payment
  const showDownloadModal = () => {
    if (hasPaid) {
      setIsDownloadModalVisible(true); // Show download confirmation modal
    } else {
      setIsModalVisible(true); // Show payment modal
    }
  };

  if (loading) {
    return <Spin tip="Loading..." />;
  }

  if (!article) {
    return <div>Article not found</div>;
  }

  // Extract the URL for FileViewer (preferably PDF)
  const pdfUrlObj = article.url.find(file => file.type === 'pdf');
  const fileViewerUrl = pdfUrlObj ? pdfUrlObj.url : (article.url[0] ? article.url[0].url : null);

  return (
    <div style={{ padding: '20px' }}>
      {/* Use FileViewer component for file preview */}
      {fileViewerUrl && (
        <div>
          <FileViewer file_url={fileViewerUrl} page_id={1} scale={1.2} /> {/* Pass scale parameter */}
          {/* Use CSS Module to control button layout and styles */}
          <div className={styles.buttonContainer}>
            <Button 
              className={styles.downloadButton} 
              size="small" 
              onClick={showDownloadModal}
            >
              {hasPaid ? 'Download File' : 'Pay and Download'}
            </Button>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      <Modal
        title="Payment Required"
        open={isModalVisible}
        onOk={handlePayment}
        onCancel={() => setIsModalVisible(false)}
        okText="Confirm Payment"
        cancelText="Cancel"
      >
        <p>Downloading this file requires a payment of 500 Tokens. Do you wish to proceed?</p>
      </Modal>

      {/* Download Confirmation Modal */}
      <Modal
        title="Confirm Download"
        open={isDownloadModalVisible}
        onOk={handleDownload}
        onCancel={() => setIsDownloadModalVisible(false)}
        okText="Confirm Download"
        cancelText="Cancel"
        okButtonProps={{ disabled: !selectedFormat }} // Disable download button if no format selected
      >
        <p>Are you sure you want to download this file?</p>
        <Radio.Group 
          onChange={(e) => setSelectedFormat(e.target.value)} 
          value={selectedFormat}
        >
          {article.url.map((file, index) => (
            <Radio key={index} value={file.type}>
              {file.type.toUpperCase()}
            </Radio>
          ))}
        </Radio.Group>
      </Modal>
    </div>
  );
};

export default UserArticleDetail;
