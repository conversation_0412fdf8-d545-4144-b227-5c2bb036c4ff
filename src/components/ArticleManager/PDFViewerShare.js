import React, { useState, useMemo, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import styles from './PDFViewer.module.css';  // 使用 CSS Modules 加载样式

// Set the PDF.js worker source
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

const PDFViewer = ({ file_url, page_id, initialScale = 0.9 }) => {
  const [numPages, setNumPages] = useState(null);
  const [page_number, setPageNumber] = useState(1);
  const [scale, setScale] = useState(initialScale);

  const options = useMemo(() => ({
    cMapUrl: 'cmaps/',
    cMapPacked: true,
  }), []);

  useEffect(() => {
    if (page_id && page_id > 0) {
      setPageNumber(page_id);
    } else {
      setPageNumber(1);
    }
  }, [page_id]);

  function onDocumentLoadSuccess({ numPages }) {
    setNumPages(numPages);
    setPageNumber(page_id ? page_id : 1);
  }

  function changePage(offset) {
    setPageNumber(prevPageNumber => Math.max(1, Math.min(prevPageNumber + offset, numPages)));
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.1, 3.0));
  };

  const zoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.1, 0.5));
  };

  return (
    <div className={styles.pdfViewerContainer}>
      <div className={styles.navigationPdf}>
        <button onClick={previousPage} disabled={page_number <= 1}>
          Prev
        </button>
        <span>
          Page {page_number} of {numPages || '--'}
        </span>
        <button onClick={nextPage} disabled={page_number >= (numPages || 0)}>
          Next
        </button>
        <button onClick={zoomIn} disabled={scale >= 3.0}>+</button>
        <button onClick={zoomOut} disabled={scale <= 0.5}>-</button>
      </div>
      <div className={styles.pdfViewer}>
        <Document
          file={file_url}
          onLoadSuccess={onDocumentLoadSuccess}
          options={options}
          renderMode="canvas"
          key={file_url}
        >
          <Page
            pageNumber={page_number}
            renderTextLayer={false}
            renderAnnotationLayer={false}
            scale={scale}
          />
        </Document>
      </div>
    </div>
  );
};

export default PDFViewer;
