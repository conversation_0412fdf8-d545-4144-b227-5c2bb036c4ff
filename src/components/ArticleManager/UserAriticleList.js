import React, { useEffect, useState } from 'react';
import { List, Card, Spin, message, Switch } from 'antd';
import { useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FilePdfOutlined,
  FileWordOutlined,
  FilePptOutlined,
  FileExcelOutlined,
  FileImageOutlined,
  FileMarkdownOutlined,
  FileZipOutlined,
  FileUnknownOutlined,
  AudioOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons';
import { fetchBulk, updateData } from '../Routers/Router';
import { ARTICLE_ENDPOINT, ARTICLE_EDIT_NAME } from '../Configs/Config';
import SidebarList from '../SidebarList';

/**
 * Updated getFileIcon function to accept a file object with 'url' and 'type'.
 * It uses 'type' if available; otherwise, it infers the type from the 'url'.
 */
const getFileIcon = (file) => {
  if (!file || !file.url) {
    // No icon is returned here; the caller should handle the absence of icons
    return null;
  }

  // Use 'type' if provided; otherwise, infer from the file extension
  const fileType = file.type
    ? file.type.toLowerCase()
    : file.url.split('.').pop().toLowerCase();

  switch (fileType) {
    case 'pdf':
      return <FilePdfOutlined style={{ fontSize: '24px', color: '#e74c3c' }} />;
    case 'doc':
    case 'docx':
      return <FileWordOutlined style={{ fontSize: '24px', color: '#2980b9' }} />;
    case 'ppt':
    case 'pptx':
      return <FilePptOutlined style={{ fontSize: '24px', color: '#d35400' }} />;
    case 'xls':
    case 'xlsx':
      return <FileExcelOutlined style={{ fontSize: '24px', color: '#27ae60' }} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return <FileImageOutlined style={{ fontSize: '24px', color: '#8e44ad' }} />;
    case 'audio':
    case 'mp3':
    case 'wav':
    case 'flac':
      return <AudioOutlined style={{ fontSize: '24px', color: '#2c3e50' }} />;
    case 'video':
    case 'mp4':
    case 'avi':
    case 'mov':
      return <VideoCameraOutlined style={{ fontSize: '24px', color: '#16a085' }} />;
    case 'zip':
    case 'rar':
      return <FileZipOutlined style={{ fontSize: '24px', color: '#f39c12' }} />;
    case 'md':
      return <FileMarkdownOutlined style={{ fontSize: '24px', color: '#34495e' }} />;
    default:
      return <FileUnknownOutlined style={{ fontSize: '24px', color: '#95a5a6' }} />;
  }
};

const UserArticleList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const sortField = 'updated_at';
  const sortOrder = 'desc';
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);
  const [total, setTotal] = useState(0);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const username = queryParams.get('username');
  const is_published = queryParams.get('is_published');
  const { user_info } = useSelector((state) => state.user);

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, is_published, username]);

  const fetchData = async () => {
    try {
      setLoading(true);

      const skip = (currentPage - 1) * pageSize;
      const limit = pageSize;

      let endpoint_api = `${ARTICLE_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&skip=${skip}&limit=${limit}&username=${user_info.username}`;

      if (username) {
        endpoint_api += `&username=${username}`;
      }

      if (is_published !== null && is_published !== undefined) { // Ensure is_published is not null or undefined
        endpoint_api += `&is_published=${is_published}`;
      }

      const result = await fetchBulk(endpoint_api);

      setData(result.data);
      setTotal(result.count);
    } catch (error) {
      console.error('数据获取失败:', error);
      message.error('数据获取失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  const handleCardClick = (record) => {
    navigate(`${ARTICLE_EDIT_NAME}/${record.id}`);
  };

  const handleIsPublishedChange = async (record, isPublished) => {
    const endpoint_api = `${ARTICLE_ENDPOINT}/${record.id}`;
    const updatedRecord = { ...record, is_published: isPublished };
    try {
      const update_data = await updateData(endpoint_api, updatedRecord);
      setData(
        data.map((item) =>
          item.id === record.id ? { ...update_data, is_published: isPublished } : item
        )
      );
      message.success('知识库状态更新成功');
    } catch (error) {
      message.error('更新知识库状态失败');
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin tip="加载中..." />
      </div>
    );
  }

  if (data.length === 0) {
    return <div style={{ textAlign: 'center', padding: '50px' }}>暂无数据</div>;
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', padding: '20px' }}>
      <h2 style={{ textAlign: 'center', marginBottom: '20px' }}>内容列表</h2>

      <div style={{ display: 'flex' }}>
        <div style={{ width: '200px', marginRight: '20px' }}>
          <SidebarList />
        </div>

        <div style={{ flex: 1, padding: '20px' }}>
          <List
            grid={{
              gutter: 16,
              xs: 1,
              sm: 2,
              md: 3,
              lg: 4,
              xl: 4,
              xxl: 4,
            }}
            dataSource={data}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: total,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: handlePageChange,
            }}
            renderItem={(item) => {
              // Check if 'url' exists and is an array
              const urlArray = Array.isArray(item.url) ? item.url : [];

              // If 'url' doesn't exist or is empty, do not display any icons
              const icons =
                urlArray.length > 0
                  ? urlArray.map((file, index) => {
                      const icon = getFileIcon(file);
                      return icon ? (
                        <span key={index} style={{ marginLeft: '5px' }}>
                          {icon}
                        </span>
                      ) : null;
                    })
                  : null; // No icons to display

              // Generate a summary from the md_article field
              const summary = item.md_article
                ? item.md_article.slice(0, 140)
                : '无摘要';

              return (
                <List.Item>
                  <Card
                    hoverable
                    style={{
                      width: '100%',
                      minHeight: '350px',
                      border: '1px solid #f0f0f0',
                      overflow: 'hidden',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      transition: 'transform 0.3s',
                    }}
                    onClick={() => handleCardClick(item)}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'scale(1.02)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'scale(1)';
                    }}
                  >
                    <Card.Meta
                      description={
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '10px',
                            height: '100%',
                          }}
                        >
                          <div style={{ flexGrow: 1, overflow: 'hidden' }}>
                            <p style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                              {summary}
                            </p>
                          </div>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                            }}
                          >
                            <span>作者: {item.username || '未知作者'}</span>
                            {icons && (
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                {icons}
                              </div>
                            )}
                          </div>
                          {is_published === null && (
                            <div style={{ marginTop: '10px' }}>
                              <Switch
                                checked={item.is_published}
                                onChange={(checked, event) => {
                                  event.stopPropagation();
                                  handleIsPublishedChange(item, checked);
                                }}
                                checkedChildren="发布"
                                unCheckedChildren="未发布"
                              />
                            </div>
                          )}
                        </div>
                      }
                    />
                  </Card>
                </List.Item>
              );
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default UserArticleList;
