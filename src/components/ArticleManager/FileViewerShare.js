import React from 'react';
import { getFileIcon } from '../Dashboard/Chat/utils/utils';
import PDFViewer from './PDFViewerShare';

const FileViewer = ({ file_url, page_id, scale }) => {  // 添加 scale 属性
  const url = new URL(file_url);
  const file_name = url.pathname.split('/').pop();
  const file_extension = file_name.split('.').pop().toLowerCase();

  const renderFileContent = () => {
    switch (file_extension) {
      case 'pdf':
        return (
          <div style={{ height: '600px' }}>
            <PDFViewer file_url={file_url} page_id={page_id} initialScale={scale} /> {/* 将 scale 传递给 PDFViewer */}
          </div>
        );
      case 'ppt':
      case 'pptx':
        return (
          <iframe
            src={`https://docs.google.com/viewer?url=${file_url}&embedded=true`}
            title="PowerPoint Viewer"
            width="100%"
            height="600"
            frameBorder="0"
          />
        );
      case 'doc':
      case 'docx':
        return (
          <iframe
            src={`https://docs.google.com/viewer?url=${file_url}&embedded=true`}
            title="Word Viewer"
            width="100%"
            height="600"
            frameBorder="0"
          />
        );
      case 'xls':
      case 'xlsx':
        return (
          <iframe
            src={`https://docs.google.com/viewer?url=${file_url}&embedded=true`}
            title="Excel Viewer"
            width="100%"
            height="600"
          />
        );
      case 'txt':
        return (
          <pre>
            {fetch(file_url)
              .then((response) => response.text())
              .then((text) => text)}
          </pre>
        );
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
        return <img src={file_url} alt="Attachment" width="100%" />;
      case 'mp3':
      case 'wav':
        return (
          <audio controls>
            <source src={file_url} type={`audio/${file_extension}`} />
            Your browser does not support the audio element.
          </audio>
        );
      case 'mp4':
      case 'webm':
        return (
          <video controls width="100%">
            <source src={file_url} type={`video/${file_extension}`} />
            Your browser does not support the video element.
          </video>
        );
      case 'ogg':
        const mimeType = url.pathname.includes('video') ? 'video/ogg' : 'audio/ogg';
        return mimeType === 'video/ogg' ? (
          <video controls width="100%">
            <source src={file_url} type="video/ogg" />
            Your browser does not support the video element.
          </video>
        ) : (
          <audio controls>
            <source src={file_url} type="audio/ogg" />
            Your browser does not support the audio element.
          </audio>
        );
      default:
        return (
          <div>
            {getFileIcon(file_url)} File type not supported: {file_extension}
          </div>
        );
    }
  };

  return (
    <div className="file-viewer">
      {renderFileContent()}
    </div>
  );
};

export default FileViewer;
