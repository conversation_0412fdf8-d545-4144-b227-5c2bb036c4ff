Container styles
.page-container {
  width: 100%;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 100vw; /* Ensure width does not exceed the viewport */
  max-height: 100vh; /* Ensure height does not exceed the viewport */
  overflow: hidden; /* Prevent overflow */
}

/* Title styles */
.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-top: -30px;
  text-align: center;
}

/* Content container with flexbox for layout */
.content-container {
  display: flex;
  gap: 20px; /* Space between form and markdown preview */
  height: calc(100vh - 60px); /* Adjust height to fit within the screen, considering padding and title */
  overflow-y: auto; /* Allow vertical scrolling if content exceeds the container */
}

/* Form container styles */
.form-container {
  flex: 1;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto; /* Scrollable if content exceeds height */
  display: flex;
  height: 86vh;
  flex-direction: column; /* Ensure the form content fills the available space */
}

/* Markdown container styles */
.markdown-container {
  flex: 1;
  background-color: #f1f1f1;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto; /* Scrollable if content exceeds height */
  white-space: pre-wrap; /* Preserve line breaks */
  font-size: 14px;
  line-height: 1.6;
}

/* Markdown image styles */
.markdown-container img {
  max-width: 30%; /* Default size is half */
  cursor: pointer;
  display: block;
  margin: 0 auto;
}

/* Form item margins */
.ant-form-item {
  margin-bottom: 16px;
}

/* Input field and text area styles */
.ant-input,
.ant-textarea {
  padding: 10px;
  border-radius: 4px;
}

/* Primary button styles */
.ant-btn-primary {
  width: 100%;
  height: 40px;
  font-size: 16px;
  background-color: #1890ff;
  border-color: #1890ff;
  border-radius: 4px;
}

/* Primary button hover effect */
.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* Responsive styles for mobile screens */
@media (max-width: 768px) {
  .page-container {
    width: 98%;
    padding: 10px;
    height: 100vh;
  }

  .content-container {
    flex-direction: column;
    height: auto;
    height: 100vh;
  }

  .form-container,
  .markdown-container {
    height: 100vh;
    /* overflow-y: hidden; */
  }
}
