import Zoom from 'react-medium-image-zoom';

export const renderers = {
  img: ({ src, alt }) => (
    <Zoom>
      <img
        src={src}
        alt={alt}
        style={{
          maxWidth: '30%', // Half the original size
          display: 'block',
          marginLeft: '0', // Align image to the left
          cursor: 'pointer',
        }}
      />
    </Zoom>
  ),
};
// utils.js


export const handleUpload = async ({ file, endpoint_api, media_type }) => {
    const token = localStorage.getItem('token'); // 从 localStorage 中获取 token
    console.log(media_type, 'media_type');
    console.log(file, 'file'); // 确保这是文件对象
  
    // 使用 FormData 构建上传请求
    const formData = new FormData();
    formData.append('file', file); // 将文件附加到表单中
    formData.append('media_type', media_type); // 附加媒体类型
  
    try {
      // 发起请求并携带 token
      const response = await fetch(endpoint_api, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`, // 添加 Authorization 头部
        },
        body: formData, // 上传文件的 formData
      });
  
      // 处理 401 Unauthorized 错误，可能是 token 失效或未授权
      if (response.status === 401) {
        localStorage.removeItem('token'); // 移除无效的 token
        throw new Error('Unauthorized, please log in again.');
      }
  
      // 处理其他非成功响应
      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.detail || `HTTP error! status: ${response.status}`;
        throw new Error(errorMessage);
      }
  
      // 返回上传结果
      const result = await response.json();
      return result;
  
    } catch (error) {
      // 捕获并处理错误
      console.error('Upload error:', error);
      throw error; // 抛出错误以便在调用时处理
    }
  };
  
