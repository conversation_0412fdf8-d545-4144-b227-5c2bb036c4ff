/* WxArticle.css */

/* Container styling */
.form-container {
    width: 100%;
    padding: 20px;
    background-color: #f9f9f9;
  }
  
  /* Header styles */
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .page-title {
    font-size: 24px;
    font-weight: bold;
  }
  
  /* Button container styles */
  .button-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .custom-button {
    background-color: #1890ff;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .custom-button:hover {
    background-color: #40a9ff;
  }
  
  /* Card container styling */
  .card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .Card {
    width: 300px;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 16px;
    transition: transform 0.3s;
  }
  
  .Card:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
  
  /* Text styling inside cards */
  .Card p {
    font-size: 14px;
    color: #595959;
    margin-bottom: 8px;
  }
  
  .Card .ant-switch {
    margin-top: 10px;
  }
  
  .Card .ant-btn-link {
    color: #ff4d4f;
  }
  
  /* Pagination styling */
  .ant-pagination {
    text-align: center;
    margin-top: 20px;
  }
  
  .ant-select {
    margin-right: 10px;
  }
  
  .ant-tooltip-inner {
    max-width: 300px;
  }
  
  /* Tooltip text truncation */
  .tooltip-text {
    display: inline-block;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  