import { createSlice } from '@reduxjs/toolkit';

const loadUsers = () => {
  try {
    const storedUsers = localStorage.getItem('users');
    return storedUsers ? JSON.parse(storedUsers) : [];
  } catch (error) {
    console.error('Failed to load users from localStorage:', error);
    return [];
  }
};

const saveUsers = (users) => {
  try {
    localStorage.setItem('users', JSON.stringify(users));
  } catch (error) {
    console.error('Failed to save users to localStorage:', error);
  }
};

const initialState = {
  users: loadUsers(),
};

export const UserSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUsers: (state, action) => {
      state.users = action.payload;
      saveUsers(state.users);
    },
    addUser: (state, action) => {
      state.users.push(action.payload);
      saveUsers(state.users);
    },
    updateUser: (state, action) => {
      const index = state.users.findIndex(user => user.id === action.payload.id);
      if (index !== -1) {
        state.users[index] = action.payload;
        saveUsers(state.users);
      }
    },
    removeUser: (state, action) => {
      state.users = state.users.filter(user => user.id !== action.payload);
      saveUsers(state.users);
    },
    clearUsers: (state) => {
      state.users = [];
      saveUsers(state.users);
    },
  },
});

export const {
  setUsers,
  addUser,
  updateUser,
  removeUser,
  clearUsers,
} = UserSlice.actions;

export default UserSlice.reducer;
