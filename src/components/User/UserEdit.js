import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Form, Input, Button, message, Modal, notification, Spin, Select, Switch } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { fetchData, updateData } from '../Routers/Router';
import { USER_ENDPOINT, USERS_NAME } from '../Configs/Config';
import { updateUser as updateUserInStore } from './UserSlice';
import './User.css'; // Import the CSS file
import Logout from '../Login/Logout'; // Import the Logout component

const { Option } = Select;

const EditUser = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    const getUser = async () => {
      try {
        const endpoint_api = `${USER_ENDPOINT}/${id}`;
        const data = await fetchData(endpoint_api);
        form.setFieldsValue(data);
      } catch (error) {
        message.error('Failed to fetch user details');
      } finally {
        setInitialLoading(false);
      }
    };

    getUser();
  }, [id, form]);

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${USER_ENDPOINT}/${id}`;
          const response = await updateData(endpoint_api, values);
          if (response) {
            dispatch(updateUserInStore({ id, ...values }));
            message.success('User updated successfully');
            navigate(`${USERS_NAME}`);
          }
        } catch (error) {
          notification.error({
            message: '更新失败',
            description: 'Failed to update user. Please try again.',
            onClose: () => console.log('Notification closed'),
            duration: 5, // Set duration to 0 to keep it open until manually closed
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  if (initialLoading) {
    return <Spin tip="Loading..." />;
  }

  return (
    <div className="page-container">
      <div className="header-container">
        <h1 className="page-title">编辑用户</h1>
        <Logout /> {/* Add the Logout component */}
      </div>
      <div className="form-container">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={(errorInfo) => {
            console.log('Failed:', errorInfo);
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="email"
            label="电子邮件"
            rules={[{ required: true, message: '请输入电子邮件!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色!' }]}
          >
            <Select>
              <Option value="admin">超级管理员</Option>
              <Option value="enterprise">企业</Option>
              <Option value="user">普通用户</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="激活" unCheckedChildren="未激活" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存更改
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default EditUser;
