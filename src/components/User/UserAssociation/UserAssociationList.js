import React, { useEffect, useState } from 'react';
import { Table, Button, message,Switch } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { USER_ASSOCIATION_NEW_NAME, USER_ASSOCIATION_ENDPOINT } from '../../Configs/Config';
import { fetchBulk, deleteData, deleteBulk,updateData } from '../../Routers/Router';
import SidebarList from '../../SidebarList';
import styles from './UserAssociationList.module.css'; // Updated import for CSS module

const UserAssociationList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [sortField, setSortField] = useState('custom');
  const [sortOrder, setSortOrder] = useState('asc');
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);

  useEffect(() => {
    getData();
  }, [sortField, sortOrder]);

  const getData = async () => {
    try {
      const endpoint_api = `${USER_ASSOCIATION_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&username=${user_info.username}`;
      const result = await fetchBulk(endpoint_api);
      setData(result.data);
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch user association data');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    const endpoint_api = `${USER_ASSOCIATION_ENDPOINT}/${id}`;
    try {
      await deleteData(endpoint_api);
      setData(data.filter((item) => item.id !== id));
      message.success('用户关联删除成功');
    } catch (error) {
      message.error('删除用户关联失败');
    }
  };

  const handleBulkDelete = async () => {
    try {
      const endpoint_api = `${USER_ASSOCIATION_ENDPOINT}/bulk`;
      await deleteBulk(endpoint_api, { user_association_ids: selectedRowKeys });
      setData(data.filter((item) => !selectedRowKeys.includes(item.id)));
      setSelectedRowKeys([]);
      message.success('选中的用户关联已删除');
    } catch (error) {
      message.error('删除选中的用户关联失败');
    }
  };
  
  const handleIsActiveChange = async (record, isActive) => {
    const endpoint_api = `${USER_ASSOCIATION_ENDPOINT}/${record.id}`;
    const updatedRecord = { ...record, is_active: isActive };
    try {
      const update_data = await updateData(endpoint_api, updatedRecord);
      setData(data.map(item => 
        item.id === record.id ? { ...update_data, is_active: isActive } : item
      ));
      message.success('用户关系激活成功');
    } catch (error) {
      console.error("只有超级管理员或企业用户可以创建用户关联")
    }
  };
  const handleTableChange = (pagination, filters, sorter) => {
    setSortField(sorter.field || 'custom');
    setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
  };

  const columns = [
    {
      title: '索引',
      dataIndex: 'index',
      key: 'index',
      render: (text, record, index) => index + 1,
    },
    {
      title: '用户',
      dataIndex: 'custom',
      key: 'custom',
      sorter: true,
    },
    {
      title: '管理者',
      dataIndex: 'username',
      key: 'username',
      sorter: true,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      sorter: true,
      render: (text, record) => (
        <Switch
          checked={record.is_active}
          onChange={(checked) => handleIsActiveChange(record, checked)}
        />
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => (
        <Button 
          type="link" 
          onClick={() => handleDelete(record.id)} 
          disabled={user_info.role !== 'admin' && user_info.role !== 'enterprise'}
        >
          删除
        </Button>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys),
    getCheckboxProps: (record) => ({
      className: selectedRowKeys.includes(record.id) ? 'selected-row' : '',
    }),
  };

  return (
    <div className={styles.formContainer}>
      <div className={styles.headerContainer}>
        <h1 className={styles.pageTitle}>用户关联配置</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        <button
          className="custom-button"
          onClick={() => navigate(`${USER_ASSOCIATION_NEW_NAME}`)}
          disabled={user_info.role !== 'admin' && user_info.role !== 'enterprise'}
        >
          新增
        </button>
        <button
          className="custom-button delete-button"
          onClick={handleBulkDelete}
          disabled={selectedRowKeys.length === 0 || (user_info.role !== 'admin' && user_info.role !== 'enterprise')}
        >
          删除
        </button>
      </div>
      <Table
        className={styles.tableWrapper}
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        rowClassName={(record) => (selectedRowKeys.includes(record.id) ? 'selected-row' : 'hover-row')}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default UserAssociationList;