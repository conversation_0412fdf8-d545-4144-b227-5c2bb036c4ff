import React, { useState, useEffect } from 'react';
import { Form, Button, message, Modal, notification, Switch, Select, Input } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { debounce } from 'throttle-debounce';
import styles from './UserAssociationCreate.module.css'; // Updated import for CSS module
import { USER_ASSOCIATIONS_NAME, USER_ASSOCIATION_ENDPOINT, USER_ENDPOINT } from '../../Configs/Config';
import { createData, fetchBulk } from '../../Routers/Router';
import Logout from '../../Login/Logout';

const { Option } = Select;

const UserAssociationCreate = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);
  const [isActive, setIsActive] = useState(true);
  const [customUsers, setCustomUsers] = useState([]);
  const [adminUsers, setAdminUsers] = useState([]);
  
  const fetchUsersWithRole = async (keyword, roles) => {
    try {
      const rolesFilter = roles.join(',');
      const endpoint_api = `${USER_ENDPOINT}/bulk?username__contains=${keyword}&role__in=${rolesFilter}`;
      // const params = {
      //   keyword,
      //   roles: roles.join(','),
      //   limit: 10
      // };
      // console.log(params,'params');
      // console.log(endpoint_api);
      const response = await fetchBulk(endpoint_api);
      console.log(response);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch users:', error);
      return [];
    }
  };

  const debouncedFetchAndSetUsers = debounce(300, async (value, roles, setUsers) => {
    const users = await fetchUsersWithRole(value, roles);
    setUsers(users);
  });

  const handleCustomSearch = (value) => {
    console.log(value, "roles")
    if (value) {
      debouncedFetchAndSetUsers(value, ['user', 'enterprise'], setCustomUsers);
    } else {
      setCustomUsers([]);
    }
  };

  const handleAdminSearch = (value) => {
    if (value) {
      debouncedFetchAndSetUsers(value, ['enterprise'], setAdminUsers);
    } else {
      setAdminUsers([]);
    }
  };

  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${USER_ASSOCIATION_ENDPOINT}/`; 
          await createData(endpoint_api, values);
          message.success('用户关联创建成功');
          
          form.resetFields();
          navigate(`${USER_ASSOCIATIONS_NAME}`);
        } catch (error) {
          console.error('Error:', error);
          notification.error({
            message: '创建失败',
            description: '创建用户关联失败，请重试。',
            onClose: () => console.log('Notification closed'),
            duration: 5,
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  useEffect(() => {
    if (user_info.role === 'enterprise') {
      form.setFieldsValue({ username: user_info.username });
    }
  }, [user_info, form]);

  return (
    <div className={styles.pageContainer}>
      <div className={styles.headerContainer}>
        <h1 className={styles.pageTitle}>创建用户关联</h1>
        <Logout />
      </div>
      <div className={styles.formContainer}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={(errorInfo) => {
            console.log('Failed:', errorInfo);
          }}
        >
          {user_info.role === 'admin' ? (
            <>
              <Form.Item
                name="custom"
                label="用户"
                rules={[{ required: true, message: '请选择用户!' }]}
              >
                <Select
                  showSearch
                  placeholder="搜索用户"
                  onSearch={handleCustomSearch}
                  filterOption={false}
                >
                  {customUsers.map(user => (
                    <Option key={user.id} value={user.username}>{user.username} ({user.role})</Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                name="username"
                label="管理者"
                rules={[{ required: true, message: '请选择管理者!' }]}
              >
                <Select
                  showSearch
                  placeholder="搜索管理者"
                  onSearch={handleAdminSearch}
                  filterOption={false}
                >
                  {adminUsers.map(user => (
                    <Option key={user.id} value={user.username}>{user.username} ({user.role})</Option>
                  ))}
                </Select>
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item
                name="custom"
                label="用户"
                rules={[{ required: true, message: '请输入用户名!' }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                name="username"
                label="管理者"
                rules={[{ required: true, message: '请输入管理者用户名!' }]}
              >
                <Input disabled={user_info.role === 'enterprise'} />
              </Form.Item>
            </>
          )}
          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
            initialValue={isActive}
          >
            <Switch checked={isActive} onChange={(checked) => setIsActive(checked)} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建用户关联
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default UserAssociationCreate;
