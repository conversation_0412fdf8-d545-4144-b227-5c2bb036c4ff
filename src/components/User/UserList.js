import React, { useEffect, useState } from 'react';
import { Table, Button, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { USER_EDIT_NAME, USER_NEW_NAME, USER_ENDPOINT } from '../Configs/Config';
import { fetchBulk, deleteData, deleteBulk } from '../Routers/Router';
import SidebarList from '../SidebarList';
import './User.css';

const UserList = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [sortField, setSortField] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const navigate = useNavigate();
  const { user_info } = useSelector((state) => state.user);

  useEffect(() => {
    getData();
  }, [sortField, sortOrder]);

  const getData = async () => {
    try {
      const endpoint_api = `${USER_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&username=${user_info.username}`;
      const result = await fetchBulk(endpoint_api);
      setData(result.data);
    } catch (error) {
      console.error(error);
      message.error('Failed to fetch user data');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    const endpoint_api = `${USER_ENDPOINT}/${id}`;
    try {
      await deleteData(endpoint_api);
      setData(data.filter((item) => item.id !== id));
      message.success('用户删除成功');
    } catch (error) {
      message.error('删除用户失败');
    }
  };

  const handleBulkDelete = async () => {
    try {
      const endpoint_api = `${USER_ENDPOINT}/bulk`;
      await deleteBulk(endpoint_api, { user_ids: selectedRowKeys });
      setData(data.filter((item) => !selectedRowKeys.includes(item.id)));
      setSelectedRowKeys([]);
      message.success('选中的用户已删除');
    } catch (error) {
      message.error('删除选中的用户失败');
    }
  };

  const handleTableChange = (pagination, filters, sorter) => {
    setSortField(sorter.field || 'updated_at');
    setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
  };
  const renderRole = (role) => {
    switch (role) {
      case 'admin':
        return '超级管理员';
      case 'enterprise':
        return '企业';
      case 'user':
        return '普通用户';
      default:
        return role;
    }
  };

  const columns = [
    {
      title: '索引',
      dataIndex: 'index',
      key: 'index',
      render: (text, record, index) => index + 1,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      sorter: true,
      render: (text, record) => (
        <button
          onClick={() => navigate(`${USER_EDIT_NAME}/${record.id}`)}
          className={`link-button ${user_info.role !== 'admin' ? 'disabled' : ''}`}
          disabled={user_info.role !== 'admin'}
        >
          {text}
        </button>
      ),
    },
    {
      title: '电子邮件',
      dataIndex: 'email',
      key: 'email',
      sorter: true,
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      sorter: true,
      render: renderRole,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      sorter: true,
      render: (text) => (text ? '激活' : '未激活'),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (text, record) => (
        <Button type="link" onClick={() => handleDelete(record.id)} disabled={user_info.role !== 'admin'}>
          删除
        </Button>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys),
    getCheckboxProps: (record) => ({
      className: selectedRowKeys.includes(record.id) ? 'selected-row' : '',
    }),
  };

  return (
    <div className="form-container">
      <div className="header-container">
        <h1 className="page-title">用户列表</h1>
        <SidebarList />
      </div>
      <div className="button-container">
        <button 
          className="custom-button" 
          onClick={() => navigate(`${USER_NEW_NAME}`)}
          disabled={user_info.role === 'user'}
        >
          新增
        </button>
        <button
          className="custom-button"
          style={{ marginLeft: 8 }}
          onClick={handleBulkDelete}
          disabled={selectedRowKeys.length === 0 || user_info.role !== 'admin'}
        >
          删除
        </button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        rowClassName={(record) => (selectedRowKeys.includes(record.id) ? 'selected-row' : '')}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default UserList;