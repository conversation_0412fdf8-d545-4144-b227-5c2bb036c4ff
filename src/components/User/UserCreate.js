import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Form, Input, Button, message, Modal, notification,Select,Switch } from 'antd';
import { addUser } from './UserSlice';
import { useNavigate } from 'react-router-dom';
import './User.css'; // Import the CSS file
import { USERS_NAME, USER_ENDPOINT } from '../Configs/Config';
import { createData } from '../Routers/Router';
import Logout from '../Login/Logout'; // Import the Logout component

const { Option } = Select;

const CreateUser = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
    
  const onFinish = (values) => {
    Modal.confirm({
      title: '确认保存',
      content: '您确定要保存这些更改吗？',
      onOk: async () => {
        setLoading(true);
        try {
          const endpoint_api = `${USER_ENDPOINT}/`; 
          const response = await createData(endpoint_api, values);
          const newUser = {
            id: response.id, // Assuming the API returns an id for the new User
            ...values
          };
          dispatch(addUser(newUser));
          message.success('User created successfully');
          // const association_endpoint_api = `${USER_ASSOCIATION_ENDPOINT}/`; 
          // const association_values = {
          //   custom:newUser.username
          // }
          // await createData(association_endpoint_api, association_values);
          // message.success(`${newUser.username}关联成功！`);
          form.resetFields();
          navigate(`${USERS_NAME}`);
        } catch (error) {
          notification.error({
            message: '创建失败',
            description: 'Failed to create User. Please try again.',
            onClose: () => console.log('Notification closed'),
            duration: 5, // Set duration to 0 to keep it open until manually closed
          });
        } finally {
          setLoading(false);
        }
      },
    });
  };

  return (
    <div className="page-container">
      <div className="header-container">
        <h1 className="page-title">创建新用户</h1>
        <Logout /> {/* Add the Logout component */}
      </div>
      <div className="form-container">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onFinishFailed={(errorInfo) => {
            console.log('Failed:', errorInfo);
          }}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            name="email"
            label="电子邮件"
            rules={[{ required: true, message: '请输入电子邮件!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色!' }]}
          >
            <Select>
              <Option value="admin">超级管理员</Option>
              <Option value="enterprise">企业</Option>
              <Option value="user">普通用户</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="激活" unCheckedChildren="未激活" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建用户
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default CreateUser;