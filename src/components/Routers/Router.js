import { message } from 'antd';  // 确保你已经导入了 Ant Design 的 message 模块
import { FILE_MANAGER_ENDPOINT,AGENT_KNOWLEDGE_BASE_ENDPOINT } from '../Configs/Config';

export const fetchBulk = async (endpoint_api, params = {}) => {
  const token = localStorage.getItem('token');
  try {
    const url = new URL(endpoint_api);
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.status === 401) {
      localStorage.removeItem('token');
      throw new Error('Unauthorized');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    message.error(error.message || '批量获取数据失败');  // 使用 Ant Design 弹窗显示错误信息
    throw error;  // 直接抛出捕获的错误
  }
};
  export const fetchData = async (endpoint_api) => {
    const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
    try {
      const response = await fetch(endpoint_api, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
      });
        //   console.log(token,'tokentoken')
      if (response.status === 401) {
        // 如果状态码是401，清除token并处理未授权情况
        localStorage.removeItem('token');
        // 可以选择重定向用户到登录页面
        // window.location.href = '/login'; // 假设你的登录页面路径是 /login
        throw new Error('Unauthorized');
        }
        if (response.status !== 200) {
          return null
        }
      return await response.json();
    } catch (error) {
      message.error(error.message || '获取数据失败');  // 使用 Ant Design 弹窗显示错误信息
      throw error;  // 直接抛出捕获的错误
    }
  };

export const deleteData = async (endpoint_api) => {
    const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
    try {
      const response = await fetch(endpoint_api, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
      });
      if (response.status === 401) {
        // 如果状态码是401，清除token并处理未授权情况
        localStorage.removeItem('token');
        throw new Error('Unauthorized');
        }
      return await response.json();
    } catch (error) {
      message.error(error.message || '删除数据失败');  // 使用 Ant Design 弹窗显示错误信息
      throw error;  // 直接抛出捕获的错误
    }
  };
  
export  const deleteBulk = async (endpoint_api, kb_data) => {
    // const url = `${KNOWLEDGE_BASE_ENDPOINT}/bulk`;  // 使用从配置文件导入的基础 URL
    const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
    try {
      const response = await fetch(endpoint_api, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
        body: JSON.stringify(kb_data),
        // body: body,
      });
      if (response.status === 401) {
        // 如果状态码是401，清除token并处理未授权情况
        localStorage.removeItem('token');
        throw new Error('Unauthorized');
        }
      return await response.json();
    } catch (error) {
      message.error(error.message || '批量删除数据失败');  // 使用 Ant Design 弹窗显示错误信息
      throw error;  // 直接抛出捕获的错误
    }
  };
  
export const createData = async (endpoint_api, data) => {
    // const url = `${KNOWLEDGE_BASE_ENDPOINT}/`;  // 使用从配置文件导入的基础 URL
    const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
    try {
      const response = await fetch(endpoint_api, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
        body: JSON.stringify(data),
      });
      if (!response.ok) {

        const errorData = await response.json();
        const errorMessage = errorData.detail || `HTTP error! status: ${response.status}`;
        message.error(errorMessage);  // 使用 Ant Design 弹窗显示错误信息
        if (response.status === 401) {
          // 如果状态码是401，清除token并处理未授权情况
          localStorage.removeItem('token');
        }
        throw new Error(errorMessage);
      }
      return await response.json();
    } catch (error) {
      // console.error('Failed to create data:', error.message || error);
      message.error(error.message || '新增数据失败');  // 使用 Ant Design 弹窗显示错误信息
      throw error;  // 直接抛出捕获的错误
    }
  };
  
export const updateData = async (endpoint_api, kb_data) => {
    const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
    try {
      const response = await fetch(endpoint_api, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        },
        body: JSON.stringify(kb_data),
      });
      if (response.status === 401) {
        // 如果状态码是401，清除token并处理未授权情况
        localStorage.removeItem('token');
        throw new Error('Unauthorized');
        }
      if (!response.ok) {

        const errorData = await response.json();
        const errorMessage = errorData.detail || `HTTP error! status: ${response.status}`;
        throw new Error(errorMessage);
      }
      return await response.json();
    } catch (error) {
      message.error(error.message || '更新数据失败');  // 使用 Ant Design 弹窗显示错误信息
      throw error;  // 直接抛出捕获的错误
    }
  };
  
  export const uploadFileFunction = async (file,kb_id,is_background = true) => {
    const token = localStorage.getItem('token');
    const endpoint_api = `${FILE_MANAGER_ENDPOINT}/upload_file/?kb_id=${kb_id}`; // Adjust this to match your API endpoint
  
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('agent_endpoint', AGENT_KNOWLEDGE_BASE_ENDPOINT); 
      formData.append('is_background', is_background); 
  
      const response = await fetch(endpoint_api, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (response.status === 401) {
        localStorage.removeItem('token');
        throw new Error('Unauthorized');
      }
  
      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.detail || `HTTP error! status: ${response.status}`;
        throw new Error(errorMessage);
      }
  
      const result = await response.json();
      // console.log(result,'uploadFileFunction')
      return result;
    } catch (error) {
      message.error(error.message || '上传文件失败');  // 使用 Ant Design 弹窗显示错误信息
      throw error;  // 直接抛出捕获的错误
    }
  };

  export function processWorkflows(workflows) {
    return workflows.map(workflow => {
        return {
            ...workflow,
            nodes: JSON.parse(workflow.nodes),
            edges: JSON.parse(workflow.edges),
            workflow_name: workflow.name,
            // database: workflow?.database.split(",")
        };
    });
}
 
export const createKBData = async ({endpoint_api, kb_data_file_infos} ) => {
  const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
  try {
    const response = await fetch(endpoint_api, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
      },
      body: JSON.stringify(kb_data_file_infos),
    });
    const response_data = await response.json()
    if (!response.ok) {
      const errorData = await response.json();
      const errorMessage = errorData.detail || `HTTP error! status: ${response.status}`;
      message.error(errorMessage);  // 使用 Ant Design 弹窗显示错误信息
      if (response.status === 401) {
        // 如果状态码是401，清除token并处理未授权情况
        localStorage.removeItem('token');
      }
      throw new Error(errorMessage);
    }
    return response_data;
  } catch (error) {
    // console.error('Failed to create data:', error.message || error);
    message.error(error.message || '知识库数据新增失败');  // 使用 Ant Design 弹窗显示错误信息
    throw error;  // 直接抛出捕获的错误
  }
};


/**
 * 上传文件到服务器
 * @param {File[]} files - 要上传的文件数组
 * @returns {Promise<Object>} - 返回服务器响应的数据
 * @throws {Error} - 如果上传失败，则抛出错误
 */
// src/Routers/Router.js 或相应的文件路径

export const uploadFiles = async (file, onProgress) => {
  const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
  const formData = new FormData();
  formData.append('file', file); // 使用 'file' 作为字段名

  try {
    const response = await fetch(`${FILE_MANAGER_ENDPOINT}/upload_file_chat/`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
        // 不要设置 'Content-Type'，让浏览器自动处理
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(errorText || '文件上传失败');
    }

    const data = await response.json();
    return data; // 返回服务器响应的数据
  } catch (error) {
    console.error('上传错误:', error);
    throw error; // 将错误抛出，以便调用方处理
  }
};


export const onlyUploadFile = async (file, endpoint_api) => {
  const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await fetch(endpoint_api, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.ok) {
      const result = await response.json();
      return result.url; // 返回文件上传后的 URL
    } else {
      const errorText = await response.text();
      console.error('文件上传失败:', errorText);
      throw new Error('文件上传失败');
    }
  } catch (error) {
    console.error('上传文件时出错:', error);
    throw error; // 抛出错误供调用方处理
  }
};

export const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
      return new Date(dateString).toLocaleString(); // Basic formatting
  } catch (e) {
      return dateString; // Return original if parsing fails
  }
};

// Add these functions to your existing Router.js file

/**
 * Checks if a Python package exists and meets version requirements.
 * @param {string} packageName - The name of the package.
 * @param {string} [requiredVersion] - Optional required version specifier (e.g., ">=0.1.0").
 * @returns {Promise<object>} - Promise resolving to { exists: bool, current_version: str|null, version_ok: bool|null }
 */
export const checkPackage = async (endpoint_api,packageName, requiredVersion = null) => {
  const token = localStorage.getItem('token');
  // Assuming your check endpoint is relative to a base URL managed elsewhere or hardcoded
  // const endpoint_api = `/api/v1/packages/check`; // Adjust this path as needed

  try {
    const response = await fetch(endpoint_api, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        package_name: packageName,
        required_version: requiredVersion,
      }),
    });

    if (response.status === 401) {
      localStorage.removeItem('token');
      throw new Error('Unauthorized');
    }
    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error! status: ${response.status}` }));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    return await response.json(); // Returns { exists, current_version, version_ok }
  } catch (error) {
    message.error(`检查包 '${packageName}' 失败: ${error.message}`);
    throw error;
  }
};

/**
 * Installs a Python package.
 * @param {string} packageName - The name of the package to install.
 * @param {string} [version] - Optional specific version to install (e.g., "1.0.1").
 * @returns {Promise<object>} - Promise resolving to { success: bool, message: str, package_spec: str }
 */
export const installPackage = async (endpoint_api,packageName, version = null) => {
  const token = localStorage.getItem('token');
  // Assuming your install endpoint is relative to a base URL managed elsewhere or hardcoded
  // const endpoint_api = `/api/v1/packages/install`; // Adjust this path as needed

  try {
    const response = await fetch(endpoint_api, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        package_name: packageName,
        version: version, // Send specific version if provided
      }),
    });

     if (response.status === 401) {
      localStorage.removeItem('token');
      throw new Error('Unauthorized');
    }
    if (!response.ok) {
        // Installation might return non-200 on failure, but still valid JSON response
        const errorData = await response.json().catch(() => ({ message: `HTTP error! status: ${response.status}` }));
         // Let's return the data even on non-OK for installation, as it contains success: false
        if (response.status >= 400) { // Treat client/server errors as throw cases if no specific handling
             throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }
        return errorData; // Return the { success: false, ... } payload
    }

    return await response.json(); // Returns { success, message, package_spec }
  } catch (error) {
    message.error(`安装包 '${packageName}' 失败: ${error.message}`);
    throw error;
  }
};
