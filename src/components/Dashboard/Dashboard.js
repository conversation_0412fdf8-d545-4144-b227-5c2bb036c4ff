import React, { useEffect, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import './dashboard.css';
import Sidebar from './Sidebar/Sidebar';
import Chat from './Chat/Chat';
import ListAttachment from './Chat/ListAttachment/ListAttachment';
import LoadingSpinner from './loadingSpinner';
import { fetchSession } from './Chat/utils/apiUtil';
import { setConversations } from './dashboardSlice';
import { setWorkflows } from '../Workflow/workflowSlice';
import { fetchBulk, processWorkflows } from '../Routers/Router';
import { WORKFLOW_ENDPOINT } from '../Configs/Config';
import WorkflowSwitcher from '../Workflow/Agents/workflows/WorkflowSwitcher';
import ArticleList from '../FileManager/ArticleList';

const Dashboard = () => {
    const dispatch = useDispatch();
    const { user_info } = useSelector((state) => state.user);
    const { workflows } = useSelector((state) => state.workflow);
    const { conversations } = useSelector((state) => state.dashboard);
    const [loading, setLoading] = useState(true);

    // Refs to prevent multiple API calls
    const conversationsFetched = useRef(false);
    const workflowsFetched = useRef(false);

    useEffect(() => {
        const getConversations = async () => {
            if (!conversationsFetched.current && (!conversations || conversations.length === 0)) {
                console.log("Fetching conversations...");
                try {
                    const conversationsData = await fetchSession(user_info.username);
                    dispatch(setConversations(conversationsData));
                } catch (error) {
                    console.error("Error fetching conversations:", error);
                }
                conversationsFetched.current = true;
            }
        };

        const getWorkflows = async () => {
            if (!workflowsFetched.current && (!workflows || workflows.length === 0)) {
                console.log("Fetching workflows...");
                try {
                    const endpoint_api = `${WORKFLOW_ENDPOINT}/bulk`;
                    const result = await fetchBulk(endpoint_api);
                    const workflowsData = processWorkflows(result.data);
                    dispatch(setWorkflows(workflowsData));
                } catch (error) {
                    console.error("Error fetching workflows:", error);
                }
                workflowsFetched.current = true;
            }
        };

        const initializeDashboard = async () => {
            await Promise.all([getConversations(), getWorkflows()]);
            setLoading(false);
        };

        initializeDashboard();
    }, [dispatch, user_info.username]); // Dependencies include dispatch and username

    if (loading) {
        return <LoadingSpinner />;
    }

    return (
        <div className="dashboard_container">
            <Sidebar />
            <Chat />
            <ListAttachment />
            <WorkflowSwitcher />
            <ArticleList />
        </div>
    );
};

export default Dashboard;
