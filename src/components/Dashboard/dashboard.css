.dashboard_container {
    width: 100%;
    height: 100vh;
    display: flex;
}

.attachment_container {
  margin: auto;
  height: 100%;
  width: 400px;
  overflow-y: auto;
  padding: 0 10px; /* 添加左右内边距 */
}

.chat_selected_container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #2b2b2b; /* 浅黑色背景 */
}

.chat_gpt_logo_container {
    flex-grow: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat_gpt_logo {
    color: rgb(217,217,227);
    font-weight: bold;
    font-size: 50px;
}

.new_chat_button {
    margin: 5px;
    height: 46px;
    border: 1px solid grey;
    border-radius: 8px;
    transition: 0.4s;
    color: white;
    display: flex;
    align-items: center;
}

.new_chat_button:hover {
    opacity: 0.5;
}

.list_item {
    margin: 5px;
    height: 46px;
    transition: 0.4s;
    display: flex;
    align-items: center;
    position: relative; /* 设置为relative，为子绝对定位元素提供参照 */
}

.list_item:hover {
    opacity: 1;
}

.list_item_options {
    margin-left: auto; /* 这会将选项推向最右侧 */
    display: flex;
    align-items: center;
    padding: 0 10px;
}

.new_chat_button_text
{
  margin-left: 10px;
  padding: 0;
  font-size: 14px;
  color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
 .list_item_text {
    margin-left: 10px;
    padding: 0;
    font-size: 14px;
    color: white;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.list_item_icon {
  margin-top: 4px;
  margin-left: 1px;
}
.new_chat_button_icon{
  margin-top: 4px;
  margin-left: 10px;
}

.new_message_input_inner {
  /* width: 650px; */
  width: 90%;
  margin-left: 20px;
  position: relative;
  display: flex;
  align-items: center;
  height: 40px; /* 确保设置高度，以便图标垂直居中 */
}


.message {
    width: 640px;
    display: flex;
}

.file_display {
  display: flex;
  align-items: center;
  margin-bottom: 5px; /* Space between file displays */
  border-radius: 16px;
  overflow: hidden; /* Hide overflow */
}
.file_icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px; /* Set a fixed width for icon area */
  border-radius: 16px 0 0 16px; /* Rounded corners for the icon */
}

.file_icon-svg {
  color: white; /* White color for the icon */
  width: 20px; /* Set a fixed size for svg icons */
  height: 20px;
}

.file_info {
  display: flex;
  flex-direction: column;
  padding: 4px 8px;
  overflow: hidden;
}

.file_name {
  font-size: 14px;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; /* Ellipsis for overflowed text */
}
.overlay {
    background: #ffffff;
    color: #666666;
    position: fixed;
    height: 100%;
    width: 100%;
    z-index: 5000;
    top: 0;
    left: 0;
    float: left;
    text-align: center;
    padding-top: 25%;
    opacity: .80;
  }
  .spinner {
      margin: 0 auto;
      height: 64px;
      width: 64px;
      animation: rotate 0.8s infinite linear;
      border: 5px solid firebrick;
      border-right-color: transparent;
      border-radius: 50%;
  }
  @keyframes rotate {
      0% {
          transform: rotate(0deg);
      }
      100% {
          transform: rotate(360deg);
      }
  }

  .circle-node {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    color: white;
  }
  

  .workflow {
    position: relative;
    width: 100%;
    height: 99vh;
    z-index: 999;
    /* background-color: rgba(0, 0, 0, 0.5); */
    transition: height 0.3s ease-in-out;
  }
  

  .save-button {
    position: fixed;
    bottom: 50px;
    right: 20px;
    background-color: #4CAF50;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .save-button svg {
    margin-right: 5px;
  }
  
  .workflow-header {
    display: flex;
    height: 8px;
    justify-content: space-between;
    align-items: center;
    background-color: #f0f0f0;
    padding: 10px;
    cursor: pointer;
  }

  
  .react-flow__edge-path {
    stroke: black !important;
    stroke-width: 2px !important;
  }

  .reference{
    color: black;
    padding-top: 30px;
    padding-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
  }
  .workflow_name{
    color: black;
    padding-left: 1px;
    font-size: 14px;
    font-weight: bold;
  }

  .workflow_set{
    color: blue;
    font-size: 10px;
  }

  .workflow-settings-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px;
    z-index: 100;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  }
  
  /* 当弹框显示时可能需要的遮罩层 */
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
  }
  

  .workflow-sidebar {
    width: 200px;
    height: 100vh;
    overflow-y: auto;
    background-color: #f0f0f0;
    padding: 10px;
  }
  
  .workflow-set-container {
    display: flex;
  }
  
  .node {
    margin: 10px 0;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    cursor: grab;
  }
  
.workflow-expanded-name{
    color: black;
    padding-left: 1px;
    font-size: 10.5px;
}

.reactflow-set-container {
    width: 100%;
    height: 800px; /* 或者 '100vh' 以填满视口高度 */
    position: relative;
  }

  .uploaded-files-info {
    
    display: flex;
    flex-wrap: wrap; /* 允许多个文件环绕到下一行 */
    align-items: center; /* 垂直居中 */
    justify-content: flex-start; /* 从行首开始排列 */
    margin-bottom: 30px;
    height: 30px; /* 固定高度 */
}

.uploaded-file-info {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f1f1;
  /* border-radius: 8px; */
  margin-right: 10px; /* 每个文件信息块右侧间隔 */
  width: 140px; /* 固定宽度 */
  height: 35px; /* 固定高度 */
  bottom: 5px;
}

.video-file {
  width: 100%; /* Full width of the container */
  height: auto; /* Maintain aspect ratio */
  max-height: 500px; /* Maximum height */
}

.audio-file {
  width: 100%; /* Full width of the container */
}

.file-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px; /* 图标和文件名之间的间隔 */
}

.file-name {
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 防止文件名换行 */
}

.file-name-container {
  position: relative;
  display: inline-block;
}
.file-name-tooltip {
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1;
  pointer-events: none;
}

.file-name-container:hover .file-name-tooltip {
  opacity: 1;
  visibility: visible;
}


.new_message_icon_container:hover {
  background-color: #0056b3;
}

.image-file {
  max-width: 100px; /* Set a max-width for inline images */
  border-radius: 4px; /* Optional, for rounded corners */
  cursor: pointer; /* Indicates the image is clickable */
}

.selected-doc-ids-warning {
  position: absolute;
  top: -10%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff7f7;
  padding: 5px 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  z-index: 1;
}

.warning-icon {
  color: #ff0000;
  margin-right: 5px;
}

.warning-text {
  font-size: 12px;
  color: #ff0000;
  margin-right: 1px;
}
.close-icon {
  color: #666;
  font-size: 1px;
  cursor: pointer;
}

.message_content_copy {
  position: absolute;
  right: 5px; /* 右对齐，留出一些边距 */
  bottom: 0px; /* 底部对齐，留出一些边距 */
  color: white;
  z-index: 10; /* 确保图标位于内容之上 */
}

/* 图标悬停样式提升用户体验 */
.message_content_copy:hover {
  color: #f1e1e1; /* 悬停时颜色变深 */
  transform: scale(1.1); /* 稍微放大图标 */
  cursor: pointer;
}
.spinning {
  animation: spin 1s infinite linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.new_message_input_container {
  width: 100%;
  height: 150px;
  position: relative; /* Ensures positioning context for absolute children */
  display: flex;
  align-items: center; /* Centers items vertically */
  justify-content: space-between; /* Utilizes space between input and logo container */
}

.chat_messages_container {
  width: 100%;
  flex-grow: 1;
  max-height: 90%;
  overflow-y: auto; /* Enables scrolling for overflow content */
}