import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    session_established: false,
    conversations: [],
    selected_conversation_id: 'new',
    selected_message_index: null, // 新增选中 message 的索引
    selected_docs:[],
    selected_data_ids:[],
    selected_conversation:{},
    isChat:false
};

const dashboardSlice = createSlice({
    name: 'dashboard',
    initialState,
    reducers:{
        setSelectedConversationId: (state, action) => {
            state.selected_conversation_id = action.payload;
        },
        setSelectedConversation:(state,action) =>{
          state.selected_conversation = action.payload;
        },

        addMessage: (state, action) => {
            const { message, conversation_id } = action.payload;
            const conversation = state.selected_conversation;

            if (conversation && conversation.id === conversation_id) {
                // 在消息数组中找到最后一条 AI 消息
              const last_ai_message_index = conversation.messages.findLastIndex((m) => m.id === message.id);
              if (last_ai_message_index === conversation.messages.length -1) {
                let updated_message = { 
                  ...conversation.messages[last_ai_message_index], 
                  content: conversation.messages[last_ai_message_index].content + message.content, 
                  attachment: message.attachment,
                  file_urls: message.file_urls,
                  is_function_call:message.is_function_call,
                  function_call: message.is_function_call ? message.function_call : conversation.messages[last_ai_message_index].function_call,
                  token:message.token,
                  is_reflection:message.is_reflection,
              };
              conversation.messages[last_ai_message_index] = updated_message;

              } else {
                // 如果还没有 AI 消息,则添加一条新的消息
                conversation.messages.push(message);
              }
            } else {
              // 如果对话不存在,创建一个新的对话
              const newConversation = {
                id: conversation_id,
                title: message.content,
                messages: [message],
              };

              state.selected_conversation = newConversation;
   
              state.conversations.push({
                id: conversation_id,
                title: message.content,
                messages: [],
              });
            }
          },
        
        addMessageBack: (state, action) => {
            const { message, conversation_id } = action.payload;
            const conversation = state.conversations.find((c) => c.id === conversation_id);
            if (conversation) {
                // 在消息数组中找到最后一条 AI 消息
              const last_ai_message_index = conversation.messages.findLastIndex((m) => m.id === message.id);
              if (last_ai_message_index === conversation.messages.length -1) {
                let updated_messages = [...conversation.messages];
                let updated_message = { 
                  ...updated_messages[last_ai_message_index], 
                  content: updated_messages[last_ai_message_index].content + message.content, 
                  attachment: message.attachment ,
                  file_urls: message.file_urls
                };
                updated_messages[last_ai_message_index] = updated_message;
                // 更新对话中的消息数组
                let updated_conversations = state.conversations.map(c => 
                  c.id === conversation_id ? { ...c, messages: updated_messages } : c
              );
                state.conversations = updated_conversations;

              } else {
                // 如果还没有 AI 消息,则添加一条新的消息
                conversation.messages.push(message);
              }
            } else {
              // 如果对话不存在,创建一个新的对话
              state.conversations.push({
                id: conversation_id,
                title: message.content,
                messages: [message],
              });
            }
          },
        
        updateConversationTitle:(state,action) =>{
            const { update_conversation_id,edit_text } = action.payload;
            console.log(update_conversation_id,edit_text,'update_conversation_id,edit_text');
            state.conversations = state.conversations.map(conversation =>
                conversation.id === update_conversation_id 
                ? { ...conversation, title: edit_text } 
                : conversation
           )
        },
        setConversations: (state, action ) => {
            state.conversations = action.payload;
            state.session_established = true;
        },
        setConversationHistory:(state,action) =>{
            const { id, title, messages } = action.payload;
            const conversation = state.conversations.find((c) => c.id === id);
            if (conversation){
                conversation.messages = messages;
            } else {
                state.conversations.push({
                    id,
                    title,
                    messages,
                });
            }
        },
        deleteConversations: (state, action) => {
          const conversation_id = action.payload;
          
          if (conversation_id === 'all') {
              state.conversations = [];
              state.selected_conversation_id = 'new'; // 重置 selected_conversation_id
          } else {
              state.conversations = state.conversations.filter(
                  conversation => conversation.id !== conversation_id
              );
              
              // 如果删除的会话是当前选中的会话，重置 selected_conversation_id
              // if (conversation_id === state.selected_conversation_id) {
              //     state.selected_conversation_id = 'new';
              // }
          }
      },      
      setSelectedMessageIndex: (state, action) => {
        state.selected_message_index = action.payload;
      },
      updateSelectedDocs: (state, action) => {
        const {id,doc_id,title} = action.payload;
        const selected_info = {
          id:id,
          doc_id:doc_id,
          title:title
        }
        const existing_index = state.selected_docs.findIndex(doc => doc.id === id);
        console.log(existing_index,'existing_index')
        if (existing_index > -1) {
          // Document is already selected, remove it from the array
          state.selected_docs = state.selected_docs.filter(doc => doc.id !== id);
        } else {
            // Document is not selected, add it to the array (immutably)
            state.selected_docs = [...state.selected_docs, selected_info];
        }
      },
      clearSelectedDocs: (state) => {
        state.selected_docs = [];
      },
      setSelectedDocs: (state,action) => {
        const {selectedDocs} = action.payload;
        // console.log(selectedDocs,'selectedDocs');
        state.selected_docs = selectedDocs
      },
      setIsChat: (state,action) => {
        const {isChat} = action.payload;
        // console.log(selectedDocs,'selectedDocs');
        state.isChat = isChat
      },
    },
});
export const { 
    setSelectedConversationId, 
    addMessage, 
    setConversations,
    setConversationHistory,
    deleteConversations,
    updateConversationTitle,
    setSelectedMessageIndex,
    updateSelectedDocs,
    clearSelectedDocs,
    setSelectedDocs,
    setSelectedConversation,
    setIsChat,
  } = dashboardSlice.actions;
export default dashboardSlice.reducer;