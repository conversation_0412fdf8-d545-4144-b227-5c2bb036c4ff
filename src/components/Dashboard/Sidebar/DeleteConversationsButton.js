import React from 'react';
import { AiOutlineDelete } from 'react-icons/ai';
import { useDispatch,useSelector } from 'react-redux';
import { deleteConversations as deleteConversationsFromStore } from '../dashboardSlice';
import { deleteSession } from '../Chat/utils/apiUtil'

const DeleteConversationsButton = () => {
  const dispatch = useDispatch();
  const conversation_id = 'all';
  const { user_info } = useSelector((state) => state.user);
  const user = user_info.username;
  const handleDeleteConversations = () => {
    dispatch(deleteConversationsFromStore(conversation_id));
    deleteSession(user,conversation_id);

  }
    return (
        <div className="list_item delete_conv_button" onClick={handleDeleteConversations}>
          <div className="list_item_icon"> 
            <AiOutlineDelete color='white' />
          </div>
          <p className='list_item_text'>删除会话记录</p>
    </div>
    );
};
export default DeleteConversationsButton;