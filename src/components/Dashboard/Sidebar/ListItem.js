import React, { useState, useEffect } from 'react';
import { BsChatLeft, BsThreeDots } from 'react-icons/bs';
import { useSelector, useDispatch } from 'react-redux';
import OptionsMenu from './OptionsMenu';
import { updateConversationTitle, clearSelectedDocs, setSelectedConversation } from '../dashboardSlice';
import { deleteConversations as deleteConversationsFromStore } from '../dashboardSlice';
import { deleteSession } from '../Chat/utils/apiUtil';
import { createData } from '../../Routers/Router';
import { MEMORY_UPDATE_TITLE_ENDPOINT } from '../../Configs/Config';

const ListItem = (props) => {
  const { title, handleSetSelectedChat, conversation_id } = props;
  const [is_editing, setIsEditing] = useState(false);
  const [edit_text, setEditText] = useState(title);
  const dispatch = useDispatch();

  const { selected_conversation_id } = useSelector((state) => state.dashboard);
  const { user_info } = useSelector((state) => state.user);
  const user = user_info.username;

  // 新增菜单
  const [showOptions, setShowOptions] = useState(false);

  const toggleOptionsMenu = () => {
    setShowOptions(!showOptions);
  };

  const handleRename = () => {
    setIsEditing(true);
    setShowOptions(false);
  };

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleChange = (e) => {
    setEditText(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
      e.preventDefault(); // 防止Enter键默认行为
      saveChanges();
    }
  };

  const saveChanges = async () => {
    if (edit_text.trim().length === 0) {
      alert('文本不能为空，请输入有效的文本。'); // 提示用户
      setEditText(title);
    } else {
      setIsEditing(false);
      const update_conversation_id = selected_conversation_id ? selected_conversation_id : conversation_id;

      // 先同步更新 Redux state
      dispatch(updateConversationTitle({ update_conversation_id, edit_text }));

      // 异步调用 createData 函数并等待其完成
      const update_content_endpoint = `${MEMORY_UPDATE_TITLE_ENDPOINT}`;
      try {
        await createData(update_content_endpoint, { user: user, title: edit_text, session_id: update_conversation_id });
      } catch (error) {
        console.error('Failed to update conversation title:', error);
        // 你可以在这里添加错误处理逻辑，例如提示用户操作失败
      }
    }
  };

  const handleBlur = () => {
    setShowOptions(false);
    saveChanges();
  };

  const inputStyle = {
    color: 'white', // 字体颜色为白色
    background: 'transparent', // 背景透明
    border: 'none', // 无边框
    outline: 'none', // 去掉焦点轮廓线
    width: '100%', // 宽度100%，根据需要调整
    // 其他需要匹配的样式
  };

  const handleDeleteOption = (conversation_id) => {
    // 使用点击的 conversation_id，而不是 selected_conversation_id
    deleteSession(user, conversation_id);

    // 更新 Redux state，删除对应的会话
    dispatch(deleteConversationsFromStore(conversation_id));
    if (conversation_id === selected_conversation_id) {
      // If the deleted conversation was selected, trigger the NewChatButton behavior
      handleSetSelectedChat('new'); // Simulates creating a new chat
    }  

    // 清空当前选择
    // dispatch(setSelectedConversation({}));
    // dispatch(clearSelectedDocs());

    // 关闭选项菜单
    setShowOptions(false);
  };

  useEffect(() => {
    // 这个函数在点击事件发生时被调用
    if (showOptions) {
      const handleClickOutside = (event) => {
        // 确保OptionsMenu已经被渲染
        if (showOptions && !event.target.closest('.list_item_options')) {
          setShowOptions(false);
        }
      };
      // 给document添加点击事件侦听器
      document.addEventListener('mousedown', handleClickOutside);

      // 组件卸载时移除事件侦听器
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showOptions, conversation_id]); // 仅当showOptions变化时重新绑定事件

  return (
    <div className="list_item" onClick={() => handleSetSelectedChat(conversation_id)}>
      <div className="list_item_icon">
        <BsChatLeft color="white" />
      </div>
      {is_editing ? (
        <input
          type="text"
          value={edit_text}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          autoFocus
          className="list_item_text"
          style={inputStyle}
        />
      ) : (
        <p className="list_item_text" onDoubleClick={handleDoubleClick}>
          {title}
        </p>
      )}
      <div
        className="list_item_options"
        onClick={(e) => {
          e.stopPropagation();
          toggleOptionsMenu();
        }}
      >
        <BsThreeDots color="white" />
        {showOptions && <OptionsMenu onRename={handleRename} onDelete={() => handleDeleteOption(conversation_id)} />}
      </div>
    </div>
  );
};

export default ListItem;
