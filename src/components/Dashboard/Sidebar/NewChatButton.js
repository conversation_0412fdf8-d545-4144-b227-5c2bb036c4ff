import React from 'react';
import { AiOutlinePlus } from 'react-icons/ai';
import { clearSelectedDocs } from '../dashboardSlice';
import { useDispatch } from 'react-redux';
const NewChatButton = ({ handleSetSelectedChat }) => {
    const  dispatch = useDispatch();
    const handleChooseNewChat = () => {
        dispatch(clearSelectedDocs());
        handleSetSelectedChat("new");
    };
    return (
    <div className="new_chat_button" onClick={ handleChooseNewChat }>
        <div className="new_chat_button_icon"> 
        <AiOutlinePlus color='white' />
         </div>
        <p className='new_chat_button_text'> New Chat</p>
    </div>
    );
};
export default NewChatButton;