.sidebar_container {
    height: 100vh;
    background-color: #202123;
    display: flex;
    flex-direction: column;
    width: 250px;
    position: relative;
}

.sidebar_top {
    padding: 10px;
}

.sidebar_scrollable {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
}

.bottom_bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #202123; /* Match sidebar background */
}

.delete_conv_button {
    position: static; /* Changed from absolute to static */
}

.workflow-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
    gap: 1px;
}

.workflow-select {
    width: 50%;
    height: 30px;
    padding: 2px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    font-size: 16px;
}

.workflow-select:focus {
    border-color: #0056b3;
    outline: none;
}

.workflow-config-btn {
    padding: 6px;
    background-color: #0056b3;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.workflow-config-btn:hover {
    background-color: #003f85;
}

/* User label styles */
.user-label {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-name {
    font-weight: bold;
    margin-bottom: 0px;
    color: white;
    font-size: 10px;
    margin-left: 10px;
}

.user-email {
    font-weight: bold;
    font-style: italic;
    color: white;
    margin-left: 2px;
    font-size: 10px;
}