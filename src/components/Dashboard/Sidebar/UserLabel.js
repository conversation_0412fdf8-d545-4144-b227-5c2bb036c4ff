// UserLabel.js
import React from 'react';
import { useSelector } from 'react-redux';
import './Sidebar.css'
const UserLabel = () => {
    // const user = useSelector((state) => state.dashboard.user);
    const {username,user_info} = useSelector((state) => state.user);
    const email = '<EMAIL>';
    const obscuredEmail = email
        ? `${email.substring(0, 2)}...@${email.split('@')[1]}`
        : '';

    return (
        <div className="user-label">
            <span className="user-name">{user_info.username}</span>
            <span className="user-email">{obscuredEmail}</span>
        </div>
    );
};

export default UserLabel;
