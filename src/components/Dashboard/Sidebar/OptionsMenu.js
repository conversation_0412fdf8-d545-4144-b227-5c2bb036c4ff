import React from 'react';
const OptionsMenu = ({ onRename, onDelete }) => {
    const menuStyle = {
      position: 'absolute',
      top: '100%',
      right: '0',
      backgroundColor: 'white',
      borderRadius: '4px',
      boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
      zIndex: 100
    };
    return (
      <div className="options_menu" style={menuStyle}>
        <div style={{ padding: '8px', cursor: 'pointer' }} onClick={onRename}>重命名</div>
        <div style={{ padding: '8px', cursor: 'pointer' }} onClick={onDelete}>删除</div>
      </div>
    );
  };

  export default OptionsMenu;