import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
// import { useNavigate } from 'react-router-dom';
import NewChatButton from './NewChatButton';
import DeleteConversationsButton from './DeleteConversationsButton';
import ListItem from './ListItem';
import UserLabel from './UserLabel';
import { setSelectedConversationId,setSelectedConversation } from '../dashboardSlice';
// import { setSelectedWorkflowId } from '../../Workflow/workflowSlice';
import { WORKFLOWS_NAME } from '../../Configs/Config';
import { fetchSession } from '../Chat/utils/apiUtil';

const Sidebar = () => {
    const dispatch = useDispatch();
    // const navigate = useNavigate();
    // const workflows = useSelector((state) => state.workflow.workflows);
    // const selected_workflow_id = useSelector((state) => state.workflow.selected_workflow_id);
    // const [selectedWorkflow, setSelectedWorkflow] = useState(null);
    const conversations = useSelector((state) => state.dashboard.conversations);
    const { user_info } = useSelector((state) => state.user);

    // useEffect(() => {
    //     if (workflows.length === 0) {
    //         setSelectedWorkflow(null);
    //         return;
    //     }
    //     let selectedWorkflow = workflows.find((workflow) => workflow.id === selected_workflow_id);
    //     if (!selectedWorkflow && workflows.length > 0) {
    //         selectedWorkflow = workflows[0];
    //         dispatch(setSelectedWorkflowId(selectedWorkflow.id));
    //     }
    //     setSelectedWorkflow(selectedWorkflow);
    // }, [workflows, selected_workflow_id, dispatch]);


    // const handleNavigateToWorkflowConfig = () => {
    //     navigate(WORKFLOWS_NAME);
    // };

    const handleSetSelectedChat = async (conversation_id) => {
        dispatch(setSelectedConversationId(conversation_id)); // 更新 selected_conversation_id
    
        try {
            const conversations = await fetchSession(user_info.username, 0, conversation_id); // 异步获取会话数据
            console.log(conversations,'list_item_text')
            const selectedConversation = conversations.find((c) => c.id === conversation_id); // 找到匹配的会话
    
            if (selectedConversation) {
                dispatch(setSelectedConversation(selectedConversation)); // 更新 selected_conversation
            } else {
                dispatch(setSelectedConversation({})); // 更新 selected_conversation
                // console.error('No matching conversation found');
            }
        } catch (error) {
            console.error('Failed to fetch conversations:', error);
        }
    };
    

    return (
        <div className="sidebar_container">
            <div className="sidebar_top">
                <NewChatButton handleSetSelectedChat={handleSetSelectedChat} />
            </div>

            <div className="sidebar_scrollable">
                {conversations.map(c => (
                    <ListItem 
                        key={c.id} 
                        title={c.title} 
                        conversation_id={c.id}
                        handleSetSelectedChat={handleSetSelectedChat}
                    />
                ))}
            </div>

            <div className="sidebar_bottom">
                <DeleteConversationsButton />
                <UserLabel />
            </div>
        </div>
    );
};

export default Sidebar;