import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setSelectedMessageIndex, clearSelectedDocs } from '../dashboardSlice';
import { setSelectedWorkflowId, setWorkflows } from '../../Workflow/workflowSlice';
import { setSelectedAgentId, setAgents } from '../../Workflow/Agents/Agents/AgentSlice';
import { processWorkflows, fetchBulk } from '../../Routers/Router';
import { setTab } from '../../Workflow/Agents/workflows/tabSlice';
import { WORKFLOW_ENDPOINT, AGENT_ENDPOINT } from '../../Configs/Config';
import Message from './Message';
import WorkflowCard from './WorkflowCard';
import styles from './MessagesComponent.module.css'; // 引入 CSS 模块

import {
    useDynamicTheme,
    chatMessagesContainerStyleFunction,
    workflowCardsContainerStyleFunction,
    workflowCardsWrapperStyleFunction
} from '../../Configs/StyleConfigs';

const MessagesComponent = ({ selected_conversation_id }) => {
    const { background_color, text_color } = useDynamicTheme();
    const chatMessagesContainerStyle = chatMessagesContainerStyleFunction(background_color, text_color);
    const workflowCardsContainerStyle = workflowCardsContainerStyleFunction(background_color, text_color);
    const workflowCardsWrapperStyle = workflowCardsWrapperStyleFunction(background_color, text_color);

    const { selected_message_index, selected_conversation } = useSelector((state) => state.dashboard);
    const { selected_workflow_id, workflows } = useSelector((state) => state.workflow);
    const { selected_agent_id, agents } = useSelector((state) => state.agent);
    const { user_info } = useSelector((state) => state.user);
    const { currentTab: tab } = useSelector((state) => state.tab);
    const dispatch = useDispatch();

    const [currentIndex, setCurrentIndex] = useState(0);
    const scrollRef = useRef();

    const fetchWorkflows = useCallback(async () => {
        try {
            const endpoint_api = `${WORKFLOW_ENDPOINT}/bulk`;
            const result = await fetchBulk(endpoint_api);
            const workflowsData = processWorkflows(result.data);
            dispatch(setWorkflows(workflowsData));
        } catch (error) {
            console.error('Failed to fetch workflows:', error);
        }
    }, [dispatch]);

    const fetchAgents = useCallback(async () => {
        try {
            const endpoint_api = `${AGENT_ENDPOINT}/bulk`;
            const result = await fetchBulk(endpoint_api);
            dispatch(setAgents(result.data));
        } catch (error) {
            console.error('Failed to fetch agents:', error);
        }
    }, [dispatch]);

    useEffect(() => {
        if (workflows.length === 0) {
            fetchWorkflows();
        }
    }, [workflows.length, fetchWorkflows]);

    useEffect(() => {
        if (agents.length === 0) {
            fetchAgents();
        }
    }, [agents.length, fetchAgents]);

    const userWorkflows = workflows.filter((workflow) => workflow.username === user_info.username);
    const userAgents = agents.filter((agent) => agent.username === user_info.username);

    useEffect(() => {
        if (userWorkflows.length > 0 && !selected_workflow_id && tab === 'workflows') {
            dispatch(setSelectedWorkflowId(userWorkflows[0].id));
            dispatch(clearSelectedDocs());
        }
    }, [dispatch, userWorkflows, selected_workflow_id, tab]);

    useEffect(() => {
        if (userAgents.length > 0 && !selected_agent_id && tab === 'agents') {
            dispatch(setSelectedAgentId(userAgents[0].id));
            dispatch(clearSelectedDocs());
        }
    }, [dispatch, userAgents, selected_agent_id, tab]);
    const handlePrev = useCallback(() => {
        const currentItems = tab === 'workflows' ? userWorkflows : userAgents;
        const selectedId = tab === 'workflows' ? selected_workflow_id : selected_agent_id;
        const currentIndexInView = currentItems.findIndex((item) => item.id === selectedId);
    
        if (currentIndexInView > 0) {
            const newIndex = currentIndexInView - 1;
            const newSelectedId = currentItems[newIndex].id;
    
            if (tab === 'workflows') {
                dispatch(setSelectedWorkflowId(newSelectedId));
            } else {
                dispatch(setSelectedAgentId(newSelectedId));
            }
    
            dispatch(clearSelectedDocs());
    
            if (newIndex < currentIndex) {
                setCurrentIndex(newIndex);
            }
        }
    }, [tab, userWorkflows, userAgents, selected_workflow_id, selected_agent_id, currentIndex, dispatch]);
    
    const handleNext = useCallback(() => {
        const currentItems = tab === 'workflows' ? userWorkflows : userAgents;
        const selectedId = tab === 'workflows' ? selected_workflow_id : selected_agent_id;
        const currentIndexInView = currentItems.findIndex((item) => item.id === selectedId);
    
        if (currentIndexInView < currentItems.length - 1) {
            const newIndex = currentIndexInView + 1;
            const newSelectedId = currentItems[newIndex].id;
    
            if (tab === 'workflows') {
                dispatch(setSelectedWorkflowId(newSelectedId));
            } else {
                dispatch(setSelectedAgentId(newSelectedId));
            }
    
            dispatch(clearSelectedDocs());
    
            if (newIndex >= currentIndex + 4) {
                setCurrentIndex(currentIndex + 1);
            }
        }
    }, [tab, userWorkflows, userAgents, selected_workflow_id, selected_agent_id, currentIndex, dispatch]);

    useEffect(() => {
        const handleKeyDown = (event) => {
            if (selected_conversation_id === 'new') {
                if (event.key === 'ArrowLeft') {
                    handlePrev();
                } else if (event.key === 'ArrowRight') {
                    handleNext();
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [handlePrev, handleNext, selected_conversation_id]);

    const scrollToButton = useCallback(() => {
        if (scrollRef.current) {
            scrollRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, []);

    useEffect(scrollToButton, [selected_conversation?.messages, scrollToButton]);

    const handleMessageSelect = useCallback((index) => {
        dispatch(setSelectedMessageIndex(index));
    }, [dispatch]);

    const handleCardClick = useCallback((itemId) => {
        console.log(itemId,'itemId')
        if (tab === 'workflows' && itemId !== selected_workflow_id) {
            dispatch(setSelectedWorkflowId(itemId));
            dispatch(clearSelectedDocs());
        } else if (tab === 'agents' && itemId !== selected_agent_id) {
            dispatch(setSelectedAgentId(itemId));
            dispatch(clearSelectedDocs());
        }
    }, [dispatch, selected_workflow_id, selected_agent_id, tab]);

    const handleTabChange = (newTab) => {
        dispatch(setTab(newTab));
        setCurrentIndex(0);
        if (newTab === 'workflows') {
            dispatch(setSelectedWorkflowId(userWorkflows[0]?.id || null));
            dispatch(setSelectedAgentId(null));
        } else {
            dispatch(setSelectedAgentId(userAgents[0]?.id || null));
            dispatch(setSelectedWorkflowId(null));
        }
        dispatch(clearSelectedDocs());
    };

    const displayedItems = tab === 'workflows' ? userWorkflows.slice(currentIndex, currentIndex + 4) : userAgents.slice(currentIndex, currentIndex + 4);
    // console.log(selected_conversation.messages);
    return (
        <div className="chat_messages_container" style={chatMessagesContainerStyle}>
            {!selected_conversation?.messages ? (
                <div className="workflow-cards-wrapper" style={workflowCardsWrapperStyle}>
                    <div className={styles.tabButtonsContainer}>
                        <button
                            className={`${styles.tabButton} ${tab === 'workflows' ? styles.active : ''}`}
                            onClick={() => handleTabChange('workflows')}
                        >
                            工作流
                        </button>
                        <button
                            className={`${styles.tabButton} ${tab === 'agents' ? styles.active : ''}`}
                            onClick={() => handleTabChange('agents')}
                        >
                            智能体
                        </button>
                    </div>
                    <button
                        onClick={handlePrev}
                        disabled={currentIndex === 0}
                        style={{ position: 'absolute', left: 0, top: '50%', transform: 'translateY(-50%)' }}
                    >
                        &lt;
                    </button>
                    <div className="workflow-cards-container" style={workflowCardsContainerStyle}>
                        {displayedItems.map((item) => (
                            <WorkflowCard
                                key={item.id}
                                workflow={item}
                                isSelected={tab === 'workflows' ? item.id === selected_workflow_id : item.id === selected_agent_id}
                                icon={tab === 'workflows' ? "📄" : "🤖"}
                                tab={tab}
                                onClick={() => handleCardClick(item.id)}
                            />
                        ))}
                    </div>
                    <button
                        onClick={handleNext}
                        disabled={currentIndex >= (tab === 'workflows' ? userWorkflows.length : userAgents.length) - 4}
                        style={{ position: 'absolute', right: 0, top: '50%', transform: 'translateY(-50%)' }}
                    >
                        &gt;
                    </button>
                </div>
            ) : (
                selected_conversation?.messages?.map((m, index) => (
                    <Message
                        key={m.id}
                        content_id={m.id}
                        content={m.content}
                        ai_message={m.ai_message}
                        creationTimeUnix={m?._additional?.creationTimeUnix}
                        file_urls={m.file_urls}
                        onSelect={() => handleMessageSelect(index)}
                        is_selected={index === selected_message_index}
                        agent_name={m.agent_name}
                        message_id={m.id}
                        attachments={m.attachment}
                        feedback_num={m.feedback}
                        is_reflection={m.is_reflection}
                        is_function_call={m.is_function_call}
                        delta_stream={m.delta_stream}
                        function_call={m.function_call}
                    />
                ))
            )}
            <div ref={scrollRef} />
        </div>
    );
};

const Messages = React.memo(MessagesComponent, (prevProps, nextProps) => {
    return prevProps.messages?.length === nextProps.messages?.length;
});

export default Messages;
