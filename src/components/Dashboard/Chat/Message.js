import React, { useState, useEffect, useCallback } from 'react';
import { Md<PERSON>onte<PERSON>Copy, MdAttachFile } from 'react-icons/md';
import { Button, message } from 'antd';
import { LikeOutlined, DislikeOutlined, EditOutlined,ShareAltOutlined } from '@ant-design/icons';
import ImageModal from './ImageModal';
import FileDisplay from './FileDisplay';
import { toast } from 'react-toastify';
import { Tooltip } from 'antd';
import 'react-toastify/dist/ReactToastify.css';
import EnhancedMarkdownMessage from './utils/CustomMessageMarkdown';
import ListAttachment from './ListAttachment/ListAttachment';
import { useSelector } from 'react-redux';
import { MEMORY_UPDATE_FEEDBACK_ENDPOINT,MD_ENDPOINT,ARTICLE_ENDPOINT } from '../../Configs/Config';
import { createData } from '../../Routers/Router';
import {
    useDynamicTheme, 
    messageContainerStyleFunction, 
    messageContentStyleFunction, 
    messageTextStyleFunction,
    messageTextareaStyleFunction, 
    messageContentCopyStyleFunction,
    iconContainerStyleFunction,
    messageContentEditStyleFunction,
} from '../../Configs/StyleConfigs';

const MessageComponent = ({
    content, ai_message, file_urls, onSelect, is_selected, agent_name,
     creationTimeUnix, message_id, attachments = null, feedback_num = 0,
     is_reflection=false,is_function_call=false,delta_stream=true,function_call=null
    
 }) => {
    const { background_color, text_color, icon_color, icon_hover_color } = useDynamicTheme();
    const messageContainerStyle = messageContainerStyleFunction(background_color, text_color);
    const messageContentStyle = messageContentStyleFunction(background_color, text_color);
    const messageTextStyle = messageTextStyleFunction(background_color, text_color);
    const messageTextareaStyle = messageTextareaStyleFunction(background_color, text_color);
    const messageContentCopyStyle = messageContentCopyStyleFunction(background_color, icon_color, icon_hover_color);
    const messageContentEditStyle = messageContentEditStyleFunction(background_color, icon_color, icon_hover_color);
    const iconContainerStyle = iconContainerStyleFunction(background_color, icon_color, icon_hover_color);
    const has_files = Array.isArray(file_urls) && file_urls.length > 0;
    const [is_image_viewer_open, setImageViewerOpen] = useState(false);
    const [current_image_url, setCurrentImageUrl] = useState('');
    const [is_editing, setIsEditing] = useState(false);
    const [edited_content, setEditedContent] = useState(content);
    const [feedback, setFeedback] = useState(feedback_num);
    const [is_sidebar_open, setSidebarOpen] = useState(false); // Sidebar control state
    const { isChat } = useSelector((state) => state.dashboard); 
    const { user_info } = useSelector((state) => state.user);

    const handleImageClick = (url) => {
        setCurrentImageUrl(url);
        setImageViewerOpen(true);
    };

    const cancelEdit = useCallback(() => {
        setIsEditing(false);
        setEditedContent(content);
    }, [content]);
    // console.log(is_selected,message_id,'message_idmessage_id');
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === "Escape") {
                cancelEdit();
            }
        };
        document.addEventListener("keydown", handleKeyDown);
        return () => {
            document.removeEventListener("keydown", handleKeyDown);
        };
    }, [cancelEdit]);

    const closeImageViewer = () => {
        setImageViewerOpen(false);
    };

    const handleMessageClick = () => {
        onSelect();
    };

    const handleCopyContent = () => {
        navigator.clipboard.writeText(content).then(() => {
            toast.success('Content copied to clipboard!');
        }).catch(err => {
            toast.error('Failed to copy content.');
            console.error('Failed to copy content: ', err);
        });
    };

    const toggleEdit = () => {
        setIsEditing(true);
        setEditedContent(content);
    };

    const saveEdit = () => {
        setIsEditing(false);
        setEditedContent(edited_content);
    };

    const handleContentChange = (event) => {
        setEditedContent(event.target.value);
    };

    const handleLike = async () => {
        const newFeedback = feedback === 1 ? 0 : 1;
        setFeedback(newFeedback);
        try {
            const result = await createData(`${MEMORY_UPDATE_FEEDBACK_ENDPOINT}`, { uuid: message_id, feedback: newFeedback, user: user_info.username });
            console.log('Data saved successfully:', result);
        } catch (error) {
            console.error('Error saving data:', error);
        }
    };

    const handleDislike = async () => {
        const newFeedback = feedback === -1 ? 0 : -1;
        setFeedback(newFeedback);
        try {
            const result = await createData(`${MEMORY_UPDATE_FEEDBACK_ENDPOINT}`, { uuid: message_id, feedback: newFeedback, user: user_info.username });
            console.log('Data saved successfully:', result);
        } catch (error) {
            console.error('Error saving data:', error);
        }
    };

    // Function to handle attachment click with isChat condition
    const handleAttachmentClick = () => {
        // Only toggle the sidebar when attachments exist
        if (isChat) {
            message.warning('会话中，不能查看附件'); // Show warning if isChat is true
        } else if (attachments && Array.isArray(attachments) && attachments.length > 0) {
            setSidebarOpen(!is_sidebar_open); // Toggle sidebar only if attachments exist
        } else {
            message.warning('无附件可查看'); // Show warning if no attachments exist
        }
    };

    const aiAvatarUrl = 'https://minio.aimed.cn/aigc-meeting/ai.png';
    const userAvatarUrl = 'https://minio.aimed.cn/aigc-meeting/user.png';

    const handleShareClick = async () => {
        if (isChat) {
            message.warning('会话中，不能查看附件'); // Show warning if isChat is true
            return; // Exit the function early if isChat is true
        }
        const endpoint_api = `${ARTICLE_ENDPOINT}/`
        const result = await createData(endpoint_api,{url:null,content_id:message_id,md_article:content})
        window.open(`${MD_ENDPOINT}${result.id}`);
      };

    return (
        <>
            <div className="message_container" onClick={handleMessageClick} style={messageContainerStyle}>
                <div className="message_content" style={messageContentStyle}>
                    {has_files && file_urls.map((fileInfo, index) => (
                        <FileDisplay key={index} fileInfo={fileInfo} onImageClick={handleImageClick} />
                    ))}
                    {content && (
                        <div className="message_text" style={messageTextStyle}>
                            {is_editing ? (
                                <>
                                    <textarea
                                        value={edited_content}
                                        onChange={handleContentChange}
                                        className="message_textarea"
                                        style={messageTextareaStyle}
                                    />
                                    <button onClick={saveEdit} className="edit_button">Save</button>
                                    <button onClick={cancelEdit} className="edit_button">Cancel</button>
                                </>
                            ) : (
                                <EnhancedMarkdownMessage
                                    avatarUrl={ai_message ? aiAvatarUrl : userAvatarUrl}
                                    userName={ai_message ? agent_name : user_info.username}
                                    content={content}
                                    creationTimeUnix={creationTimeUnix}
                                    function_call={function_call}
                                    background_color={background_color}
                                    text_color={text_color}
                                    avatarColor="#ff4d4f"
                                />
                            )}

                            {ai_message ? (
                                <div className="icon_container" style={iconContainerStyle}>
                                    <Button
                                       icon={
                                        <Tooltip title="内容编辑">
                                          <ShareAltOutlined />
                                        </Tooltip>
                                      }
                                        onClick={handleShareClick}
                                        style={{
                                        backgroundColor: background_color,
                                        color: icon_color,
                                        borderRadius: '50%',
                                        padding: '4px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        }}
                                        title="Tool"
                                    />
                                    <Button
                                        icon={
                                            <Tooltip title="点赞">
                                              <LikeOutlined />
                                            </Tooltip>
                                          }
                                        onClick={handleLike}
                                        style={{
                                            backgroundColor: feedback === 1 ? 'green' : background_color,
                                            color: feedback === 1 ? '#fff' : icon_color,
                                            borderRadius: '50%',
                                            padding: '4px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                        title="Like"
                                    />
                                    <Button
                                        icon={
                                            <Tooltip title="点踩">
                                                <DislikeOutlined />
                                            </Tooltip>
                                        }
                                        onClick={handleDislike}
                                        style={{
                                            backgroundColor: feedback === -1 ? 'red' : background_color,
                                            color: feedback === -1 ? '#fff' : icon_color,
                                            borderRadius: '50%',
                                            padding: '4px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                        title="Dislike"
                                    />
                                    <Button
                                        icon={
                                        <Tooltip title="拷贝内容">
                                            <MdContentCopy />
                                        </Tooltip>
                                        }
                                        onClick={handleCopyContent}
                                        style={messageContentCopyStyle}
                                        title="Copy Content"
                                    />
                                    {attachments && Array.isArray(attachments) && attachments.length > 0 && (
                                        <Button
                                            icon={
                                            <Tooltip title="附件">
                                                <MdAttachFile />
                                            </Tooltip>
                                        }
                                            onClick={handleAttachmentClick}
                                            style={{
                                                backgroundColor: background_color,
                                                color: icon_color,
                                                borderRadius: '50%',
                                                padding: '4px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}
                                            title="Attachments"
                                        />
                                    )}
                                </div>
                            ) : (
                                <Button
                                    icon={
                                    <Tooltip title="编辑">
                                        <EditOutlined />
                                    </Tooltip>
                                }
                                    onClick={toggleEdit}
                                    style={messageContentEditStyle}
                                    title="Edit Content"
                                />
                            )}
                        </div>
                    )}
                </div>
                {is_image_viewer_open && <ImageModal is_open={is_image_viewer_open} url={current_image_url} onClose={closeImageViewer} />}
            </div>
            <ListAttachment is_open={is_sidebar_open} onToggle={setSidebarOpen} />
        </>
    );
};

const Message = React.memo(MessageComponent);
export default Message;
