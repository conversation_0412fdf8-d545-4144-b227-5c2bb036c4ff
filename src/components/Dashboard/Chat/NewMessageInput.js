import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { v4 as uuid } from 'uuid';
import { BsSend } from 'react-icons/bs';
import { IoMdCloseCircle, IoIosCloseCircleOutline } from "react-icons/io";
import { MdOutlineSecurityUpdateWarning } from "react-icons/md";
import { ToastContainer, toast } from 'react-toastify';
import { notification, Modal, Select } from 'antd';
import 'react-toastify/dist/ReactToastify.css';
import LoadingSpinner from '../loadingSpinner';
import ExampleCard from './ExampleCard'; // Import your ExampleCard component
import { uploadFileFunction } from '../../Routers/Router';
import { setSelectedConversationId, addMessage, setSelectedMessageIndex, clearSelectedDocs, setIsChat } from '../dashboardSlice';
import { AGENT_LLM_REQUEST_ENDPOINT, KNOWLEDGE_BASE_ENDPOINT, USER, MANAGER_API_BASE_URL, FILE_MANAGER_ENDPOINT,TOOL_ENDPOINT } from '../../Configs/Config';
import { fetchKbData } from '../../utils/fetchKbData';
import FileUpload from './Upload';

import { getFileIcon } from './utils/utils';
import { fetchBulk, fetchData } from '../../Routers/Router';
import './ExampleCard.css'
import {
    useDynamicTheme,
    newMessageInputStyleFunction,
    newMessageInputContainerStyleFunction,
    newMessageInputInnerStyleFunction,
    newMessageIconContainerStyleFunction,
    deleteFileIconStyleFunction,
    exampleCardsContainerStyleFunction,
    exampleCardsContainerTextStyleFunction
} from '../../Configs/StyleConfigs';

const { Option } = Select;

const NewMessageInput = () => {
    const dispatch = useDispatch();
    const { background_color, text_color, iconHover_color } = useDynamicTheme();
    const styles = {
        container: newMessageInputContainerStyleFunction(background_color, text_color),
        input: newMessageInputStyleFunction(background_color, text_color),
        deleteFileIcon: deleteFileIconStyleFunction(background_color, text_color),
        inner: newMessageInputInnerStyleFunction(background_color, text_color),
        iconContainer: newMessageIconContainerStyleFunction(background_color, text_color, iconHover_color),
        exampleCardsContainer: exampleCardsContainerStyleFunction(background_color, text_color),
        exampleCardsContainerText: exampleCardsContainerTextStyleFunction(background_color, text_color),
    };

    const { selected_conversation_id, conversations, selected_docs } = useSelector((state) => state.dashboard);
    const { user_info } = useSelector((state) => state.user);
    const { selected_workflow_id, workflows } = useSelector((state) => state.workflow);
    const { selected_agent_id, agents } = useSelector((state) => state.agent);

    const [querySelectedDocs, setQuerySelectedDocs] = useState(selected_docs);
    const [files, setFiles] = useState([]);
    const [content, setContent] = useState("");
    const [fileUrls, setFileUrls] = useState([]);
    const [isUploading, setIsUploading] = useState(false);

    const [isModalVisible, setIsModalVisible] = useState(false); // 控制弹窗的可见性
    const [selectedKbId, setSelectedKbId] = useState(null); // 选择的知识库 ID
    const [draggedFiles, setDraggedFiles] = useState([]); // 拖拽的文件列表

    const user = user_info.username;
    const selected_workflow = selected_workflow_id ? workflows.find(item => item.id === selected_workflow_id) : null;
    const selected_agent = selected_agent_id ? agents.find(item => item.id === selected_agent_id) : null;
    const workflow_name = selected_workflow ? selected_workflow.name : null;
    const conversation_id = selected_conversation_id === 'new' ? uuid() : selected_conversation_id;
    const [wsStatus, setWsStatus] = useState('disconnected');

    const inputRef = useRef(null);
    const wsRef = useRef(null);

    const [kbData, setkbData] = useState([]);

    useEffect(() => {
        const fetchKbData = async () => {
            try {
                const data = await fetchBulk(`${KNOWLEDGE_BASE_ENDPOINT}/bulk`);
                setkbData(data.data);
            } catch (error) {
                console.error("Failed to fetch LLM data:", error);
            }
        };
        fetchKbData();
    }, []);

    useEffect(() => {
        const uniqueDocsMap = new Map();
        selected_docs.forEach(doc => {
            uniqueDocsMap.set(doc.doc_id, doc);
        });
        
        setQuerySelectedDocs(Array.from(uniqueDocsMap.values()));
    }, [selected_docs]);

    useEffect(() => {
        const selected_conversation = conversations.find(c => c.id === selected_conversation_id);
        const new_message_index = selected_conversation && selected_conversation.messages.length > 0
            ? selected_conversation.messages.length - 1
            : null;
        if (new_message_index !== null) {
            dispatch(setSelectedMessageIndex(new_message_index));
        }
    }, [selected_conversation_id, conversations, dispatch]);

    useEffect(() => {
        if (selected_conversation_id !== 'new') {
            dispatch(clearSelectedDocs());
        }
    }, [selected_conversation_id, dispatch]);

    useEffect(() => {
        return () => {
            if (wsRef.current) {
                wsRef.current.close();
                wsRef.current = null;
            }
        };
    }, []);

    const connectWebSocket = () => {
        if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
            wsRef.current = new WebSocket(AGENT_LLM_REQUEST_ENDPOINT);

            wsRef.current.onopen = () => {
                console.log("WebSocket connection established.");
                setWsStatus('connected');

                // Start keepalive ping
                wsRef.current.keepAliveInterval = setInterval(() => {
                    if (wsRef.current.readyState === WebSocket.OPEN) {
                        wsRef.current.send(JSON.stringify({ type: 'ping' }));
                    }
                }, 300000); // send ping every 30 seconds
            };

            wsRef.current.onerror = (error) => {
                console.error("WebSocket error:", error);
                setWsStatus('error');
            };

            wsRef.current.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'ping') {
                    wsRef.current.send(JSON.stringify({ type: 'pong' }));
                }
            };

            wsRef.current.onclose = (event) => {
                if (wsRef.current && wsRef.current.keepAliveInterval) {
                    clearInterval(wsRef.current.keepAliveInterval);
                }

                if (event.wasClean) {
                    console.log(`WebSocket closed cleanly, code=${event.code}, reason=${event.reason}`);
                    setIsUploading(false);
                } else {
                    console.error('WebSocket connection died');
                }
                setWsStatus('disconnected');
                console.log("Connection to the server was lost. Attempting to reconnect...");
                // Attempt to reconnect after a short delay
                setTimeout(connectWebSocket, 5000);
            };
        }
    };

    useEffect(() => {
        connectWebSocket();

        return () => {
            if (wsRef.current) {
                if (wsRef.current.keepAliveInterval) {
                    clearInterval(wsRef.current.keepAliveInterval);
                }
                wsRef.current.close();
            }
        };
    }, []);

    const processMessage = async (message, nodes) => {
        if (wsStatus !== 'connected') {
            toast.error("正在连接服务器。请稍后重试。");
            return;
        }
        dispatch(setSelectedConversationId(conversation_id));
        dispatch(addMessage({
            conversation_id,
            message,
        }));
        const attachments = await Promise.all(
            querySelectedDocs.map(async (doc) => {
                try {
                    const endpoint_api = `${FILE_MANAGER_ENDPOINT}/${doc.id}`;
                    const attachment = await fetchData(endpoint_api);
                    return attachment; // Return the doc_id with its corresponding attachment
                } catch (error) {
                    console.error(`Failed to fetch attachment for doc_id: ${doc.id}`, error);
                    toast.error(`Failed to fetch attachment for doc_id: ${doc.id}`);
                    return null; // Return null if there's an error
                }
            })
        );
        const validAttachments = attachments.filter(attachment => attachment !== null);

        // console.log(validAttachments,'success');
        const queryObject = {
            id: uuid(),
            content: content,
            session_id: conversation_id,
            user: user,
            selected_workflow: selected_workflow,
            selected_agent:selected_agent,
            workflow_name: workflow_name,
            role: USER,
            ai_message: false,
            doc_id: querySelectedDocs,
            nodes: nodes,
            manager_api_base_url: MANAGER_API_BASE_URL,
            attachments: validAttachments
        };
        console.log(queryObject, 'queryObject');
        wsRef.current.send(JSON.stringify(queryObject));
        setQuerySelectedDocs([]);
        dispatch(clearSelectedDocs());
        setContent("");
        setFiles([]);

        let accumulated_assistant_message = '';

        return new Promise((resolve, reject) => {
            const messageHandler = (event) => {
                try {
                    const parse_data = JSON.parse(event.data);
                    // console.log(parse_data,'dddddd')
                    const is_stream = parse_data.is_stream;
                    // const stream_data = parse_data.content[0].content;
                    // const agent_name = parse_data.agent_name;
                    // accumulated_assistant_message += stream_data;

                    if (is_stream && !parse_data.is_completed) {
                        // const is_stream = parse_data.is_stream;
                        const stream_data = parse_data.content[0].content;
                        const agent_name = parse_data.agent_name;
                        accumulated_assistant_message += stream_data;
                        dispatch(
                            addMessage({
                                conversation_id,
                                message: {
                                    ...message,
                                    content: stream_data,
                                    ai_message: parse_data.ai_message,
                                    session_id: conversation_id,
                                    user: user,
                                    workflow_name: workflow_name,
                                    id: parse_data.id,
                                    role: parse_data.role,
                                    agent_name: agent_name,
                                    attachment: parse_data.attachment,
                                    token: parse_data.token,
                                    is_reflection:parse_data.is_reflection,
                                    function_call:parse_data.function_call,
                                    is_function_call:parse_data.is_function_call,
                                    file_urls: parse_data?.file_urls || []
                                },
                            })
                        );
                    }

                    if (!is_stream && parse_data.is_completed) {
                        // 所有节点处理完毕，解析 Promise
                        dispatch(setIsChat({isChat:false}));
                        wsRef.current.removeEventListener('message', messageHandler);
                        resolve({ accumulated_assistant_message });
                    }
                } catch (error) {
                    console.error("处理WebSocket消息时出错:", error);
                    wsRef.current.removeEventListener('message', messageHandler);
                    reject(error);
                }

            };

            wsRef.current.addEventListener('message', messageHandler);


        });
    };

    const handleExampleClick = (example) => {
        if (example.conversable) {
            setContent(example.text);

            // 确保输入框聚焦
            setTimeout(() => {
                if (inputRef.current) {
                    inputRef.current.focus();
                    // 将光标移至文本末尾
                    inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);
                }
            }, 0); // 确保状态更新后执行
        } else {
            // 使用 notification 显示提示框
            const notificationKey = `open${Date.now()}`; // 唯一标识符，防止重复
            let backgroundColor = '#8B0000'; // 初始浅红色
            notification.info({
                key: notificationKey,
                message: '提示',
                description: (
                    <span style={{ color: backgroundColor, fontWeight: 'bold' }}>
                        {example.text}
                    </span>
                ),
                duration: 3, // 自动关闭时间（秒）
            });
        }
    };

    const handleSendMessage = async () => {
        if (isUploading) return;
        if (wsStatus !== 'connected') {
            toast.error("正在尝试连接服务器。请稍后重试。");
            connectWebSocket();
            return;
        }

        // const ai_message_id = uuid();
        const message = {
            ai_message: false,
            content,
            id: uuid(),
            animate: false,
            workflow_name: workflow_name,
            session_id: conversation_id,
            file_urls: fileUrls,
        };

        if (content.length > 0 || files.length > 0) {
            setIsUploading(true);
            try {
                const nodes = await fetchKbData(selected_workflow);
                dispatch(setIsChat({ isChat: true }));
                await processMessage(message, nodes);
            } catch (error) {
                console.error("处理消息时出错:", error);
                toast.error("发送消息时发生错误。请重试。");
            } finally {
                setFileUrls([]);
                setFiles([]);
                setContent("");
                dispatch(setIsChat({ isChat: false }));
                setIsUploading(false);
            }
        }
    };

    const handleFileSelectSuccess = (newFiles, newFileUrls) => {
        setIsUploading(false);
        const filteredFiles = [];
        const filteredFileUrls = [];
        const duplicateFileNames = [];
        newFiles.forEach((newFile, index) => {
            if (files.some(file => file.name === newFile.name)) {
                duplicateFileNames.push(newFile.name);
            } else {
                filteredFiles.push(newFile);
                filteredFileUrls.push(newFileUrls[index]);
            }
        });

        if (duplicateFileNames.length > 0) {
            toast.warn(`The following files have already been uploaded: ${duplicateFileNames.join(", ")}`);
        }
        setFiles(prevFiles => [...prevFiles, ...filteredFiles]);
        setFileUrls(prevFileUrls => [...prevFileUrls, ...filteredFileUrls]);
    };

    const handleDeleteFile = (fileName) => {
        setFiles(prevFiles => prevFiles.filter(file => file.name !== fileName));
        setFileUrls(prevFileUrls => prevFileUrls.filter(fileUrl => fileUrl.filename !== fileName));
    };

    const handleKeyPressed = (e) => {
        if (isUploading) return;
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    const handleDrop = (event) => {
        event.preventDefault();
        const files = Array.from(event.dataTransfer.files);

        // 设置拖拽的文件并显示弹窗让用户选择知识库
        setDraggedFiles(files);
        setIsModalVisible(true); // 显示选择知识库的弹窗
    };

    const handleModalOk = async () => {
        if (!selectedKbId) {
            toast.error("请选择一个知识库后再上传文件");
            return;
        }

        // 上传每个文件
        for (const file of draggedFiles) {
            try {
                const result = await uploadFileFunction(file, selectedKbId);
                handleFileSelectSuccess([file], [result]); // 假设 result 包含文件URL等信息
                toast.success("文件上传成功");
            } catch (error) {
                console.error("File upload error:", error);
                toast.error("上传文件失败，请重试");
            } finally {
                setIsUploading(false);
            }
        }

        // 清理状态并关闭弹窗
        setDraggedFiles([]);
        setSelectedKbId(null);
        setIsModalVisible(false);
    };

    const handleModalCancel = () => {
        // 取消选择知识库，清空文件列表并关闭弹窗
        setDraggedFiles([]);
        setSelectedKbId(null);
        setIsModalVisible(false);
    };

    const handleKbSelectChange = (value) => {
        setSelectedKbId(value); // 设置选择的知识库 ID
    };

    const handleClearSelectedDocs = () => {
        dispatch(clearSelectedDocs());
    };

    return (
        <>
            {wsStatus !== 'connected' && <LoadingSpinner />}
            {selected_conversation_id === 'new' && selected_workflow?.examples?.length > 0 && (
                <div className="example-cards-container" style={styles.exampleCardsContainer}>
                    {selected_workflow.examples.slice(0, 4).map((example, index) => (
                        <ExampleCard
                            key={index}
                            example={example.text}
                            onClick={() => handleExampleClick(example)}
                        />
                    ))}
                </div>
            )}
            <div className="new_message_input_container"
                onDragOver={(e) => e.preventDefault()}
                onDrop={handleDrop}
                style={styles.container}
            >
                {selected_docs.length > 0 && (
                    <div className="selected-doc-ids-warning">
                        <MdOutlineSecurityUpdateWarning className="warning-icon" />
                        <span className="warning-text">
                            Selected articles: {querySelectedDocs.map(doc => doc.title).join(", ")}
                        </span>
                        <IoIosCloseCircleOutline className="close-icon" onClick={handleClearSelectedDocs} />
                    </div>
                )}
                <ToastContainer
                    position="top-right"
                    autoClose={5000}
                    hideProgressBar={false}
                    closeOnClick
                    pauseOnHover
                    draggable
                />
                <div className="uploaded-files-info">
                    {files.map(file => (
                        <div key={file.name} className="uploaded-file-info">
                            {getFileIcon(file.name)}
                            <div className="file-name-container">
                                <span className="file-name" title={file.name}>
                                    {file.name.length > 6 ? `${file.name.substring(0, 6)}...` : file.name}
                                </span>
                                <div className="file-name-tooltip">{file.name}</div>
                            </div>
                            <IoMdCloseCircle
                                className="delete-file-icon"
                                style={styles.deleteFileIcon}
                                onClick={() => handleDeleteFile(file.name)}
                            />
                        </div>
                    ))}
                </div>
                <div className="new_message_input_inner" style={styles.inner}>
                    <textarea
                        ref={inputRef}
                        className="new_message_input"
                        placeholder='点击上方示例或输入文本'
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        onKeyDown={handleKeyPressed}
                        style={styles.input}
                    />
                    <FileUpload
                        setIsUploading={setIsUploading}
                        isUploading={isUploading}
                        onFileSelectSuccess={handleFileSelectSuccess}
                        onFileSelectError={(error) => {
                            console.error("File selection error:", error);
                            toast.error("Failed to select files. Please try again.");
                        }}
                        WorkflowName={workflow_name}
                        conversationId={conversation_id}
                        user={user}
                    />
                    <div
                        className='new_message_icon_container'
                        onClick={handleSendMessage}
                        style={{
                            ...styles.iconContainer,
                            opacity: wsStatus !== 'connected' ? 0.5 : 1,
                            cursor: wsStatus !== 'connected' ? 'not-allowed' : 'pointer'
                        }}
                        disabled={wsStatus !== 'connected' || isUploading}
                    >
                        <BsSend color={wsStatus !== 'connected' ? 'grey' : 'inherit'} />
                    </div>
                </div>
                {wsStatus !== 'connected' && (
                    <div style={{ color: 'red', marginTop: '10px' }}>
                        {wsStatus === 'error' ? 'Connection error. Retrying...' : 'Connecting to server...'}
                    </div>
                )}
            </div>

            {/* 知识库选择弹窗 */}
            <Modal
                title="选择知识库"
                // visible={isModalVisible}
                open={isModalVisible}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
            >
                <Select
                    placeholder="请选择一个知识库"
                    style={{ width: '100%' }}
                    onChange={handleKbSelectChange}
                >
                    {kbData.map((kb) => (
                        <Option key={kb.id} value={kb.id}>
                            {kb.name} {/* 假设知识库名称字段为 kb_name */}
                        </Option>
                    ))}
                </Select>
            </Modal>
        </>
    );
};

export default NewMessageInput;
