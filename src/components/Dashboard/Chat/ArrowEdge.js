import React from 'react';
import { getSmoothStepPath } from 'reactflow';

const ArrowEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  arrowHeadType,
  markerEndId,
}) => {
  // 获取平滑的路径字符串
  const edgePath = getSmoothStepPath(
    { x: sourceX, y: sourceY, sourcePosition },
    { x: targetX, y: targetY, targetPosition }
  );

  // 标记结束的ID应引用defs中的箭头标记ID
  const markerEnd = markerEndId ? `url(#${markerEndId})` : undefined;

  return (
    <>
      {/* 渲染边的路径 */}
      <path
        id={id}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
        style={style}
      />
      {/* 如果有任何标签数据，渲染它 */}
      {data && data.label && (
        <text>
          <textPath href={`#${id}`} style={{ fontSize: '12px' }} startOffset="50%" textAnchor="middle">
            {data.label}
          </textPath>
        </text>
      )}
    </>
  );
};

export default ArrowEdge;
