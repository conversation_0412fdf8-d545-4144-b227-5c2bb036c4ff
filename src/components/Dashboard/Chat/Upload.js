import React, { useRef } from 'react';
import { MdAttachFile } from "react-icons/md";
import { FaSpinner } from 'react-icons/fa';
import { uploadFiles } from '../../Routers/Router';

import { 
    useDynamicTheme, 
    uploadIconContainerStyleFunction,
    fileUploadContainerStyleFunction
} from '../../Configs/StyleConfigs';

const FileUpload = ({ setIsUploading,isUploading, onFileSelectSuccess, onFileSelectError,WorkflowName,conversationId,user }) => {
    const { background_color, text_color } = useDynamicTheme();

    const uploadIconContainerStyle = uploadIconContainerStyleFunction(background_color, text_color);
    const fileUploadContainerStyle = fileUploadContainerStyleFunction(background_color, text_color);
    const fileInputRef = useRef(null);
    const inputRef = useRef(null); 
    const handleFileChange = async (e) => {
        setIsUploading(true);
        // setIsDisabled(true); 
        const files = Array.from(e.target.files);
        uploadFiles(files,onFileSelectSuccess, onFileSelectError,WorkflowName,conversationId,user,setIsUploading);
    };

    const handleFileInputClick = () => {
        fileInputRef.current.click();
    };
    return (
        <div className="file-upload-container" ref={inputRef} style={fileUploadContainerStyle}>
            <div className="upload-icon-container" onClick={handleFileInputClick} style={uploadIconContainerStyle}>
                {
                    isUploading ? (
                        <FaSpinner className="loading-icon" />
                    ):(
                        <MdAttachFile />
                    )
                }
            </div>
            <input 
                type="file" multiple 
                onChange={handleFileChange} 
                ref={fileInputRef} 
                style={{ display: 'none' }} 
                // disabled={isUploading}
            />
        </div>
        );
};

export default FileUpload;
