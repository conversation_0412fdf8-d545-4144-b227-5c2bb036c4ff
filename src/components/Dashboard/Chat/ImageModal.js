import React, { useEffect } from 'react';
import { Modal } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import './ImageModal.css';

const ImageModal = ({ is_open, url, onClose }) => {
  useEffect(() => {
    const handleEsc = (event) => {
      if (event.key === 'Escape') onClose();
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose]);

  return (
    <Modal
      open={is_open}
      onCancel={onClose}
      footer={null}
      width="auto"
      centered
      closeIcon={<CloseOutlined />}
      wrapClassName="image-modal-wrap"
    >
      <img src={url} alt="Enlarged view" style={{ maxWidth: '100%', maxHeight: '80vh' }} />
    </Modal>
  );
};

export default ImageModal;