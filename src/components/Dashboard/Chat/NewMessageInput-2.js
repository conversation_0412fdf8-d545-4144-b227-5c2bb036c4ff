// import React, { useState, useEffect, useRef, useCallback } from 'react';
// import { useSelector, useDispatch } from 'react-redux';
// import { v4 as uuid } from 'uuid';
// import { BsSend } from 'react-icons/bs';
// import { IoMdCloseCircle, IoIosCloseCircleOutline } from "react-icons/io";
// import { MdOutlineSecurityUpdateWarning } from "react-icons/md";
// import { ToastContainer, toast } from 'react-toastify';
// import 'react-toastify/dist/ReactToastify.css';
// import { notification } from 'antd';

// import { setSelectedConversationId, addMessage, setSelectedMessageIndex, clearSelectedDocId } from '../dashboardSlice';
// import { AGENT_LLM_REQUEST_ENDPOINT, ASSISTANT, KNOWLEDGE_BASE_ENDPOINT, LLM_ENDPOINT, PROMPT_ENDPOINT, USER } from '../../Configs/Config';
// import FileUpload from './Upload';
// import { uploadFiles } from './uploadUtils';
// import { getFileIcon } from './utils/utils';
// import { fetchData, createData } from '../../Routers/Router';
// import { 
//     useDynamicTheme,
//     newMessageInputStyleFunction,
//     newMessageInputContainerStyleFunction,
//     newMessageInputInnerStyleFunction,
//     newMessageIconContainerStyleFunction,
//     deleteFileIconStyleFunction
// } from '../../Configs/StyleConfigs';

// const NewMessageInput = () => {
//     const dispatch = useDispatch();
//     const { background_color, text_color, iconHover_color } = useDynamicTheme();
//     const styles = {
//         container: newMessageInputContainerStyleFunction(background_color, text_color),
//         input: newMessageInputStyleFunction(background_color, text_color),
//         deleteFileIcon: deleteFileIconStyleFunction(background_color, text_color),
//         inner: newMessageInputInnerStyleFunction(background_color, text_color),
//         iconContainer: newMessageIconContainerStyleFunction(background_color, text_color, iconHover_color)
//     };

//     const { selected_conversation_id, conversations, selected_doc_ids } = useSelector((state) => state.dashboard);
//     const { user_info } = useSelector((state) => state.user);
//     const { selected_workflow_id, workflows } = useSelector((state) => state.workflow);

//     const [querySelectedDocIds, setQuerySelectedDocIds] = useState(selected_doc_ids.map(doc => doc.doc_id));
//     const [files, setFiles] = useState([]);
//     const [content, setContent] = useState("");
//     const [fileUrls, setFileUrls] = useState([]);
//     const [isUploading, setIsUploading] = useState(false);

//     const user = user_info.username;
//     const selected_workflow = selected_workflow_id ? workflows.find(item => item.id === selected_workflow_id) : null;
//     const workflow_name = selected_workflow ? selected_workflow.workflow_type : null;
//     const conversation_id = selected_conversation_id === 'new' ? uuid() : selected_conversation_id;
//     const [wsStatus, setWsStatus] = useState('disconnected');
    
//     console.log(selected_workflow,'selected_workflowselected_workflow');
//     const inputRef = useRef(null);
//     const wsRef = useRef(null);

//     useEffect(() => {
//         const uniqueDocIds = Array.from(new Set(selected_doc_ids.map(doc => doc.doc_id)));
//         if (JSON.stringify(uniqueDocIds) !== JSON.stringify(querySelectedDocIds)) {
//             setQuerySelectedDocIds(uniqueDocIds);
//         }
//     }, [selected_doc_ids, querySelectedDocIds]);

//     useEffect(() => {
//         const selected_conversation = conversations.find(c => c.id === selected_conversation_id);
//         const new_message_index = selected_conversation && selected_conversation.messages.length > 0
//             ? selected_conversation.messages.length - 1
//             : null;
//         dispatch(setSelectedMessageIndex(new_message_index));
//     }, [selected_conversation_id, conversations, dispatch]);

//     useEffect(() => {
//         if (selected_conversation_id !== 'new') {
//             dispatch(clearSelectedDocId());
//         }
//     }, [selected_conversation_id, dispatch]);

//     useEffect(() => {
//         return () => {
//             if (wsRef.current) {
//                 wsRef.current.close();
//                 wsRef.current = null;
//             }
//         };
//     }, []);
//     const fetchKbData = useCallback(async () => {
//         if (!selected_workflow?.nodes) {
//             console.log('selected_workflow or selected_workflow.nodes is null or undefined');
//             return null;
//         }
    
//         const fetchDataIfExists = async (node,endpoint, selectKey) => {
//             if (node.data?.selects?.[selectKey]) {
//                 return await fetchData(`${endpoint}/query_by_name/${node.data.selects[selectKey]}`);
//             }
//             return null;
//         };
    
//         const kbPromises = selected_workflow.nodes.map(async (node) => {
//             const kb_data = await fetchDataIfExists(node,KNOWLEDGE_BASE_ENDPOINT, 'kb_name');
//             const llm_data = await fetchDataIfExists(node,LLM_ENDPOINT, 'llm_name');
//             const positive_prompt = await fetchDataIfExists(node,PROMPT_ENDPOINT, 'positive_prompt');
//             const negative_prompt = await fetchDataIfExists(node,PROMPT_ENDPOINT, 'negative_prompt');
    
//             return {
//                 ...node,
//                 data: {
//                     ...node.data,
//                     selects: {
//                         ...node.data.selects,
//                         id: node.id,
//                         agent_name: node.type,
//                         kb_data,
//                         llm_data,
//                         positive_prompt_data:positive_prompt,
//                         negative_prompt_data:negative_prompt
//                     }
//                 }
//             };
//         });
    
//         return await Promise.all(kbPromises);
//     }, [selected_workflow]);
    
    
//     // const connectWebSocket = useCallback(() => {
//     //     if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
//     //         wsRef.current = new WebSocket(AGENT_LLM_REQUEST_ENDPOINT);
            
//     //         wsRef.current.onopen = () => {
//     //             console.log("WebSocket connection established.");
//     //             setWsStatus('connected');
//     //         };

//     //         wsRef.current.onerror = (error) => {
//     //             console.error("WebSocket error:", error);
//     //             setWsStatus('error');
//     //         };

//     //         wsRef.current.onclose = (event) => {
//     //             if (event.wasClean) {
//     //                 console.log(`WebSocket closed cleanly, code=${event.code}, reason=${event.reason}`);
//     //                 notification.error({
//     //                     message: '后台服务不稳定，请重试',
//     //                     description: '',
//     //                   });
//     //                 setIsUploading(false)
//     //             } else {
//     //                 console.error('WebSocket connection died');
//     //             }
//     //             setWsStatus('disconnected');
//     //             console.log("Connection to the server was lost. Attempting to reconnect...");
//     //             // Attempt to reconnect after a short delay
//     //             setTimeout(connectWebSocket, 5000);
//     //         };
//     //     }
//     // }, []);

//     const connectWebSocket = useCallback(() => {
//         if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
//             wsRef.current = new WebSocket(AGENT_LLM_REQUEST_ENDPOINT);
            
//             wsRef.current.onopen = () => {
//                 console.log("WebSocket connection established.");
//                 setWsStatus('connected');
                
//                 // Start keepalive ping
//                 wsRef.current.keepAliveInterval = setInterval(() => {
//                     if (wsRef.current.readyState === WebSocket.OPEN) {
//                         wsRef.current.send(JSON.stringify({ type: 'ping' }));
//                     }
//                 }, 30000); // send ping every 30 seconds
//             };
    
//             wsRef.current.onerror = (error) => {
//                 console.error("WebSocket error:", error);
//                 setWsStatus('error');
//             };
    
//             wsRef.current.onclose = (event) => {
//                 if (wsRef.current.keepAliveInterval) {
//                     clearInterval(wsRef.current.keepAliveInterval);
//                 }
    
//                 if (event.wasClean) {
//                     console.log(`WebSocket closed cleanly, code=${event.code}, reason=${event.reason}`);
//                     notification.error({
//                         message: '后台服务不稳定，请重试',
//                         description: '',
//                     });
//                     setIsUploading(false);
//                 } else {
//                     console.error('WebSocket connection died');
//                 }
//                 setWsStatus('disconnected');
//                 console.log("Connection to the server was lost. Attempting to reconnect...");
//                 // Attempt to reconnect after a short delay
//                 setTimeout(connectWebSocket, 5000);
//             };
//         }
//     }, []);
    
// useEffect(() => {
//     connectWebSocket();

//     return () => {
//         if (wsRef.current) {
//             if (wsRef.current.keepAliveInterval) {
//                 clearInterval(wsRef.current.keepAliveInterval);
//             }
//             wsRef.current.close();
//         }
//     };
// }, [connectWebSocket]);
//     const processMessage = useCallback(async (ai_message_id, message, nodes) => {
//         if (wsStatus !== 'connected') {
//             toast.error("正在连接服务器。请稍后重试。");
//             return;
//         }
    
//         dispatch(addMessage({
//             conversation_id,
//             message,
//         }));
//         dispatch(setSelectedConversationId(conversation_id));
    
//         const queryObject = {
//             id: uuid(),
//             content: content,
//             session_id: conversation_id,
//             user: user,
//             selected_workflow: selected_workflow,
//             workflow_name: workflow_name,
//             role: USER,
//             ai_message: false,
//             doc_id: [
//                 ...querySelectedDocIds.map(doc => doc.doc_id),
//                 ...fileUrls.map(fileUrl => fileUrl.doc_id).filter(Boolean)
//             ],
//             nodes: nodes
//         };
//         wsRef.current.send(JSON.stringify(queryObject));
    
//         setContent("");
//         setFiles([]);
    
//         let accumulated_assistant_message = '';
    
//         return new Promise((resolve, reject) => {
//             const messageHandler = (event) => {
//                 try {
//                     const parse_data = JSON.parse(event.data);
//                     const is_stream = parse_data.is_stream;
//                     const stream_data = parse_data.content[0].content;
//                     accumulated_assistant_message += stream_data;
//                     dispatch(
//                         addMessage({
//                             conversation_id,
//                             message: {
//                                 ...message,
//                                 content: stream_data,
//                                 ai_message: true,
//                                 session_id: conversation_id,
//                                 user: user,
//                                 workflow_name: workflow_name,
//                                 id: ai_message_id,
//                                 role: ASSISTANT,
//                                 attachment: parse_data.attachment?.attachment,
//                                 file_urls: parse_data?.file_urls ? parse_data?.file_urls : []
//                             },
//                         })
//                     );
    
//                     if (!is_stream && parse_data.is_completed) {
//                         // 所有节点处理完毕，解析 Promise
//                         wsRef.current.removeEventListener('message', messageHandler);
//                         resolve({ accumulated_assistant_message });
//                     }
//                 } catch (error) {
//                     console.error("处理WebSocket消息时出错:", error);
//                     wsRef.current.removeEventListener('message', messageHandler);
//                     reject(error);
//                 }
//             };
    
//             wsRef.current.addEventListener('message', messageHandler);
//         });
//     }, [content, conversation_id, dispatch, fileUrls, querySelectedDocIds, user, workflow_name, wsStatus]);

//     const handleSendMessage = useCallback(async () => {
//         if (isUploading) return;
//         if (wsStatus !== 'connected') {
//             toast.error("正在尝试连接服务器。请稍后重试。");
//             connectWebSocket();
//             return;
//         }
    
//         const ai_message_id = uuid();
//         const message = {
//             ai_message: false,
//             content,
//             id: uuid(),
//             animate: false,
//             workflow_name: workflow_name,
//             session_id: conversation_id,
//             file_urls: fileUrls,
//         };
    
//         if (content.length > 0 || files.length > 0) {
//             setIsUploading(true);
//             try {
//                 const nodes = await fetchKbData();
//                 await processMessage(ai_message_id, message, nodes);
//             } catch (error) {
//                 console.error("处理消息时出错:", error);
//                 toast.error("发送消息时发生错误。请重试。");
//             } finally {
//                 setFileUrls([]);
//                 setFiles([]);
//                 setContent("");
//                 setIsUploading(false);
//             }
//         }
//     }, [isUploading, content, files, workflow_name, conversation_id, fileUrls, fetchKbData, processMessage, wsStatus, connectWebSocket]);
    

//     const handleFileSelectSuccess = useCallback((newFiles, newFileUrls) => {
//         setIsUploading(false);
//         const filteredFiles = [];
//         const filteredFileUrls = [];
//         const duplicateFileNames = [];
//         newFiles.forEach((newFile, index) => {
//             if (files.some(file => file.name === newFile.name)) {
//                 duplicateFileNames.push(newFile.name);
//             } else {
//                 filteredFiles.push(newFile);
//                 filteredFileUrls.push(newFileUrls[index]);
//             }
//         });

//         if (duplicateFileNames.length > 0) {
//             toast.warn(`The following files have already been uploaded: ${duplicateFileNames.join(", ")}`);
//         }
//         setFiles(prevFiles => [...prevFiles, ...filteredFiles]);
//         setFileUrls(prevFileUrls => [...prevFileUrls, ...filteredFileUrls]);
//     }, [files]);

//     const handleDeleteFile = useCallback((fileName) => {
//         setFiles(prevFiles => prevFiles.filter(file => file.name !== fileName));
//         setFileUrls(prevFileUrls => prevFileUrls.filter(fileUrl => fileUrl.filename !== fileName));
//     }, []);

//     const handleKeyPressed = useCallback((e) => {
//         if (isUploading) return;
//         if (e.key === 'Enter' && !e.shiftKey) {
//             e.preventDefault();
//             handleSendMessage();
//         }
//     }, [isUploading, handleSendMessage]);

//     const handleDrop = useCallback((event) => {
//         event.preventDefault();
//         const files = Array.from(event.dataTransfer.files);
//         uploadFiles(
//             files, handleFileSelectSuccess, (error) => {
//                 console.error("File upload error:", error);
//                 toast.error("Failed to upload files. Please try again.");
//             },
//             workflow_name, conversation_id, user, setIsUploading
//         );
//     }, [handleFileSelectSuccess, workflow_name, conversation_id, user]);

//     const handleClearSelectedDocIds = useCallback(() => {
//         dispatch(clearSelectedDocId());
//     }, [dispatch]);

//     return (
//         <div className="new_message_input_container" 
//             onDragOver={(e) => e.preventDefault()}
//             onDrop={handleDrop} 
//             style={styles.container}
//         >
//             {selected_doc_ids.length > 0 && (
//                 <div className="selected-doc-ids-warning">
//                     <MdOutlineSecurityUpdateWarning className="warning-icon" />
//                     <span className="warning-text">
//                         Selected articles: {querySelectedDocIds.map(doc => doc.title).join(", ")}
//                     </span>
//                     <IoIosCloseCircleOutline className="close-icon" onClick={handleClearSelectedDocIds} />
//                 </div>
//             )}
//             <ToastContainer 
//                 position="top-right" 
//                 autoClose={5000} 
//                 hideProgressBar={false}
//                 closeOnClick 
//                 pauseOnHover 
//                 draggable
//             />
//             <div className="uploaded-files-info">
//                 {files.map(file => (
//                     <div key={file.name} className="uploaded-file-info">
//                         {getFileIcon(file.name)}
//                         <div className="file-name-container">
//                             <span className="file-name" title={file.name}>
//                                 {file.name.length > 6 ? `${file.name.substring(0, 6)}...` : file.name}
//                             </span>
//                             <div className="file-name-tooltip">{file.name}</div>
//                         </div>    
//                         <IoMdCloseCircle 
//                             className="delete-file-icon" 
//                             style={styles.deleteFileIcon}
//                             onClick={() => handleDeleteFile(file.name)} 
//                         />
//                     </div>
//                 ))}
//             </div>
//             <div className="new_message_input_inner" style={styles.inner}>
//                 <textarea
//                     ref={inputRef}
//                     className="new_message_input"
//                     placeholder='Send a message ...'
//                     value={content}
//                     onChange={(e) => setContent(e.target.value)}
//                     onKeyDown={handleKeyPressed}
//                     style={styles.input}
//                 />
//                 <FileUpload 
//                     setIsUploading={setIsUploading}
//                     isUploading={isUploading}
//                     onFileSelectSuccess={handleFileSelectSuccess} 
//                     onFileSelectError={(error) => {
//                         console.error("File selection error:", error);
//                         toast.error("Failed to select files. Please try again.");
//                     }} 
//                     WorkflowName={workflow_name} 
//                     conversationId={conversation_id} 
//                     user={user}
//                 />
//                 <div
//                     className='new_message_icon_container' 
//                     onClick={handleSendMessage}
//                     style={{
//                         ...styles.iconContainer,
//                         opacity: wsStatus !== 'connected' ? 0.5 : 1,
//                         cursor: wsStatus !== 'connected' ? 'not-allowed' : 'pointer'
//                     }}
//                     disabled={wsStatus !== 'connected' || isUploading}
//                 >
//                     <BsSend color={wsStatus !== 'connected' ? 'grey' : 'inherit'} />
//                 </div>
//             </div>
//             {wsStatus !== 'connected' && (
//                 <div style={{ color: 'red', marginTop: '10px' }}>
//                     {wsStatus === 'error' ? 'Connection error. Retrying...' : 'Connecting to server...'}
//                 </div>
//             )}
//         </div>
//     );
// };

// export default NewMessageInput;