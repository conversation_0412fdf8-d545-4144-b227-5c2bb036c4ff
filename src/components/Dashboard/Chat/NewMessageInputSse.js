// import React, { useState, useEffect, useRef } from 'react';
// import { useSelector, useDispatch } from 'react-redux';
// import { v4 as uuid } from 'uuid';
// import { BsSend } from 'react-icons/bs';
// import { IoMdCloseCircle, IoIosCloseCircleOutline } from "react-icons/io";
// import { MdOutlineSecurityUpdateWarning } from "react-icons/md";
// import { ToastContainer, toast } from 'react-toastify';
// import 'react-toastify/dist/ReactToastify.css';
// import LoadingSpinner from '../loadingSpinner';
// import ExampleCard from './ExampleCard';
// import { setSelectedConversationId, addMessage, setSelectedMessageIndex, clearSelectedDocs, setIsChat } from '../dashboardSlice';
// import { AGENT_SSE_LLM_REQUEST_ENDPOINT, USER, MANAGER_API_BASE_URL, FILE_MANAGER_ENDPOINT, AGENT_SSE_LLM_UPLOAD_DATA_ENDPOINT } from '../../Configs/Config';
// import { fetchKbData } from '../../utils/fetchKbData';
// import FileUpload from './Upload';
// import { getFileIcon } from './utils/utils';
// import { fetchData } from '../../Routers/Router';
// import './ExampleCard.css';
// import {
//     useDynamicTheme,
//     newMessageInputStyleFunction,
//     newMessageInputContainerStyleFunction,
//     newMessageInputInnerStyleFunction,
//     newMessageIconContainerStyleFunction,
//     deleteFileIconStyleFunction,
//     exampleCardsContainerStyleFunction,
// } from '../../Configs/StyleConfigs';

// const NewMessageInput = () => {
//     const dispatch = useDispatch();
//     const { background_color, text_color, iconHover_color } = useDynamicTheme();
//     const styles = {
//         container: newMessageInputContainerStyleFunction(background_color, text_color),
//         input: newMessageInputStyleFunction(background_color, text_color),
//         deleteFileIcon: deleteFileIconStyleFunction(background_color, text_color),
//         inner: newMessageInputInnerStyleFunction(background_color, text_color),
//         iconContainer: newMessageIconContainerStyleFunction(background_color, text_color, iconHover_color),
//         exampleCardsContainer: exampleCardsContainerStyleFunction(background_color, text_color),
//     };

//     const { selected_conversation_id, conversations, selected_docs } = useSelector((state) => state.dashboard);
//     const { user_info } = useSelector((state) => state.user);
//     const { selected_workflow_id, workflows } = useSelector((state) => state.workflow);

//     const [querySelectedDocs, setQuerySelectedDocs] = useState(selected_docs);
//     const [files, setFiles] = useState([]);
//     const [content, setContent] = useState("");
//     const [fileUrls, setFileUrls] = useState([]);
//     const [isUploading, setIsUploading] = useState(false);
//     const [sseStatus, setSseStatus] = useState('disconnected');

//     const user = user_info.username;
//     const selected_workflow = selected_workflow_id ? workflows.find(item => item.id === selected_workflow_id) : null;
//     const workflow_name = selected_workflow ? selected_workflow.name : null;
//     const conversation_id = selected_conversation_id === 'new' ? uuid() : selected_conversation_id;

//     const inputRef = useRef(null);
//     const eventSourceRef = useRef(null);

//     useEffect(() => {
//         const uniqueDocsMap = new Map(selected_docs.map(doc => [doc.doc_id, doc]));
//         const uniqueDocs = Array.from(uniqueDocsMap.values());
//         if (JSON.stringify(uniqueDocs) !== JSON.stringify(querySelectedDocs)) {
//             setQuerySelectedDocs(uniqueDocs);
//         }
//     }, [selected_docs]);

//     useEffect(() => {
//         const selected_conversation = conversations.find(c => c.id === selected_conversation_id);
//         const new_message_index = selected_conversation && selected_conversation.messages.length > 0
//             ? selected_conversation.messages.length - 1
//             : null;
//         dispatch(setSelectedMessageIndex(new_message_index));
//     }, [selected_conversation_id, conversations, dispatch]);

//     useEffect(() => {
//         if (selected_conversation_id !== 'new') {
//             dispatch(clearSelectedDocs());
//         }
//     }, [selected_conversation_id, dispatch]);

//     useEffect(() => {
//         if (eventSourceRef.current) {
//             eventSourceRef.current.close();
//         }
//     }, []);

//     const connectSSE = (sessionId) => {
//         console.log(sessionId,!eventSourceRef.current,'connected');
//         if (sessionId) {
//             const endpoint = `${AGENT_SSE_LLM_REQUEST_ENDPOINT}/?session_id=${sessionId}`
//             console.log(endpoint,'connect');
//             eventSourceRef.current = new EventSource(endpoint);

//             eventSourceRef.current.onopen = () => {
//                 console.log("SSE connection established.");
//                 setSseStatus('connected');
//             };

//             eventSourceRef.current.onerror = (error) => {
//                 console.error("SSE error:", error);
//                 setSseStatus('error');
//                 eventSourceRef.current.close();
//                 setTimeout(() => connectSSE(sessionId), 5000);
//             };

//             eventSourceRef.current.onmessage = (event) => {
//                 const data = JSON.parse(event.data);
//                 handleSSEMessage(data);
//             };
//         }
//     };

//     const handleSSEMessage = (data) => {
//         if (data.is_stream && !data.is_completed) {
//             dispatch(
//                 addMessage({
//                     conversation_id,
//                     message: {
//                         content: data.content[0].content,
//                         ai_message: data.ai_message,
//                         session_id: conversation_id,
//                         user: user,
//                         workflow_name: workflow_name,
//                         id: data.id,
//                         role: data.role,
//                         agent_name: data.agent_name,
//                         attachment: data.attachment,
//                         token: data.token,
//                         file_urls: data.file_urls || []
//                     },
//                 })
//             );
//         }

//         if (!data.is_stream && data.is_completed) {
//             dispatch(setIsChat({isChat: false}));
//         }
//     };

//     const uploadData = async (attachments, nodes) => {
//         const queryObject = {
//             id: uuid(),
//             content: content,
//             session_id: conversation_id,
//             user: user,
//             workflow_name: workflow_name,
//             selected_workflow: selected_workflow,
//             role: USER,
//             ai_message: false,
//             nodes: nodes,
//             attachments: attachments,
//             manager_api_base_url: MANAGER_API_BASE_URL,
//         };

//         try {
//             const endpoint = `${AGENT_SSE_LLM_UPLOAD_DATA_ENDPOINT}`
//             console.log(endpoint);
//             const response = await fetch(endpoint, {
//                 method: 'POST',
//                 headers: {
//                     'Content-Type': 'application/json',
//                 },
//                 body: JSON.stringify(queryObject),
//             });
//             await response.json();
//             return conversation_id;
//         } catch (error) {
//             console.error('Failed to upload data:', error);
//             toast.error("Data upload failed, please try again.");
//             return null;
//         }
//     };

//     const processMessage = async () => {
//         // if (sseStatus !== 'connected') {
//         //     toast.error("正在连接服务器。请稍后重试。");
//         //     return;
//         // }

//         setSseStatus('connecting');
//         const attachments = await Promise.all(
//             querySelectedDocs.map(async (doc) => {
//                 try {
//                     const endpoint_api = `${FILE_MANAGER_ENDPOINT}/${doc.id}`;
//                     const attachment = await fetchData(endpoint_api);
//                     return attachment;
//                 } catch (error) {
//                     console.error(`Failed to fetch attachment for doc_id: ${doc.id}`, error);
//                     toast.error(`Failed to fetch attachment for doc_id: ${doc.id}`);
//                     return null;
//                 }
//             })
//         );
//         const validAttachments = attachments.filter(attachment => attachment !== null);
//         const nodes = await fetchKbData(selected_workflow);

//         const sessionId = await uploadData(validAttachments, nodes);
//         connectSSE(sessionId);
//     };

//     const handleSendMessage = async () => {
//         if (isUploading) return;
//         dispatch(setSelectedConversationId(conversation_id));
//         await processMessage();
//         setQuerySelectedDocs([]);
//         dispatch(clearSelectedDocs());
//         setContent("");
//         setFiles([]);
//     };

//     const handleExampleClick = (example) => {
//         if (example.conversable) {
//             setContent(example.text);
//             setTimeout(() => {
//                 if (inputRef.current) {
//                     inputRef.current.focus();
//                     inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);
//                 }
//             }, 0);
//         } else {
//             toast.info(example.text, {
//                 position: "top-right",
//                 autoClose: 3000,
//                 hideProgressBar: false,
//                 closeOnClick: true,
//                 pauseOnHover: true,
//                 draggable: true,
//             });
//         }
//     };

//     const handleFileSelectSuccess = (newFiles, newFileUrls) => {
//         setIsUploading(false);
//         const filteredFiles = [];
//         const filteredFileUrls = [];
//         const duplicateFileNames = [];
//         newFiles.forEach((newFile, index) => {
//             if (files.some(file => file.name === newFile.name)) {
//                 duplicateFileNames.push(newFile.name);
//             } else {
//                 filteredFiles.push(newFile);
//                 filteredFileUrls.push(newFileUrls[index]);
//             }
//         });

//         if (duplicateFileNames.length > 0) {
//             toast.warn(`The following files have already been uploaded: ${duplicateFileNames.join(", ")}`);
//         }
//         setFiles(prevFiles => [...prevFiles, ...filteredFiles]);
//         setFileUrls(prevFileUrls => [...prevFileUrls, ...filteredFileUrls]);
//     };

//     const handleDeleteFile = (fileName) => {
//         setFiles(prevFiles => prevFiles.filter(file => file.name !== fileName));
//         setFileUrls(prevFileUrls => prevFileUrls.filter(fileUrl => fileUrl.filename !== fileName));
//     };

//     const handleKeyPressed = (e) => {
//         if (isUploading) return;
//         if (e.key === 'Enter' && !e.shiftKey) {
//             e.preventDefault();
//             handleSendMessage();
//         }
//     };

//     const handleClearSelectedDocs = () => {
//         dispatch(clearSelectedDocs());
//     };

//     return (
//         <>
//             {/* {sseStatus !== 'connected' && <LoadingSpinner />} */}
//             {selected_conversation_id === 'new' && selected_workflow?.examples?.length > 0 && (
//                 <div className="example-cards-container" style={styles.exampleCardsContainer}>
//                     {selected_workflow.examples.slice(0, 4).map((example, index) => (
//                         <ExampleCard
//                             key={index}
//                             example={example.text}
//                             onClick={() => handleExampleClick(example)}
//                         />
//                     ))}
//                 </div>
//             )}
//             <div className="new_message_input_container" style={styles.container}>
//                 {selected_docs.length > 0 && (
//                     <div className="selected-doc-ids-warning">
//                         <MdOutlineSecurityUpdateWarning className="warning-icon" />
//                         <span className="warning-text">
//                             Selected articles: {querySelectedDocs.map(doc => doc.title).join(", ")}
//                         </span>
//                         <IoIosCloseCircleOutline className="close-icon" onClick={handleClearSelectedDocs} />
//                     </div>
//                 )}
//                 <ToastContainer />
//                 <div className="uploaded-files-info">
//                     {files.map(file => (
//                         <div key={file.name} className="uploaded-file-info">
//                             {getFileIcon(file.name)}
//                             <div className="file-name-container">
//                                 <span className="file-name" title={file.name}>
//                                     {file.name.length > 6 ? `${file.name.substring(0, 6)}...` : file.name}
//                                 </span>
//                                 <div className="file-name-tooltip">{file.name}</div>
//                             </div>
//                             <IoMdCloseCircle
//                                 className="delete-file-icon"
//                                 style={styles.deleteFileIcon}
//                                 onClick={() => handleDeleteFile(file.name)}
//                             />
//                         </div>
//                     ))}
//                 </div>
//                 <div className="new_message_input_inner" style={styles.inner}>
//                     <textarea
//                         ref={inputRef}
//                         className="new_message_input"
//                         placeholder='点击上方示例或输入文本'
//                         value={content}
//                         onChange={(e) => setContent(e.target.value)}
//                         onKeyDown={handleKeyPressed}
//                         style={styles.input}
//                     />
//                     <FileUpload
//                         setIsUploading={setIsUploading}
//                         isUploading={isUploading}
//                         onFileSelectSuccess={handleFileSelectSuccess}
//                         onFileSelectError={(error) => {
//                             console.error("File selection error:", error);
//                             toast.error("Failed to select files. Please try again.");
//                         }}
//                         WorkflowName={workflow_name}
//                         conversationId={conversation_id}
//                         user={user}
//                     />
//                     <div
//                         className='new_message_icon_container'
//                         onClick={handleSendMessage}
//                         style={{
//                             ...styles.iconContainer,
//                             opacity: sseStatus !== 'connected' ? 0.5 : 1,
//                             cursor: sseStatus !== 'connected' ? 'not-allowed' : 'pointer'
//                         }}
//                         disabled={sseStatus !== 'connected' || isUploading}
//                     >
//                         <BsSend color={sseStatus !== 'connected' ? 'grey' : 'inherit'} />
//                     </div>
//                 </div>
//                 {sseStatus !== 'connected' && (
//                     <div style={{ color: 'red', marginTop: '10px' }}>
//                         {sseStatus === 'error' ? 'Connection error. Retrying...' : 'Connecting to server...'}
//                     </div>
//                 )}
//             </div>
//         </>
//     );
// };

// export default NewMessageInput;
