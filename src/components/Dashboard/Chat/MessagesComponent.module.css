/* MessagesComponent.module.css */
.tabButtonsContainer {
    position: absolute;
    top: 10px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.tabButton {
    border: 1px solid #dcdcdc;
    border-radius: 16px;
    padding: 5px 15px;
    cursor: pointer;
    color: #666;
    background-color: #f9f9f9;
    transition: background-color 0.3s, color 0.3s;
}

.tabButton.active {
    background-color: #e4e4e4;
    color: #000;
}

.tabButton:hover {
    background-color: #d3d3d3;
}

.workflowCardsWrapper {
    position: relative;
    margin: 20px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.workflowCardsContainer {
    display: flex;
    justify-content: center;
    align-items: center;
}

.chatMessagesContainer {
    display: flex;
    flex-direction: column;
    padding: 10px;
    background-color: #f2f2f2;
    border-radius: 8px;
}
