import React, { useState, useMemo, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Set the PDF.js worker source
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

const PDFViewer = ({ file_url, page_id, initialScale = 0.9 }) => {  // 接收 initialScale 作为初始缩放比例
  const [numPages, setNumPages] = useState(null);
  const [page_number, setPageNumber] = useState(1);
  const [scale, setScale] = useState(initialScale);  // 使用传入的 initialScale 作为默认值

  const options = useMemo(() => ({
    cMapUrl: 'cmaps/',
    cMapPacked: true,
  }), []);

  useEffect(() => {
    if (page_id && page_id > 0) {
      setPageNumber(page_id);
    } else {
      setPageNumber(1);
    }
  }, [page_id]);

  function onDocumentLoadSuccess({ numPages }) {
    setNumPages(numPages);
    setPageNumber(page_id ? page_id : 1);
  }

  function changePage(offset) {
    setPageNumber(prevPageNumber => Math.max(1, Math.min(prevPageNumber + offset, numPages)));
  }

  function previousPage() {
    changePage(-1);
  }

  function nextPage() {
    changePage(1);
  }

  const zoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.1, 3.0));
  };

  const zoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.1, 0.5));
  };

  return (
    <div className="pdf-viewer-container">
      <div className="pdf-viewer">
        <Document
          file={file_url}
          onLoadSuccess={onDocumentLoadSuccess}
          options={options}
          renderMode="canvas"
          key={file_url}  // 这确保文件变化时才重新渲染 Document
        >
          <Page pageNumber={page_number} 
            renderTextLayer={false}         // 不渲染文本层
            renderAnnotationLayer={false}   // 不渲染注解层
            scale={scale}
          />
        </Document>
        <div className="navigation-pdf">
          <button onClick={previousPage} disabled={page_number <= 1}>
            Prev
          </button>
          <span>
            Page {page_number} of {numPages || '--'}
          </span>
          <button onClick={nextPage} disabled={page_number >= (numPages || 0)}>
            Next
          </button>
          <button onClick={zoomIn} disabled={scale >= 3.0}>+</button>
          <button onClick={zoomOut} disabled={scale <= 0.5}>-</button>
        </div>
      </div>
    </div>
  );
};

export default PDFViewer;
