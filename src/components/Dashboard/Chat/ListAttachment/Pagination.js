import React from 'react';

const Pagination = ({ current_page, setCurrentPage, page_size, total_documents }) => {
    const total_pages = Math.ceil(total_documents / page_size);

    return (
        <div className="pagination">
            <button
                disabled={current_page === 1}
                onClick={() => setCurrentPage(current_page - 1)}
            >
                Prev
            </button>
            <span>
                Page {current_page} of {total_pages}
            </span>
            <button
                disabled={current_page >= total_pages}
                onClick={() => setCurrentPage(current_page + 1)}
            >
                Next
            </button>
        </div>
    );
};

export default Pagination;
