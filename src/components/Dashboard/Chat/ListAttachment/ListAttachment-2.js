import React, { useState, useCallback, useEffect } from "react";
import Pagination from './Pagination'; 
import { useSelector, useDispatch } from 'react-redux';
import ReadOnlyQuill from '../utils/ReadOnlyQuill'; 
import FileViewer from './FileViewer';
import './ListAttachment.css'; // Assume CSS is defined here
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { updateSelectedDocs } from '../../dashboardSlice';

const ListAttachment = () => {
  const { selected_conversation_id, conversations, selected_message_index, selected_doc_ids } = useSelector(
    (state) => state.dashboard
);
  const dispatch = useDispatch();
  const [documents, setDocuments] = useState([]);
  const [is_open, setIsOpen] = useState(false); // State to control sidebar visibility
  const [current_page, setCurrentPage] = useState(1);
  const page_size = 1;
  const [sidebar_width, setSidebarWidth] = useState(500); // Default width of the sidebar

  const sidebar_header_class = is_open ? "sidebar_header" : "sidebar_header vertical-text";

  useEffect(() => {
    const handleKeyDown = (event) => {
      // Check if the ESC key was pressed
      if (event.keyCode === 27) {
        setIsOpen(false);
      }
    };
    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Remove event listener on cleanup
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []); // Empty array ensures effect runs only once

  useEffect(() => {
    setCurrentPage(1);
  }, [selected_message_index]);

  useEffect(() => {
    const getAttachments = () => {
      let message_attachment = [];

      if (conversations && selected_conversation_id) {
        const selected_conversation = conversations.find((c) => c.id === selected_conversation_id);
        if (selected_conversation && selected_conversation.messages && selected_conversation.messages.length > 0) {
          const target_message_index = selected_message_index !== undefined && selected_message_index >= 0
          ? selected_message_index
          : selected_conversation.messages.length - 1;
          
          if (target_message_index < selected_conversation.messages.length) {
            const target_message = selected_conversation.messages[target_message_index];
            message_attachment = target_message?.attachment || [];
          }
        }
      }
      const new_attachments = message_attachment.map(item => ({
        id: item.uuid,
        title: item.title,
        content: item.content,
        score: item.rerank_score.toFixed(2),
        file_url:item.file_url,
        page_id:item.page_id,
        data_id:item.data_id,
        doc_id:item.doc_id,
        is_selected: selected_doc_ids.some(doc => doc.id === item.uuid),
        show_full_content: false
      }));
      setDocuments(new_attachments);
    };

    getAttachments();
  }, [conversations, selected_conversation_id,selected_message_index,selected_doc_ids]);

  const toggleSidebar = () => {
    if (documents.length === 0) {
      toast.error("该消息无证据附件!!!!", {
        position: "top-center",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } else {
      setIsOpen(!is_open);
    }
  };

  const handleSelect = (selected_id,selected_doc_id,selected_doc_title) => {
    setDocuments(docs =>
      docs.map(doc =>
        doc.id === selected_id ? { ...doc, is_selected: !doc.is_selected } : doc
      )
    );
    dispatch(updateSelectedDocs({selected_id, selected_doc_id,selected_doc_title}));
  };

  const handleTitleClick = (doc) => {
    setDocuments(docs =>
      docs.map(d =>
        d.id === doc.id ? { ...d, show_full_content: !d.show_full_content } : d
      )
    );
  };
  
  useEffect(() => {
    const expanded_doc = documents.find(doc => doc.showFullContent);
    if (expanded_doc) {
      const expanded_doc_element = document.getElementById(`doc-${expanded_doc.id}`);
      if (expanded_doc_element) {
        expanded_doc_element.scrollIntoView({ behavior: "smooth", block: "nearest" });
      }
    }
  }, [documents]);

  useEffect(() => {
    if (documents.length===0){
      setIsOpen(false);
    }

  }, [documents]);

  const start_index = (current_page - 1) * page_size;
  const end_index = start_index + page_size;
  const paginated_documents = documents.slice(start_index, end_index);

  return (
    <div className={`attachment_sidebar ${is_open ? "open" : ""}`} style={{ width: `${sidebar_width}px` }}>
    <div className={sidebar_header_class} onClick={toggleSidebar}>
      参考资料
    </div>
    {is_open && (
      <>
        <div className="sidebar_content">
          {paginated_documents.map((doc) => (
            <div key={doc.id} className="document_card" id={`doc-${doc.id}`}>
              <div className="document_header">
                <input
                  type="checkbox"
                  id={`checkbox-${doc.id}`}
                  checked={doc.is_selected}
                  onChange={() => handleSelect(doc.id,doc.doc_id,doc.title)}
                  className="attachment-checkbox"
                />
                <span
                  role="button"
                  tabIndex={0}
                  className="document_title"
                  onClick={() => handleTitleClick(doc)}
                >
                  {doc.title}
                </span>
                <span className="document_score">分数: {doc.score}</span>
              </div>
              {doc.show_full_content ? (
                <FileViewer file_url={doc.file_url} page_id={doc.page_id} />
              ) : (
                <div className="document_content">
                  <ReadOnlyQuill 
                  content={doc.content} 
                  />
                  </div>
              )}
            </div>
          ))}
        </div>
        {documents.length > 0 && (
          <Pagination
            currentPage={current_page}
            totalDocuments={documents.length}
            pageSize={page_size}
            setCurrentPage={page => setCurrentPage(page)}
          />
        )}
      </>
    )}
  </div>
);
};


export default ListAttachment;