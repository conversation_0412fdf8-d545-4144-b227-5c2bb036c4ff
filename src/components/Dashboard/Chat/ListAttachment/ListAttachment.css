.attachment_sidebar {
  width: 250px;
  position: fixed;
  right: 28px;
  top: 50%;
  transform: translateY(-50%) translateX(100%);
  transition: transform 0.3s ease, width 0.3s ease;
  background-color: #f4f4f4;
  box-shadow: -2px 0 5px rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}
  
.attachment_sidebar.open {
    transform: translateY(-50%) translateX(0);  /* Move into view */
    width: 500px; /* Increased width when open */
    height: 75.7vh; /* Increased height to 80% of the viewport height */
    top: 45%;
  }
  
/* Base styles for sidebar header */
.sidebar_header {
  padding: 10px;  /* Padding to provide spacing around text */
  width: 100%;  /* Full width to fill the sidebar */
  background-color: #007bff;  /* Consistent background color */
  color: white;
  display: flex;
  align-items: center;  /* Vertically center the content */
  justify-content: center;  /* Horizontally center the content */
  cursor: pointer;
  transition: transform 0.3s ease, background-color 0.3s ease;  /* Smooth transition for transformations and color */
  height: 10px;  /* Initial fixed height */
}

.sidebar_header.vertical-text {
  display: flex;  /* 使用弹性盒布局 */
  writing-mode: vertical-lr;  /* Vertical text orientation */
  justify-content: center;  /* Align text to the start of the flex container */
  align-items: center;  /* Center the text vertically */
  height: 70px;  /* Full height to cover the sidebar area */
  padding-left: 1px; /* Add left padding to align the text to the left */
  width:28px;
  font-size:17px;
  font-weight: bold;
}

/* Styles when sidebar is open */
.attachment_sidebar.open .sidebar_header {
  text-align: center;  /* Horizontally center the text */
  height: 10px;  /* Maintain fixed height */
  width: auto;  /* Auto width to fit content */
  justify-content: center;  /* Center content horizontally */
  transform: none;  /* No rotation needed */
  align-items: center;  /* Center content vertically */
}

.sidebar_content {
    overflow-y: auto;
    max-height: calc(100% - 40px); /* Subtract header height */
    flex-grow: 1;
    padding: 10px;
    background-color: white;
    color: black;
}

  .document_card {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    max-height: 90%;  /* Each card can take up to 90% of the sidebar_content's height */
}
  
  .document_header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .document_header .attachment-checkbox {
    position: relative;
    z-index: 1; /* Ensure the checkbox is on top of other elements */
  }
  
  .document_header .attachment-checkbox-label {
    position: relative;
    z-index: 1; /* Ensure the label is on top of other elements */
    margin-left: 5px;
  }
  .document_title {
    font-weight: bold;
    margin-left: 5px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .document_score {
    margin-left: 10px;
  }
  
  .document_content {
    overflow-y: auto;  /* Allow scrolling within the document content */
    line-height: 1.8;
    word-break: break-word;
  }
  
  .document_content .ql-container {
    /* Overrides for Quill's container to fit better in your layout */
    border: none;
    padding: 0;
}

.document_content .ql-editor {
    /* Reduces default padding and ensures background matches */
    padding: 0;
    background-color: transparent;
}

  .show-full-content {
    max-height: none !important;
    overflow: visible !important;
  }
/*   
  .full-content-wrapper {
   margin-bottom: 10px; 
  } */
  
  .file-viewer {
    flex-grow: 1; /* Allow the viewer to expand fully within its container */
    overflow: auto; /* Enable scrollbars if content overflows */
    padding: 0px; /* Padding around the content */
  }

  .document_footer {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }
  .pagination {
    padding: 4px;
    background-color: #add8e6; /* Set to light blue */
    border-top: 1px solid #ccc; /* Adds a subtle top border for visual separation */
    text-align: right; /* Center align the pagination controls */
}
.pdf-viewer-container {
  display: flex; /* Enable flexbox to position children side by side */
  align-items: start; /* Align items at the start of the container vertically */
  height: 100%; /* Use all available height */
  background: #fff; /* Optional: background color for the viewer container */
}

.pdf-viewer {
  flex-grow: 1; /* Allow the viewer to take up as much space as possible */
  overflow: auto; /* Scrollbars appear if content is larger than the container */
  padding: 0px; /* Space around the content */
  min-width: 0; /* Ensures flexbox shrink/grow works as expected */
}

.navigation-pdf {
  display: flex;
  flex-direction: row; /* Aligns navigation buttons horizontally */
  justify-content: center; /* Centers the navigation buttons horizontally within the navigation */
  align-items: center; /* Centers the navigation buttons vertically */
  padding: 4px; /* Padding around the navigation controls */
  position: absolute; /* Positions navigation relative to its nearest positioned ancestor */
  bottom: 0; /* Aligns the navigation bar at the bottom of its container */
  right: 0; /* Aligns the navigation bar to the right side of its container */
  background-color: #add8e6; /* Set to light blue */
  border-top: 1px solid #ccc; /* Adds a subtle top border for visual separation */
}

.navigation-pdf button,
.navigation-pdf span {
  margin: 0 px; /* Adds horizontal spacing between buttons and the page number */
}


.react-pdf__Page__canvas {
  max-width: 100%;
  height: auto;
  box-shadow: 0 0 0px rgba(0,0,0,0.5);
}

.handle {
  background-color: #007bff; /* Set the background color */
  color: #fff; /* Set the text color to white */
  padding: 10px; /* Add some padding */
  cursor: move; /* Change the cursor to indicate the element can be moved */
  user-select: none; /* Prevent text selection */
  display: flex; /* Use flexbox to center the content */
  justify-content: center; /* Center the content horizontally */
  align-items: center; /* Center the content vertically */
  font-weight: bold; /* Make the text bold */
  border-radius: 4px; /* Add some rounded corners */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Add a subtle drop shadow */
}

.handle:hover {
  background-color: #0056b3; /* Slightly darken the background on hover */
}

.handle:active {
  background-color: #004a99; /* Darken the background further when clicked */
}

.tooltip {
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
  white-space: nowrap;
}
