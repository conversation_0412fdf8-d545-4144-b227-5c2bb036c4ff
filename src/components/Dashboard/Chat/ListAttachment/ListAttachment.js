import React, { useState, useEffect } from "react";
import Pagination from './Pagination'; 
import { useSelector, useDispatch } from 'react-redux';
import ReadOnlyQuill from '../utils/ReadOnlyQuill'; 
import FileViewer from './FileViewer';
import './ListAttachment.css'; // Assume CSS is defined here
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { updateSelectedDocs } from '../../dashboardSlice';

const ListAttachment = ({ is_open: initialIsOpen = false, onToggle }) => {
  const { selected_conversation_id, conversations, selected_message_index, selected_docs, selected_conversation,isChat } = useSelector(
    (state) => state.dashboard
  );
  const dispatch = useDispatch();
  const [documents, setDocuments] = useState([]);
  const [is_open, setIsOpen] = useState(initialIsOpen); // Use prop as initial value
  const [current_page, setCurrentPage] = useState(1);
  const page_size = 1;
  const sidebar_width = 500;
  const sidebar_header_class = is_open ? "sidebar_header" : "sidebar_header vertical-text";

  useEffect(() => {
    setIsOpen(initialIsOpen);
  }, [initialIsOpen]);
  
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.keyCode === 27) {
        setIsOpen(false);
        if (onToggle) onToggle(false); // Notify parent component
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onToggle]);

  useEffect(() => {
    setCurrentPage(1);
  }, [selected_message_index]);

  useEffect(() => {
    const getAttachments = () => {
      let message_attachment = [];
      if (selected_conversation && selected_conversation.id === selected_conversation_id) {
        if (selected_conversation.messages && selected_conversation.messages.length > 0) {
          const target_message_index = selected_message_index !== undefined && selected_message_index >= 0
            ? selected_message_index
            : selected_conversation.messages.length - 1;

          if (target_message_index < selected_conversation.messages.length) {
            const target_message = selected_conversation.messages[target_message_index];
            message_attachment = target_message?.attachment || [];
          }
        }
      }
      const new_attachments = message_attachment.map(item => ({
        id: item.uuid,
        title: item.title,
        content: item.content,
        score: typeof item.rerank_score === 'number' ? item.rerank_score.toFixed(2) : 'N/A',  // 确保 score 是数字
        file_url: item.file_url,
        page_id: item.page_id,
        data_id: item.data_id,
        doc_id: item.doc_id,
        query: item.query,
        is_selected: selected_docs.some(doc => doc.id === item.uuid),
        show_full_content: false
      }));
      setDocuments(new_attachments);
    };

    getAttachments();
  }, [conversations, selected_conversation_id, selected_message_index, selected_docs,selected_conversation]);

  useEffect(() => {
    const expanded_doc = documents.find(doc => doc.showFullContent);
    if (expanded_doc) {
      const expanded_doc_element = document.getElementById(`doc-${expanded_doc.id}`);
      if (expanded_doc_element) {
        expanded_doc_element.scrollIntoView({ behavior: "smooth", block: "nearest" });
      }
    }
  }, [documents]);

  useEffect(() => {
    if (documents.length === 0) {
      setIsOpen(false);
      if (onToggle) onToggle(false); // Notify parent component
    }
  }, [documents, onToggle]);

  const toggleSidebar = () => {
    if (isChat) {
      // 如果 isChat 为 true，显示提示信息
      toast.warning("会话中，不能查看文献列表", {
        position: "top-center",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
      return; // 阻止继续执行
    }

    if (documents.length === 0) {
      // 如果文档为空，显示错误信息
      toast.error("该消息无附件", {
        position: "top-center",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } else {
      // 切换侧边栏状态
      const newState = !is_open;
      setIsOpen(newState);
      if (onToggle) onToggle(newState); // 通知父组件状态变化
    }
  };

  const handleSelect = (selected_id, selected_doc_id, selected_doc_title) => {
    setDocuments(docs =>
      docs.map(doc =>
        doc.id === selected_id ? { ...doc, is_selected: !doc.is_selected } : doc
      )
    );
    dispatch(updateSelectedDocs({ id:selected_id, doc_id:selected_doc_id, title:selected_doc_title }));
  };

  const handleTitleClick = (doc) => {
    setDocuments(docs =>
      docs.map(d =>
        d.id === doc.id ? { ...d, show_full_content: !d.show_full_content } : d
      )
    );
  };

  const start_index = (current_page - 1) * page_size;
  const end_index = start_index + page_size;
  const paginated_documents = documents.slice(start_index, end_index);

  return (
    <div className={`attachment_sidebar ${is_open ? "open" : ""}`} style={{ width: `${sidebar_width}px` }}>
      <div className={sidebar_header_class} onClick={toggleSidebar}>
        参考资料
      </div>
      {is_open && (
        <>
          <div className="sidebar_content">
            {paginated_documents.map((doc) => (
              <div key={doc.id} className="document_card" id={`doc-${doc.id}`}>
                <div className="document_query">
                  <strong>用户问题:</strong> {doc.query}
                </div>
                <div className="document_header">
                  <input
                    type="checkbox"
                    id={`checkbox-${doc.id}`}
                    checked={doc.is_selected}
                    onChange={() => handleSelect(doc.id, doc.doc_id, doc.title)}
                    className="attachment-checkbox"
                  />
                  <span
                    role="button"
                    tabIndex={0}
                    className="document_title"
                    onClick={() => handleTitleClick(doc)}
                  >
                    {doc.title}
                  </span>
                  <span className="document_score">分数: {doc.score}</span>
                </div>
                {doc.show_full_content ? (
                  <FileViewer file_url={doc.file_url} page_id={doc.page_id} />
                ) : (
                  <div className="document_content">
                    <ReadOnlyQuill content={doc.content} />
                  </div>
                )}
              </div>
            ))}
          </div>
          {documents.length > 0 && (
            <Pagination
              current_page={current_page}
              total_documents={documents.length}
              page_size={page_size}
              setCurrentPage={page => setCurrentPage(page)}
            />
          )}
        </>
      )}
    </div>
  );
};

export default ListAttachment;
