import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { setSelectedWorkflowId } from '../../Workflow/workflowSlice';
import { setSelectedAgentId } from '../../Workflow/Agents/Agents/AgentSlice';

const WorkflowCard = ({ workflow, isSelected, icon, tab }) => {
    const dispatch = useDispatch();
    const [isHovered, setIsHovered] = useState(false);

    // 根据当前的 tab，选择不同的 dispatch 动作
    const handleSelect = () => {
        if (tab === 'workflows') {
            dispatch(setSelectedWorkflowId(workflow.id));
        } else if (tab === 'agents') {
            dispatch(setSelectedAgentId(workflow.id));
        }
    };

    // 卡片样式，根据是否被选中进行设置
    const cardStyle = {
        border: isSelected ? '2px solid blue' : '1px solid gray',
        padding: '20px',
        margin: '10px',
        borderRadius: '10px',
        boxShadow: isSelected ? '0 4px 8px rgba(0, 0, 0, 0.2)' : '0 2px 4px rgba(0, 0, 0, 0.1)',
        cursor: 'pointer',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: '200px',
        textAlign: 'center',
        position: 'relative',
    };

    // 悬停提示信息的样式
    const hoverMessageStyle = {
        position: 'absolute',
        bottom: '10px',
        left: '50%',
        transform: 'translateX(-50%)',
        backgroundColor: 'rgba(0, 0, 0, 0.75)',
        color: 'white',
        padding: '5px 10px',
        borderRadius: '5px',
        pointerEvents: 'none',
        opacity: isHovered ? 1 : 0,
        transition: 'opacity 0.3s ease-in-out',
    };

    // 截断描述信息
    const truncateDescription = (description, maxLength) => {
        if (description.length > maxLength) {
            return description.substring(0, maxLength) + '...';
        }
        return description;
    };

    return (
        <div
            style={cardStyle}
            onClick={handleSelect}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            <div style={{ fontSize: '16px', marginBottom: '10px' }}>{icon}</div>
            <h4>{workflow.name}</h4>
            <p>
                描述: 
                <span>{truncateDescription(workflow.description, 40)}</span>
            </p>
            <div style={hoverMessageStyle}>
                {tab === 'workflows' ? '点击下方样例开始会话' : '点击以选择智能体'}
            </div>
        </div>
    );
};

export default WorkflowCard;
