import React from 'react';
import { exampleCardsStyleFunction,useDynamicTheme } from '../../Configs/StyleConfigs'; 

const ExampleCard = ({ example, onClick }) => {
    const { background_color, text_color,icon_hover_color } = useDynamicTheme();
    const exampleCardsStyle = exampleCardsStyleFunction(background_color,text_color,icon_hover_color);
    return (
        <div className="example-card" onClick={() => onClick(example)} style={exampleCardsStyle}>
            <p>{example}</p>
        </div>
    );
};

export default ExampleCard;
