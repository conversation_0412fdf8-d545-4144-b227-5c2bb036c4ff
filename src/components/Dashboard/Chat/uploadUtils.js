// uploadUtils.js
import axios from 'axios';
import { API_BASE_URL } from '../../Configs/Config';

export const uploadFiles = async (
  files, onUploadSuccess, onUploadError,WorkflowName,conversationId, user,setIsUploading
  // ,setIsDisabled
) => {
  const form_data = new FormData();
  const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token
  setIsUploading(true); // 设置上传状态为 true
  // setIsDisabled(true);
  files.forEach((file) => {
    form_data.append('files', file); // 'files' is the field name in the backend
  });
  form_data.append('workflow_name', WorkflowName); 
  form_data.append('session_id', conversationId); 
  form_data.append('user', user); 
  try {
    const response = await axios.post(`http://${API_BASE_URL}/uploadfiles/`, form_data, {
      headers: {
        'Content-Type': 'multipart/form-data',
         'Authorization': `Bearer ${token}`
      },
    });
    // Call the success handler with the uploaded file URLs
    setIsUploading(false); // 设置上传状态为 false
    onUploadSuccess(files, response.data.urls);
  } catch (error) {
    console.error('Error uploading files:', error);
    // Call the error handler
    onUploadError(error);
  }
};
export const shuffleArray = (array) => {
  return array.sort(() => 0.5 - Math.random());
};
