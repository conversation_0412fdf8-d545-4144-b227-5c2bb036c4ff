import React from 'react';
import { FaFilePdf, FaFileWord, FaFileExcel, FaFilePowerpoint } from 'react-icons/fa';
import { MdInsertDriveFile } from 'react-icons/md';
import { fileNameStyleFunction,  FileIconStyleFunction, FileIconSvgStyleFunction, useDynamicTheme, } from '../../Configs/StyleConfigs'
const getFileIcon = (fileName,FileIconSvgStyle) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
        case 'pdf': return <FaFilePdf className="file-icon-svg" style={FileIconSvgStyle}/>;
        case 'docx':
        case 'doc': return <FaFileWord className="file-icon-svg" style={FileIconSvgStyle}/>;
        case 'xlsx':
        case 'xls': return <FaFileExcel className="file-icon-svg" style={FileIconSvgStyle} />;
        case 'pptx':
        case 'ppt': return <FaFilePowerpoint className="file-icon-svg" style={FileIconSvgStyle}/>;
        default: return <MdInsertDriveFile className="file-icon-svg" style={FileIconSvgStyle}/>;
    }
};

const FileDisplay = ({ fileInfo, onImageClick }) => {
    const { background_color, text_color } = useDynamicTheme();
    const fileNameStyle = fileNameStyleFunction(background_color, text_color)
    const FileIconStyle = FileIconStyleFunction(background_color, text_color)
    const FileIconSvgStyle = FileIconSvgStyleFunction(background_color, text_color)
    const extension = fileInfo?.filename?.split('.').pop().toLowerCase();
    if (/^(jpg|jpeg|png|gif|bmp)$/i.test(extension)) {
        return (
            <img
                src={fileInfo.url}
                alt={fileInfo.filename}
                className="image-file"
                onClick={() => onImageClick(fileInfo.url)}
            />
        );
    } else if (/^(mp4|webm)$/i.test(extension)) {
        return (
            <video controls className="video-file" src={fileInfo.url}>
                Your browser does not support the video tag.
            </video>
        );
    } else if (/^(mp3|wav)$/i.test(extension)) {
        return (
            <audio controls className="audio-file" src={fileInfo.url}>
                Your browser does not support the audio element.
            </audio>
        );
    } else {
        return (
            <div className="file_display">
                <div className="file_icon" style={FileIconStyle}>{getFileIcon(fileInfo?.filename,FileIconSvgStyle)}</div>
                <div className="file_info">
                    <div className="file_name" style={fileNameStyle}>{fileInfo.filename}</div>
                </div>
            </div>
        );
    }
};

export default FileDisplay;
