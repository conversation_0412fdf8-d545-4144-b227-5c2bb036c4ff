/* src/components/NewMessageInput/NewMessageInput.module.css */

/* 容器样式 */
.container {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 10px;
    position: relative;
    background-color: #fff;
    transition: border-color 0.3s;
}

.dragActive {
    border-color: #40a9ff;
    background-color: #e6f7ff;
}

/* 选择文档警告 */
.selectedDocsWarning {
    display: flex;
    align-items: center;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 10px;
}

.warning-icon {
    color: #faad14;
    margin-right: 8px;
    font-size: 20px;
}

.warning-text {
    flex: 1;
    color: #8c8c8c;
}

.close-icon {
    color: #8c8c8c;
    cursor: pointer;
    font-size: 20px;
}

/* 已上传文件列表 */
.uploadedFilesContainer {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 10px;
}

.uploadedFile {
    display: flex;
    align-items: center;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px;
}

.fileIcon {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: #fff;
    font-size: 20px;
}

.fileInfo {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.fileName {
    font-weight: 500;
    color: #000;
    margin-bottom: 4px;
}

.fileType {
    font-size: 12px;
    color: #8c8c8c;
}

.fileProgress {
    margin-top: 4px;
}

.errorText {
    color: #f5222d;
    font-size: 12px;
    margin-top: 4px;
}

.closeButton {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #8c8c8c;
}

.iconXs {
    width: 16px;
    height: 16px;
}

/* 输入行 */
.inputLine {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 上传按钮 */
.uploadButton {
    font-size: 18px;
    color: #1890ff;
}

/* 自定义文本域 */
.customTextarea {
    flex: 1;
    resize: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s;
    max-height: 200px;
    overflow-y: auto;
}

.customTextarea:focus {
    border-color: #40a9ff;
}

/* 发送按钮 */
.sendButton {
    font-size: 18px;
    color: #1890ff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sendButton:disabled {
    color: #d9d9d9;
    cursor: not-allowed;
}

/* 上传文件信息（拖拽） */
.uploadedFilesInfo {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.uploadedFileInfo {
    display: flex;
    align-items: center;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px;
}

.fileNameContainer {
    display: flex;
    flex-direction: column;
    margin-left: 8px;
}

.fileName {
    font-weight: 500;
    color: #000;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
}

.fileNameTooltip {
    visibility: hidden;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%; /* Position above the text */
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
}

.fileNameContainer:hover .fileNameTooltip {
    visibility: visible;
    opacity: 1;
}

.deleteFileIcon {
    color: #ff4d4f;
    cursor: pointer;
    font-size: 18px;
    margin-left: 8px;
}

/* 示例卡片容器 */
.exampleCardsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.exampleCardsContainerText {
    font-size: 14px;
    color: #8c8c8c;
}

/* 内部容器 */
.inner {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 其他辅助类 */
@media (max-width: 600px) {
    .customTextarea {
        font-size: 12px;
    }

    .fileName {
        max-width: 80px;
    }
}
