import React,{useEffect} from 'react';
import Messages from './Messages';
import { useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import NewMessageInput  from './NewMessageInput';
import { 
    useDynamicTheme,
    chatContainerStyleFunction,
    chatGptLogoStyleFunction,
    chatGptLogoContainerStyleFunction,
} from '../../Configs/StyleConfigs';

const ChatLogo = () => {
    const { background_color, text_color } = useDynamicTheme();
    const chatGptLogoStyle = chatGptLogoStyleFunction(background_color, text_color)
    const chatGptLogoContainerStyle = chatGptLogoContainerStyleFunction(background_color, text_color)
    return (
        <div className='chat_gpt_logo_container' style={chatGptLogoContainerStyle}>
            <p className='chat_gpt_logo' style={chatGptLogoStyle}> Flint </p>
        </div>
    );
}
const Chat = () => {
    const { background_color, text_color } = useDynamicTheme();
    const chatContainerStyle = chatContainerStyleFunction(background_color, text_color);
    const selected_conversation_id = useSelector((state) => state.dashboard.selected_conversation_id);
    const navigate = useNavigate(); // 使用 useNavigate 进行导航
    const { conversationId } = useParams();
    useEffect(() => {
        if (selected_conversation_id && selected_conversation_id !== conversationId) {
            navigate(`/dashboard/conversation/${selected_conversation_id}`);
        }
    }, [selected_conversation_id, conversationId, navigate]);
    return (
        <div className="chat_container" style={chatContainerStyle}>
        {!selected_conversation_id ? ( 
            <ChatLogo /> ) : (
            <div className='chat_selected_container'>
                <Messages selected_conversation_id={selected_conversation_id}/>
                <NewMessageInput />
            </div>
            )}
    </div>
    );
}; 
export default Chat;