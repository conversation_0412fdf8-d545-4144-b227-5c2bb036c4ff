import React, { useState } from "react";

const useDynamicBackground = () => {
    const [is_night, setIsNight] = useState(false);

    React.useEffect(() => {
        const checkTime = () => {
            const hour = new Date().getHours();
            const night_time = hour >= 19 || hour < 8;
            setIsNight(night_time);
        };

        checkTime();  // Initial check
        const interval_id = setInterval(checkTime, 60 * 1000);  // Check every minute

        return () => clearInterval(interval_id);  // Clean up the interval on component unmount
    }, []);

    return is_night;
};

export default useDynamicBackground;