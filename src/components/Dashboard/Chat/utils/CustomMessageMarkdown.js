import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeHighlight from 'rehype-highlight';
import { Avatar, Typography, Collapse } from 'antd';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks'; // 添加remark-breaks插件
import { ChevronDown, ChevronUp } from 'lucide-react';

const { Text } = Typography;
const { Panel } = Collapse;

function convertUnixToTime(unixTimestamp) {
  if (!unixTimestamp) {
    unixTimestamp = Date.now();
  }
  const timestampInSeconds = unixTimestamp / 1000;
  const date = new Date(timestampInSeconds * 1000);
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  const hours = (date.getUTCHours() + 8).toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const seconds = date.getUTCSeconds().toString().padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function EnhancedMarkdownMessage({
  content,
  avatarUrl,
  userName,
  creationTimeUnix,
  function_call,
  background_color = 'rgb(43, 43, 43)',
  text_color = 'rgb(255, 255, 255)',
}) {
  const [hovered, setHovered] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true);

  const messageStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: '16px',
    backgroundColor: background_color,
    color: text_color,
    overflowY: 'auto',
    width: '98%',
  };

  const avatarContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '8px',
  };

  const avatarStyle = {
    backgroundColor: background_color,
    color: text_color,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    fontSize: '16px',
    fontWeight: 'bold',
    marginRight: '8px',
  };

  const userNameStyle = {
    fontSize: '18px',
    maxWidth: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    backgroundColor: background_color,
    color: text_color,
    fontWeight: 'bold',
  };

  const timeStyle = {
    fontSize: '12px',
    color: hovered ? '#000000' : text_color,
    fontWeight: hovered ? 'bold' : 'normal',
    marginLeft: hovered ? '12px' : '8px',
    cursor: 'pointer',
  };

  const contentStyle = {
    flex: 1,
    backgroundColor: background_color,
    color: text_color,
    padding: '10px',
    paddingLeft: '10px',
    borderRadius: '12px',
    overflowWrap: 'break-word',
    border: '1px solid #ccc',
    maxHeight: '100%',
    overflowY: 'auto',
    width: '98%'
  };

  const functionCallStyle = {
    border: '1px solid #ccc',
    borderRadius: '8px',
    padding: '10px',
    marginBottom: '10px',
    width: '100%',
    backgroundColor: background_color,
    color: text_color
  };
  const functionCallTextStyle = {
    color: text_color,
    backgroundColor: background_color,
    marginRight: '8px'
  };
  const collapseHeaderStyle = {
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    userSelect: 'none',
    backgroundColor: background_color,
    color: text_color
  };

  const collapsePanelStyle = {
    backgroundColor: background_color,
    color: text_color,
    borderRadius: '0 0 8px 8px',
  };

  const formattedTime = convertUnixToTime(creationTimeUnix);

  return (
    <div style={messageStyle}>
      <div className="message-avatar" style={avatarContainerStyle}>
        <Avatar src={avatarUrl} size="sm" style={avatarStyle}/>
        <Text style={userNameStyle}>{userName}</Text>
        <Text
          style={timeStyle}
          onMouseEnter={() => setHovered(true)}
          onMouseLeave={() => setHovered(false)}
        >
          {formattedTime}
        </Text>
      </div>
      <div className="message-content" style={contentStyle}>
        {function_call && (
          <div style={functionCallStyle}>
            <div
              style={collapseHeaderStyle}
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              <Text strong style={functionCallTextStyle}>工具搜索</Text>
              {isCollapsed ? <ChevronDown size={16} /> : <ChevronUp size={16} />}
            </div>
            <Collapse
              activeKey={isCollapsed ? [] : ['1']}
              ghost
            >
              <Panel key="1" showArrow={false} header={null}>
                <div style={collapsePanelStyle}>
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm, remarkBreaks]}
                    rehypePlugins={[rehypeHighlight]}
                    components={{
                      pre: ({ children }) => (
                        <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', margin: 0, padding: '10px' }}>
                          {children}
                        </pre>
                      ),
                    }}
                  >
                  {function_call}
                  </ReactMarkdown>
                </div>
              </Panel>
            </Collapse>
          </div>
        )}
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkBreaks]} // 添加remark-breaks插件
          rehypePlugins={[rehypeHighlight]}
          components={{
            p: ({ children }) => (
              <Text style={{ color: text_color, marginBottom: '8px', display: 'block' }}>
                {children}
              </Text>
            ),
            img: ({ src, alt }) => (
              <img src={src} alt={alt} style={{ maxWidth: '50%', maxHeight: '50%' }} />
            ),
          }}
        >
          {content}
        </ReactMarkdown>
      </div>
    </div>
  );
}

export default EnhancedMarkdownMessage;
