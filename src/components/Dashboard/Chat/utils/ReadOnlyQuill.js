import React from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; // Styles are important for proper display

const ReadOnlyQuill = ({ content }) => {
  // Define modules to disable all toolbar functionality
  const modules = {
    toolbar: false
  };

  // Define formats to constrain allowed formats and potentially reduce processing needs
  const formats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike', 'blockquote',
    'list', 'bullet', 'indent',
    'link', 'image', 'video'
  ];

  return (
    <ReactQuill 
      theme="snow" // You can use 'bubble' for a simpler appearance
      value={content}
      readOnly={true}
      modules={modules}
      formats={formats}
      style={{ fontSize: '20px' }} // Applying font size directly
    />
  );
};

export default ReadOnlyQuill;
