import { FaFilePdf, FaFileWord, FaFileExcel, FaFileImage } from "react-icons/fa";

export const getFileIcon = (fileName) => {
    console.log(fileName,'  {getFileIcon(file.url)}')
    const file_extension = fileName.split('.').pop().toLowerCase();
    switch (file_extension) {
        case 'pdf':
            return <FaFilePdf className="file-icon" />;
        case 'doc':
        case 'docx':
            return <FaFileWord className="file-icon" />;
        case 'xls':
        case 'xlsx':
            return <FaFileExcel className="file-icon" />;
        case 'png':
        case 'jpg':
        case 'jpeg':
        case 'gif':
            return <FaFileImage className="file-icon" />;
        default:
            return <FaFilePdf className="file-icon" />;
    }
};