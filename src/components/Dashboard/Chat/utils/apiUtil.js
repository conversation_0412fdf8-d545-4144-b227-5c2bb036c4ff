// apiUtils.js
import {
     MEMORY_INSERT_ENDPOINT, MEMORY_SELECT_ENDPOINT,
      MEMORY_DELETE_ENDPOINT,REGISTER_ENDPOINT,
      LOGIN_ENDPOINT,WORKFLOWS_USER_ENDPOINT,WORKFLOWS_UPDATE_ENDPOINT,
      NEW_WORKFLOW,WORKFLOWS_DATABASE_COUNT_ENDPOINT,USER_CONFORM_ENDPOINT,LOGIN_API_NAME
    } from '../../../Configs/Config'

export const insertSessionMessage = async (data) => {
    const url = `${MEMORY_INSERT_ENDPOINT}`;  // 使用从配置文件导入的基础 URL
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        const output_data = await response.json();
        console.log('Post response:', output_data);
        return output_data;
    } catch (error) {
        console.error('Failed to post data:', error);
        alert("flint Agent服务连接失败，联系后台管理员");
        // throw new Error(`Failed to send data: ${error.message}`);

    }
};

export const fetchSession = async (user, is_title=0, session_id = null) => {
    const url = `${MEMORY_SELECT_ENDPOINT}`;
    console.log("Sending data:", { session_id, user, is_title });

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: session_id,
                user: user,
                is_title: is_title
            })
        });

        if (!response.ok) {
            console.error("HTTP error! Response:", await response.text());
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Received data:", data);
        return data.data;
    } catch (error) {
        console.error("Failed to fetch session data:", error);
        // throw new Error(`Failed to fetch data: ${error.message}`);
        return []
    }
};

export const deleteSession = async (user, session_id = null) => {
    const url = `${MEMORY_DELETE_ENDPOINT}`;
    console.log("Sending data:", { session_id, user});

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: session_id,
                user: user
            })
        });

        if (!response.ok) {
            console.error("HTTP error! Response:", await response.text());
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Received data:", data);
        return data;
    } catch (error) {
        console.error("Failed to fetch session data:", error);
        throw new Error(`Failed to fetch data: ${error.message}`);
    }
};


export const register = async ({ username, email, password }) => {
    const url = `${REGISTER_ENDPOINT}`;
    console.log("Attempting to register user...");
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, email, password })
        });
        if (!response.ok) {
            const errorText = await response.text(); // Fetch the full error message if available
            console.error("HTTP error during registration! Response:", errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }
        const data = await response.json();
        localStorage.setItem("user_info",data)
        console.log("User registered successfully:", data);
        return data
        // Optionally handle additional actions, e.g., automatic login
    } catch (error) {
        console.error("Failed to register:", error);
        alert(`Failed to register: ${error.message}`);
    }
};

export const login = async (username, password) => {
    const url = `${LOGIN_ENDPOINT}`;
    const formData = new URLSearchParams();
    formData.append('username', username);
    formData.append('password', password);
    console.log("Attempting to login user...");
    try {
        const response = await fetch(url, {
            method: 'POST',
            body: formData
        });
        if (!response.ok) {
            const errorText = await response.text(); // Fetch the full error message if available
            console.error("HTTP error during login! Response:", errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }
        const data = await response.json();
        console.log("Login successful!!!!!");
        return data;  // Return data for further processing or navigation
    } catch (error) {
        console.error("Failed to login:", error);
        alert(`登录失败: ${error.message}`);  // Display the full error message
        throw error;  // Re-throw the error if you need to handle it later or indicate failure
    }
};

function processWorkflows(workflows) {
    return workflows.map(workflow => {
        return {
            ...workflow,
            nodes: JSON.parse(workflow.nodes),
            edges: JSON.parse(workflow.edges),
            workflow_name:workflow.name,
            database:workflow.database.split(",")
        };
    });
}

export const fetchWorkflowsByUsername = async (username, skip = 0, limit = 100) => {
    const url = `${WORKFLOWS_USER_ENDPOINT}${username}?skip=${skip}&limit=${limit}`;
    console.log("Attempting to fetch workflows...");
    const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token

    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
                'Content-Type': 'application/json'
                // If your API requires authentication tokens, add them in headers:
                // 'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("HTTP error while fetching workflows! Response:", errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }
        
        const data = await response.json();
        if (data.code !== 200) {
            // Handle the case where the code is not 200
            console.error("API returned non-200 status code:", data.code);
            window.location.href = LOGIN_API_NAME;
            return;
          }
        const workflows = processWorkflows(data.workflows)
        // const workflows = {...data,workflows:processData};
        console.log("Workflows fetched successfully:", workflows);
        return workflows;  // Return data for further processing in your application
    } catch (error) {
        console.error("Failed to fetch workflows:", error);
        alert(`Failed to fetch workflows: ${error.message}`);
        throw error;  // Re-throw the error if you need to handle it later or indicate failure
    }
};


export const updateWorkflow = async (workflow_id, workflow_data) => {
    // const url = `${WORKFLOWS_UPDATE_ENDPOINT}${workflow_id}`;
    const url = `${WORKFLOWS_UPDATE_ENDPOINT}${workflow_id}`; // Endpoint to update an existing workflow
    const method = 'PUT';
    console.log("Attempting to update workflow...",workflow_data);
    try {
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                // Include authorization header if needed, e.g.:
                // 'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(workflow_data)
        });

        if (!response.ok) {
            const errorText = await response.text();  // Fetch the full error message if available
            console.error("HTTP error during workflow update! Response:", errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        const data = await response.json();
        console.log("Workflow update successful:", data);
        return data;  // Return data for further processing or displaying success message
    } catch (error) {
        console.error("Failed to update workflow:", error);
        alert(`Update failed: ${error.message}`);
        throw error;  // Re-throw the error if you need to handle it later or indicate failure
    }
};


export const createWorkflow = async (workflow_data) => {

    const url = `${WORKFLOWS_UPDATE_ENDPOINT}`; // Endpoint to create a new workflow
    const method = 'POST';
    if ( workflow_data.id===NEW_WORKFLOW) {
        console.log("Removing 'id' from workflow data before sending to the API.");
        delete workflow_data.id;
    }
    console.log("Attempting to create workflow...",workflow_data);
    try {
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                // Include authorization header if needed, e.g.:
                // 'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(workflow_data)
        });

        if (!response.ok) {
            const errorText = await response.text();  // Fetch the full error message if available
            console.error("HTTP error during workflow create! Response:", errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        const data = await response.json();
        console.log("Workflow create successful:", data);
        return data;  // Return data for further processing or displaying success message
    } catch (error) {
        console.error("Failed to create workflow:", error);
        alert(`Update failed: ${error.message}`);
        throw error;  // Re-throw the error if you need to handle it later or indicate failure
    }
};

export const deleteWorkflow = async (workflowId) => {
    const url = `${WORKFLOWS_UPDATE_ENDPOINT}${workflowId}`; // Endpoint to delete a workflow
    console.log(`Attempting to delete workflow with id: ${workflowId}`);
    
    try {
        const response = await fetch(url, {
            method: 'DELETE', // Use DELETE method for deletion endpoint
            headers: {
                'Content-Type': 'application/json',
                // Include authorization header if needed, e.g.:
                // 'Authorization': `Bearer ${token}`
            },
            // No body is needed for a DELETE request
        });

        if (!response.ok) {
            const errorText = await response.text(); // Fetch the full error message if available
            console.error("HTTP error during workflow deletion! Response:", errorText);
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        console.log(`Workflow with id: ${workflowId} deletion successful.`);
        return { status: "success", message: `Workflow with id: ${workflowId} has been deleted.` };
    } catch (error) {
        console.error("Failed to delete workflow:", error);
        alert(`Deletion failed: ${error.message}`);
        throw error; // Re-throw the error if you need to handle it later or indicate failure
    }
};

export const fetchDatabaseCounts = async () => {
    const url = `${WORKFLOWS_DATABASE_COUNT_ENDPOINT}`;

    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error("HTTP error! Response:", await response.text());
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Received database counts:", data);
        return data;
    } catch (error) {
        console.error("Failed to fetch database counts:", error);
        return [];
    }
};

export const fetchCurrentUser = async () => {
    const url = `${USER_CONFORM_ENDPOINT}`; // 你的 /me 接口的URL路径
    const token = localStorage.getItem('token'); // 从localStorage中获取存储的Token

    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`, // 确保以正确的格式发送Token
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            console.error("HTTP error! Response:", await response.text());
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Received current user data:", data);
        return data;
    } catch (error) {
        console.error("Failed to fetch current user data:", error);
        return null;
    }
};
