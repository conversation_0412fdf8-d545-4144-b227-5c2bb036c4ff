import React, { useState } from 'react';

const CustomTooltip = ({ children, title }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div className="absolute z-10 p-2 text-sm text-white bg-black bg-opacity-75 rounded shadow-lg bottom-full left-1/2 transform -translate-x-1/2 mb-1">
          {title}
        </div>
      )}
    </div>
  );
};

export default CustomTooltip;