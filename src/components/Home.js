import React, { useState, useEffect } from 'react';
import { Layout, Menu, Tabs } from 'antd';
import { useNavigate, Outlet } from 'react-router-dom';
import {
  UserOutlined,
  HomeOutlined,
  AppstoreOutlined,
  ShopOutlined,
  MessageOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FileImageOutlined,
  FileTextOutlined,
  ShoppingOutlined
} from '@ant-design/icons';
import {
  DASHBOARD_API_NAME,
  LLM_DIALOGUE_ENDPOINT,
  ARTICLE_NAME,
  PRODUCT_NAME
} from './Configs/Config';
import WorkflowList from './Workflow/Agents/workflows/WorkflowList';
import KnowledgeBaseList from './Workflow/Agents/KnowledgeBase/KnowledgeBaseList';
import PromptList from './Workflow/Agents/prompt/PromptList';
import AgentList from './Workflow/Agents/Agents/AgentList';
import { fetchData } from './Routers/Router';
import ChartsDisplay from '../components/eda/ChartsDisplay';
import './Home.css';
import SidebarList from './SidebarList';
import ProductList from './Product/ProductList';

const { Sider, Content, Header } = Layout;

const Home = () => {
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(false);
  const [selectedMenu, setSelectedMenu] = useState('home');
  const [personalSpaceContent, setPersonalSpaceContent] = useState(null);
  const [reportData, setReportData] = useState(null);

  useEffect(() => {
    if (selectedMenu === 'home') {
      const report_endpoint = `${LLM_DIALOGUE_ENDPOINT}/report`;
      const fetchReportData = async () => {
        try {
          const data = await fetchData(report_endpoint);
          setReportData(data);
        } catch (error) {
          console.error('Error fetching report data:', error);
        }
      };
      fetchReportData();
    }
  }, [selectedMenu]);

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const handleMenuClick = (e) => {
    setSelectedMenu(e.key);

    if (e.key === 'conversations') {
      setCollapsed(true);
      navigate(DASHBOARD_API_NAME);
    } else if (e.key === 'personalSpace') {
      setPersonalSpaceContent(
        <Tabs
          defaultActiveKey="1"
          items={[
            {
              key: '1',
              label: '工作流',
              children: <WorkflowList />,
            },
            {
              key: '2',
              label: '提示词',
              children: <PromptList />,
            },
            {
              key: '3',
              label: '知识库',
              children: <KnowledgeBaseList />,
            },
            {
              key: '4',
              label: 'Bots',
              children: <AgentList />,
            },
            {
              key: '5',
              label: '插件',
              children: <>插件 content</>,
            },
          ]}
        />
      );
    } else if (e.key === ARTICLE_NAME) {
      setPersonalSpaceContent(null);
      navigate(`${ARTICLE_NAME}?&is_published=true`);
    } else if (e.key === PRODUCT_NAME) {
      setPersonalSpaceContent(null);
      navigate(PRODUCT_NAME);
    } else {
      setPersonalSpaceContent(null);
      navigate('/');
    }
  };

  const getPageTitle = () => {
    switch (selectedMenu) {
      case 'personalSpace':
        return '个人空间';
      case ARTICLE_NAME:
        return '文章管理';
      case PRODUCT_NAME:
        return '产品管理';
      default:
        return '主页';
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={200} collapsible collapsed={collapsed} trigger={null}>
        <div className="logo" />
        <Menu
          mode="inline"
          selectedKeys={[selectedMenu]}
          style={{ height: '100%', borderRight: 0 }}
          onClick={handleMenuClick}
          items={[
            {
              key: 'home',
              icon: <HomeOutlined />,
              label: '主页',
            },
            {
              key: 'personalSpace',
              icon: <UserOutlined />,
              label: '个人空间',
            },
            {
              key: 'botStore',
              icon: <AppstoreOutlined />,
              label: 'Bot 商店',
            },
            {
              key: 'modelSquare',
              icon: <ShopOutlined />,
              label: '模型广场',
            },
            // {
            //   key: 'conversations',
            //   icon: <MessageOutlined />,
            //   label: '会话',
            // },
            {
              key: PRODUCT_NAME,
              icon: <ShoppingOutlined />,
              label: '产品',
            },
            {
              key: 'content-market',
              icon: <FileImageOutlined />,
              label: '内容集市',
              children: [
                {
                  key: ARTICLE_NAME,
                  icon: <FileTextOutlined />,
                  label: '文章',
                }
              ]
            },
          ]}
        />
      </Sider>
      <Layout>
        <Header className="site-layout-background" style={{ padding: 0, background: '#fff', display: 'flex', alignItems: 'center' }}>
          {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
            className: 'trigger',
            onClick: toggleSidebar,
          })}
          <h1 className="page-title-1" style={{ margin: 0, marginLeft: '20px' }}>
            {getPageTitle()}
          </h1>
          <div style={{ marginLeft: 'auto', marginRight: '160px' }}>
            <input type="search" placeholder="搜索" style={{ marginRight: '10px' }} />
            <select>
              <option>全部</option>
            </select>
          </div>
          <div className='content-container'>
            <SidebarList />
          </div>
        </Header>
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
          }}
        >
          {selectedMenu === 'home' ? <ChartsDisplay reportData={reportData} /> : personalSpaceContent || <Outlet />}
        </Content>
      </Layout>
    </Layout>
  );
};

export default Home;
