// ProductCard.js
import React from "react";
import { useNavigate } from "react-router-dom";
import PodcastIcon from "./PodcastIcon";
import styles from "./ProductCard.module.css";

const ProductCard = ({ product, isFirst }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(product.router);
  };

  return (
    <div className={styles.card} onClick={handleClick}>
      <div className={styles.content}>
        {isFirst && <PodcastIcon />}
        <span className={styles.name}>{product.name}</span>
      </div>
    </div>
  );
};

export default ProductCard;
