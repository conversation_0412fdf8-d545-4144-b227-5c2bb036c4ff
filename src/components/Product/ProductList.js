// ProductList.js
import React from "react";
import ProductCard from "./ProductCard";
import { products } from "./products";
import styles from "./ProductList.module.css";
import SidebarList from '../SidebarList'
const ProductList = () => {
  return (
    <div>
      <h2 className={styles.title}>产品列表</h2> {/* 添加标题 */}
      <div className='content-container'>
        <SidebarList />
      </div>
      <div className={styles.container}>
        {products.map((product, index) => (
          <ProductCard
            key={product.name}
            product={product}
            isFirst={index === 0}
          />
        ))}
      </div>
    </div>
  );
};

export default ProductList;
