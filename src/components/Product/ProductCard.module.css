/* ProductCard.module.css */

.card {
    position: relative;
    width: 100%;
    padding-top: 75%; /* 4:3 比例 */
    border: 1px solid #ccc;
    border-radius: 8px;
    cursor: pointer;
    overflow: hidden;
    background-color: #fff;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
  }
  
  .card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-5px);
  }
  
  .content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .name {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    text-align: center;
  }
  