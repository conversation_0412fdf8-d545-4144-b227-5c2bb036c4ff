/* ProductList.module.css */

.title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    color: #333;
  }
  
  
.container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
    justify-items: center;
}

/* 为了确保每个卡片保持4:3的宽高比 */
.container > * {
    width: 100%;
    aspect-ratio: 6/3;
}

@media (min-width: 1200px) {
    .container {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 767px) {
    .container {
        grid-template-columns: repeat(2, 1fr);
    }
}