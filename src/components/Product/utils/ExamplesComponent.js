// src/Components/PodcastManager/ExamplesComponent.jsx
import React, { useState, useEffect } from 'react';
import { Card, Tooltip } from 'antd';
import styles from './ExamplesComponent.module.css';

const ExamplesComponent = ({ examples, onExampleClick }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const examplesPerPage = 3;

    // 定义一组颜色
    const colors = ['#FFB6C1', '#ADD8E6', '#90EE90', '#FFD700', '#FFA500', '#EE82EE'];

    // 处理键盘事件
    const handleKeyDown = (e) => {
        if (e.key === 'ArrowLeft') {
            // 向左切换
            setCurrentIndex((prevIndex) =>
                prevIndex - examplesPerPage >= 0 ? prevIndex - examplesPerPage : prevIndex
            );
        } else if (e.key === 'ArrowRight') {
            // 向右切换
            setCurrentIndex((prevIndex) =>
                prevIndex + examplesPerPage < examples.length ? prevIndex + examplesPerPage : prevIndex
            );
        }
    };

    useEffect(() => {
        // 添加键盘事件监听器
        window.addEventListener('keydown', handleKeyDown);
        // 组件卸载时清理监听器
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [currentIndex, examples.length]);

    // 计算当前显示的示例
    const currentExamples = examples.slice(currentIndex, currentIndex + examplesPerPage);

    return (
        <div className={styles.examplesContainer}>
            {currentExamples.map((example, index) => {
                const displayText =
                    example.text.length > 6
                        ? example.text.substring(0, 6) + '...'
                        : example.text;

                // 计算卡片颜色
                const color = colors[(currentIndex + index) % colors.length];

                return (
                    <Tooltip title={example.text} key={currentIndex + index}>
                        <Card
                            hoverable
                            onClick={() => {
                                if (example.conversable) {
                                    onExampleClick(example.text);
                                }
                            }}
                            className={styles.exampleCard}
                            style={{
                                backgroundColor: color,
                                height: '40px', // 设置卡片高度
                                overflow: 'hidden', // 隐藏溢出内容
                            }}
                        >
                            {displayText}
                        </Card>
                    </Tooltip>
                );
            })}
        </div>
    );
};

export default ExamplesComponent;
