// src/Components/utils/ArticleListSidebar.jsx
import React, { useState, useEffect } from 'react';
import { Checkbox, List, Pagination, Empty, Input, message, Select } from 'antd';
import classNames from 'classnames';
import styles from './ArticleListSidebar.module.css';
import { fetchBulk } from '../../Routers/Router';
import { FILE_MANAGER_ENDPOINT, KNOWLEDGE_BASE_ENDPOINT } from '../../Configs/Config';
import { useSelector } from 'react-redux';

const { Option } = Select;

const ArticleListSidebar = ({
    sortField = 'updated_at',
    sortOrder = 'desc',
    pageSize = 10,
    onSelectArticle,
    selectedDoc,
    disabled = false,
}) => {
    const [data, setData] = useState([]);
    const [kbData, setKbData] = useState([]);
    const [total, setTotal] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [searchKeyword, setSearchKeyword] = useState('');
    const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState(null);
    const { user_info } = useSelector((state) => state.user);


    useEffect(() => {
        const fetchArticles = async () => {
            const skip = (currentPage - 1) * pageSize;
            try {
                let endpoint_api = `${FILE_MANAGER_ENDPOINT}/bulk?sort_by=${sortField}&sort_order=${sortOrder}&skip=${skip}&limit=${pageSize}&chunk_type=pdf&username=${user_info.username}`;
                if (selectedKnowledgeBase) {
                    endpoint_api += `&kb_id=${selectedKnowledgeBase}`;
                }
                if (searchKeyword) {
                    endpoint_api += `&name__contains=${searchKeyword}`;
                }

                const result = await fetchBulk(endpoint_api);
                setData(result.data);
                setTotal(result.count);

                // 移除自动选择第一篇文章的逻辑
                // 如果文章列表为空，清除已选中文章
                if (result.data.length === 0 && onSelectArticle) {
                    onSelectArticle(null); // 清除父组件的选中状态
                }
            } catch (error) {
                console.error(error);
                message.error('获取文章失败');
            }
        };

        fetchArticles();
    }, [selectedKnowledgeBase, currentPage, searchKeyword, sortField, sortOrder, pageSize]);

    useEffect(() => {
        const fetchKnowledgeBases = async () => {
            try {
                const result = await fetchBulk(`${KNOWLEDGE_BASE_ENDPOINT}/bulk?username=${user_info.username}`);
                setKbData(result.data);
                if (result.data.length > 0) {
                    setSelectedKnowledgeBase(result.data[0].id);
                }
            } catch (error) {
                console.error(error);
                message.error('获取知识库失败');
            }
        };

        fetchKnowledgeBases();
    }, []);

    const toggleSelection = (article) => {
        if (selectedDoc && selectedDoc.id === article.id) {
            if (onSelectArticle) {
                onSelectArticle(null);
            }
        } else {
            if (onSelectArticle) {
                onSelectArticle(article);
            }
        }
    };

    const handleCheckboxChange = (article) => {
        toggleSelection(article);
    };

    const handleArticleClick = (article) => {
        if (!disabled) {
            toggleSelection(article);
        }
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setCurrentPage(1);
    };

    const handleKnowledgeBaseChange = (value) => {
        setSelectedKnowledgeBase(value);
        setCurrentPage(1);
        if (onSelectArticle) {
            onSelectArticle(null); // 清除父组件的选中状态
        }
    };

    return (
        <div className={styles.articleListContainer}>
            <div className={styles.searchBarContainer}>
                <Select
                    placeholder="选择知识库"
                    onChange={handleKnowledgeBaseChange}
                    allowClear
                    className={styles.selectSelector}
                    style={{ width: 100, marginRight: '16px' }}
                    value={selectedKnowledgeBase}
                    disabled={disabled}
                >
                    {kbData.map((kb) => (
                        <Option key={kb.id} value={kb.id}>
                            {kb.name}
                        </Option>
                    ))}
                </Select>
                <Input.Search
                    placeholder="搜索文章"
                    allowClear
                    onSearch={handleSearch}
                    className={styles.inputStyle}
                    style={{ flex: 1 }}
                    disabled={disabled}
                />
            </div>
            {data.length > 0 ? (
                <>
                    <List
                        itemLayout="horizontal"
                        dataSource={data}
                        renderItem={(article) => {
                            const isSelected = selectedDoc && selectedDoc.id === article.id;
                            return (
                                <List.Item
                                    key={article.id}
                                    className={classNames(styles.listItem, {
                                        [styles.selectedArticle]: isSelected,
                                    })}
                                >
                                    <Checkbox
                                        checked={isSelected}
                                        onChange={() => handleCheckboxChange(article)}
                                        disabled={disabled}
                                    />
                                    <div
                                        className={classNames(styles.checkboxWrapper, styles.articleTitle)}
                                        onClick={() => handleArticleClick(article)}
                                        style={{ cursor: disabled ? 'not-allowed' : 'pointer', marginLeft: '8px' }}
                                    >
                                        {article.name}
                                    </div>
                                </List.Item>
                            );
                        }}
                    />
                    <Pagination
                        className={styles.customPagination}
                        current={currentPage}
                        total={total}
                        pageSize={pageSize}
                        onChange={handlePageChange}
                        showSizeChanger={false}
                        disabled={disabled}
                    />
                </>
            ) : (
                <Empty description="未找到文章" className={styles.emptyState} />
            )}
        </div>
    );
};

export default ArticleListSidebar;
