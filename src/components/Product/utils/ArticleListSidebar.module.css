/* ArticleListSidebar.module.css */

.articleListContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.searchBarContainer {
    display: flex;
    align-items: center;
    padding: 1px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;
}

.selectSelector {
    margin-right: 16px;
}

.inputStyle {
    flex: 1;
}

/* 搜索框样式覆盖 */
.searchBarContainer :global(.ant-input-affix-wrapper) {
    position: relative;
    display: inline-flex;
    width: 100%;
    min-width: 0;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    line-height: 1.5714285714285714;
    transition: all 0.2s;
    height: 33px;
}

.searchBarContainer :global(.ant-input) {
    height: 25px;
    padding: 0;
}

.searchBarContainer :global(.ant-input-group-addon) {
    height: 33px;
}

.searchBarContainer :global(.ant-input-search-button) {
    height: 33px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 列表项样式 */
.listItem {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
}

.listItem:hover {
    background-color: #f5f5f5;
}

.selectedArticle {
    background-color: #e6f7ff;
}

.checkboxWrapper {
    flex: 1;
    margin-left: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.articleTitle {
    color: #333;
}

/* 分页样式 */
.customPagination {
    margin-top: 16px;
    padding: 16px;
    text-align: right;
    border-top: 1px solid #f0f0f0;
}

/* 空状态样式 */
.emptyState {
    margin: 32px 0;
}