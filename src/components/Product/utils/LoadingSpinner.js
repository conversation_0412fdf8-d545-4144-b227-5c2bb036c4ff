import React from 'react';

const LoadingSpinner = ({ size = 'default', message = '正在生成内容...' }) => {
  // Map size prop to actual dimensions
  const sizeMap = {
    small: 'w-6 h-6 border-2',
    default: 'w-8 h-8 border-4',
    large: 'w-10 h-10 border-4'
  };

  const spinnerSize = sizeMap[size] || sizeMap.default;

  return (
    <div className="flex items-center justify-center w-full p-4">
      <div className="flex flex-col items-center gap-2">
        <div 
          className={`${spinnerSize} border-blue-500 border-t-transparent rounded-full animate-spin`}
          role="status"
          aria-label="loading"
        />
        {message && (
          <span className="text-gray-600 text-sm">{message}</span>
        )}
      </div>
    </div>
  );
};

export default LoadingSpinner;