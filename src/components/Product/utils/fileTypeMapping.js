// fileTypeMapping.js
import React from 'react';

// Example SVG Icons for different file types
export const fileTypeMapping = {
  pdf: {
    color: '#E53E3E', // Red
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 36 36"
        fill="none"
        className="h-10 w-10 flex-shrink-0"
        width="36"
        height="36"
        aria-hidden="true"
      >
        <rect width="36" height="36" rx="6" fill="#E53E3E"></rect>
        <path
          d="M10.5 14V13.5V12.1667C10.5 11.2462 11.2462 10.5 12.1667 10.5H15.5H23.8333C24.7538 10.5 25.5 11.2462 25.5 12.1667V13.5V14M10.5 14V23.8333C10.5 24.7538 11.2462 25.5 12.1667 25.5H15.5H18H23.8333C24.7538 25.5 25.5 24.7538 25.5 23.8333V14M10.5 14H25.5M14 21.5L16.5 19L19 20.5L22 17.5"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
    ),
  },
  pptx: {
    color: '#FF5588', // Pink
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 36 36"
        fill="none"
        className="h-10 w-10 flex-shrink-0"
        width="36"
        height="36"
        aria-hidden="true"
      >
        <rect width="36" height="36" rx="6" fill="#FF5588"></rect>
        <path
          d="M19.6663 9.66663H12.9997C12.5576 9.66663 12.1337 9.84222 11.8212 10.1548C11.5086 10.4673 11.333 10.8913 11.333 11.3333V24.6666C11.333 25.1087 11.5086 25.5326 11.8212 25.8451C12.1337 26.1577 12.5576 26.3333 12.9997 26.3333H22.9997C23.4417 26.3333 23.8656 26.1577 24.1782 25.8451C24.4907 25.5326 24.6663 25.1087 24.6663 24.6666V14.6666L19.6663 9.66663Z"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M19.667 9.66663V14.6666H24.667"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M21.3337 18.8334H14.667"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M21.3337 22.1666H14.667"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
        <path
          d="M16.3337 15.5H15.5003H14.667"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
    ),
  },
  docx: {
    color: '#3182CE', // Blue
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 36 36"
        fill="none"
        className="h-10 w-10 flex-shrink-0"
        width="36"
        height="36"
        aria-hidden="true"
      >
        <rect width="36" height="36" rx="6" fill="#3182CE"></rect>
        <path
          d="M10.5 14V13.5V12.1667C10.5 11.2462 11.2462 10.5 12.1667 10.5H15.5H23.8333C24.7538 10.5 25.5 11.2462 25.5 12.1667V13.5V14M10.5 14V23.8333C10.5 24.7538 11.2462 25.5 12.1667 25.5H15.5H18H23.8333C24.7538 25.5 25.5 24.7538 25.5 23.8333V14M10.5 14H25.5M14 21.5L16.5 19L19 20.5L22 17.5"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
    ),
  },
  txt: {
    color: '#4A5568', // Gray
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 36 36"
        fill="none"
        className="h-10 w-10 flex-shrink-0"
        width="36"
        height="36"
        aria-hidden="true"
      >
        <rect width="36" height="36" rx="6" fill="#4A5568"></rect>
        <path
          d="M10.5 14V13.5V12.1667C10.5 11.2462 11.2462 10.5 12.1667 10.5H15.5H23.8333C24.7538 10.5 25.5 11.2462 25.5 12.1667V13.5V14M10.5 14V23.8333C10.5 24.7538 11.2462 25.5 12.1667 25.5H15.5H18H23.8333C24.7538 25.5 25.5 24.7538 25.5 23.8333V14M10.5 14H25.5M14 21.5L16.5 19L19 20.5L22 17.5"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
    ),
  },
  default: {
    color: '#A0AEC0', // Default Gray
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 36 36"
        fill="none"
        className="h-10 w-10 flex-shrink-0"
        width="36"
        height="36"
        aria-hidden="true"
      >
        <rect width="36" height="36" rx="6" fill="#A0AEC0"></rect>
        <path
          d="M10.5 14V13.5V12.1667C10.5 11.2462 11.2462 10.5 12.1667 10.5H15.5H23.8333C24.7538 10.5 25.5 11.2462 25.5 12.1667V13.5V14M10.5 14V23.8333C10.5 24.7538 11.2462 25.5 12.1667 25.5H15.5H18H23.8333C24.7538 25.5 25.5 24.7538 25.5 23.8333V14M10.5 14H25.5M14 21.5L16.5 19L19 20.5L22 17.5"
          stroke="white"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></path>
      </svg>
    ),
  },
};
