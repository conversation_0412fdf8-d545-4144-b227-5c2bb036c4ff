/* src/components/ChatInput/ChatInput.module.css */

.container {
  background-color: #ffffff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  width: 100%;
  box-sizing: border-box;
}

.maxWidthContainer {
  max-width: 1024px; /* 相当于 Tailwind 的 max-w-4xl */
  margin: 0 auto;
  padding-right: 20px;
}

.customInputContainer {
  display: flex;
  flex-direction: column; /* 堆叠已上传文件和输入行 */
  align-items: stretch;
  background: #f7f7f7;
  border-radius: 8px;
  padding-right:16px;
  position: relative;
  border: 2px dashed transparent;
  transition: border-color 0.3s;
  width: 100%;
}

.dragActive {
  border-color: #2a8a82; /* 指示拖放活动 */
}

.uploadedFilesContainer {
  display: flex;
  flex-wrap: wrap; /* 允许多行文件 */
  gap: 8px; /* 文件项之间的间距 */
  margin-bottom: 8px; /* 已上传文件与输入行之间的间距 */
}

.uploadedFile {
  position: relative; /* 以便绝对定位关闭按钮 */
  display: flex;
  align-items: center;
  width: 220px; /* 固定宽度 */
  background: #d1fae5; /* 相当于 hsl(172, 53%, 92%) */
  border-radius: 8px;
  padding: 12px; /* 增加填充以适应关闭按钮 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.fileIcon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: white; /* 确保图标在背景色上可见 */
}

.fileInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.fileName {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fileType {
  font-size: 12px;
  color: #a0a0a0; /* 相当于 Tailwind 的 gray-400 */
  margin-top: 2px;
}

.closeButton {
  position: absolute; /* 相对于 .uploadedFile 进行定位 */
  top: 4px; /* 根据需要调整 */
  right: 4px; /* 根据需要调整 */
  transform: translateY(-50%) translateX(50%); /* 相当于 -translate-y-1/2 translate-x-1/2 */
  background: #d3d3d3; /* 浅灰色背景 */
  color: #333; /* 深灰色图标 */
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  cursor: pointer;
  border: 3px solid #f4f4f4; /* 相当于 border-[3px] border-[#f4f4f4] */
  transition: background-color 0.3s, color 0.3s, transform 0.3s;
}

.closeButton:hover {
  background: #b0b0b0; /* 悬停时略深的灰色 */
  color: #fff; /* 悬停时图标为白色 */
  transform: translateY(-50%) translateX(50%) scale(1.1); /* 悬停时稍微放大 */
}

.inputLine {
  display: flex;
  align-items: center;
}

.uploadButton {
  margin-right: 8px;
  font-size: 20px;
  color: #595959;
  background: transparent;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
}

.sendButton {
  margin-left: 8px;
  font-size: 20px;
  color: #2a8a82;
  background: transparent;
  border: none;
  padding: 4px 8px;
  cursor: pointer;
}

.customTextarea {
  flex: 1;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-size: 14px;
  padding: 4px 8px;
  min-height: 40px; /* 最小高度 */
  max-height: 200px; /* 可选：最大高度 */
  overflow: hidden;
}

.iconXs {
  width: 16px; /* 根据需要调整大小 */
  height: 16px;
}

.uploadProgress {
  margin-bottom: 8px;
}
