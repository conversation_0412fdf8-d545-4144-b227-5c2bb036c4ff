// src/components/ChatInput/ChatInput.jsx

import React, { useEffect, useState, useRef } from 'react';
import { Upload, Button, message, Progress } from 'antd';
import {
    SendOutlined,
    PaperClipOutlined,
    LoadingOutlined,
} from '@ant-design/icons';
import styles from './ChatInput.module.css';
import { getFileIcon } from './getFileIcon';
import { uploadFiles } from '../../Routers/Router';

const ChatInput = ({ query, onSendMessage }) => {
    // 状态管理
    const [messageText, setMessageText] = useState('');
    const [uploading, setUploading] = useState(false);
    const [fileList, setFileList] = useState([]); // 用于管理上传列表的显示
    const [attachments, setAttachments] = useState([]); // 用于存储上传返回的data对象
    const [dragActive, setDragActive] = useState(false);
    const textareaRef = useRef(null);

    useEffect(() => {
        if (query) {
            setMessageText(query);
            // 可选：如果外部设置了query，自动调整textarea高度
            if (textareaRef.current) {
                textareaRef.current.style.height = 'auto';
                textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
            }
        }
    }, [query]);

    // 处理消息提交
    const handleMessageSubmit = async () => {
        if (!messageText.trim() && attachments.length === 0) return;

        // 调用回调函数，传递消息和附件
        console.log(attachments,'attachmentsattachments')
        if (onSendMessage) {
            await onSendMessage({
                message: messageText.trim(),
                files: attachments, // 传递完整的data对象数组
            });
        }

        // 重置输入字段
        setMessageText('');
        if (textareaRef.current) {
            textareaRef.current.style.height = 'auto'; // 重置高度
        }
        setFileList([]);
        setAttachments([]);
    };

    // 处理Enter键按下
    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleMessageSubmit();
        }
    };

    // 处理文件选择
    const handleFileChange = ({ file, fileList: newFileList }) => {
        // 仅处理新文件
        const newFiles = newFileList.filter(
            (f) => !fileList.some((existing) => existing.uid === f.uid)
        );

        newFiles.forEach((file) => {
            uploadSingleFile(file);
        });

        // 更新fileList，保留已上传和正在上传的文件
        setFileList((prevList) => [...prevList, ...newFiles]);
    };

    // 上传单个文件
    const uploadSingleFile = async (file) => {
        setUploading(true);
        try {
            const data = await uploadFiles(file.originFileObj);

            // 检查attachments中是否已存在相同的url
            const urlExists = attachments.some((attachment) => attachment.url === data.url);

            if (urlExists) {
                message.warning(`文件 ${file.name} 已经上传过，不会重复添加。`);
                // 从fileList中移除重复的文件
                setFileList((prevList) =>
                    prevList.filter((f) => f.uid !== file.uid)
                );
                return;
            }

            // 上传成功，更新fileList和attachments
            message.success(`${file.name} 上传成功！`);
            setFileList((prevList) =>
                prevList.map((f) =>
                    f.uid === file.uid ? { ...f, status: 'done', progress: 100 } : f
                )
            );
            setAttachments((prevAttachments) => [...prevAttachments, data]);
        } catch (error) {
            // 处理上传错误
            message.error(`文件 ${file.name} 上传失败：${error.message}`);
            setFileList((prevList) =>
                prevList.map((f) =>
                    f.uid === file.uid ? { ...f, status: 'error' } : f
                )
            );
        } finally {
            setUploading(false);
        }
    };

    // 验证文件上传前的合法性
    const beforeUpload = (file) => {
        const isValidType = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'video/mp4',
            'video/x-msvideo',
            'video/mpeg',
            'video/quicktime',
            'video/webm',
            'audio/mpeg',
            'audio/wav',
            'audio/ogg',
            'audio/aac',
        ].includes(file.type);

        const isValidSize = file.size / 1024 / 1024 < 200; // 小于10MB

        if (!isValidType) {
            message.error('不支持的文件类型');
        }
        if (!isValidSize) {
            message.error('文件大小超过 10MB');
        }
        return isValidType && isValidSize;
    };

    // 拖拽事件处理器
    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(true);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(true);
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        const files = Array.from(e.dataTransfer.files);
        const validFiles = files.filter(beforeUpload).map((file, index) => ({
            uid: `${Date.now()}-${index}`,
            name: file.name,
            type: file.type,
            status: 'uploading',
            originFileObj: file,
            progress: 0,
        }));

        // 过滤掉名称重复的文件
        setFileList((prevList) => {
            const existingFileNames = prevList.map((file) => file.name);
            const filteredNewFiles = validFiles.filter(
                (file) => !existingFileNames.includes(file.name)
            );
            return [...prevList, ...filteredNewFiles];
        });

        // 开始上传有效文件
        validFiles.forEach((file) => {
            uploadSingleFile(file);
        });
    };

    // 获取文件扩展名
    const getFileExtension = (filename) => {
        return filename.split('.').pop().toUpperCase();
    };

    return (
        <div className={styles.container}>
            <div className={styles.maxWidthContainer}>
                <div
                    className={`${styles.customInputContainer} ${
                        dragActive ? styles.dragActive : ''
                    }`}
                    onDragEnter={handleDragEnter}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                >
                    {/* 条件渲染已上传文件列表 */}
                    {fileList.length > 0 && (
                        <div className={styles.uploadedFilesContainer}>
                            {fileList.map((file, index) => {
                                const { color, icon } = getFileIcon(file.name);
                                return (
                                    <div key={file.uid} className={styles.uploadedFile}>
                                        <div
                                            className={styles.fileIcon}
                                            style={{ backgroundColor: color }}
                                            aria-label={`${getFileExtension(file.name)} 文件`}
                                        >
                                            {icon}
                                        </div>
                                        <div className={styles.fileInfo}>
                                            <div className={styles.fileName}>{file.name}</div>
                                            <div className={styles.fileType}>
                                                {getFileExtension(file.name)}
                                            </div>
                                            {file.status === 'uploading' && (
                                                <Progress
                                                    percent={file.progress}
                                                    size="small"
                                                    status="active"
                                                    className={styles.fileProgress}
                                                />
                                            )}
                                            {file.status === 'error' && (
                                                <div className={styles.errorText}>上传失败</div>
                                            )}
                                        </div>
                                        {/* 自定义关闭按钮 */}
                                        <button
                                            className={styles.closeButton}
                                            onClick={() => {
                                                // 从fileList中移除文件
                                                setFileList((prevList) =>
                                                    prevList.filter((_, i) => i !== index)
                                                );
                                                // 同时从attachments中移除对应的data对象
                                                setAttachments((prevAttachments) =>
                                                    prevAttachments.filter(
                                                        (attachment) => attachment.url !== file.data?.url
                                                    )
                                                );
                                            }}
                                            aria-label={`移除文件 ${file.name}`}
                                        >
                                            <span data-state="closed">
                                                <svg
                                                    width="29"
                                                    height="28"
                                                    viewBox="0 0 29 28"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className={styles.iconXs}
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        clipRule="evenodd"
                                                        d="M7.30286 6.80256C7.89516 6.21026 8.85546 6.21026 9.44775 6.80256L14.5003 11.8551L19.5529 6.80256C20.1452 6.21026 21.1055 6.21026 21.6978 6.80256C22.2901 7.39485 22.2901 8.35515 21.6978 8.94745L16.6452 14L21.6978 19.0526C22.2901 19.6449 22.2901 20.6052 21.6978 21.1974C21.1055 21.7897 20.1452 21.7897 19.5529 21.1974L14.5003 16.1449L9.44775 21.1974C8.85546 21.7897 7.89516 21.7897 7.30286 21.1974C6.71057 20.6052 6.71057 19.6449 7.30286 19.0526L12.3554 14L7.30286 8.94745C6.71057 8.35515 6.71057 7.39485 7.30286 6.80256Z"
                                                        fill="currentColor"
                                                    ></path>
                                                </svg>
                                            </span>
                                        </button>
                                    </div>
                                );
                            })}
                        </div>
                    )}

                    {/* 输入行：上传按钮、textarea、发送按钮 */}
                    <div className={styles.inputLine}>
                        {/* 上传按钮 */}
                        <Upload
                            beforeUpload={beforeUpload}
                            fileList={fileList}
                            onChange={handleFileChange}
                            showUploadList={false}
                            multiple={true} // 允许多文件上传
                        >
                            <Button
                                type="text"
                                icon={<PaperClipOutlined />}
                                disabled={uploading}
                                className={styles.uploadButton}
                                aria-label="上传文件"
                            />
                        </Upload>

                        {/* 消息输入textarea */}
                        <textarea
                            ref={textareaRef}
                            value={messageText}
                            onChange={(e) => {
                                setMessageText(e.target.value);
                                // 自动调整textarea高度
                                e.target.style.height = 'auto';
                                e.target.style.height = `${e.target.scrollHeight}px`;
                            }}
                            onKeyDown={handleKeyPress}
                            placeholder="输入问题进行对话..."
                            className={styles.customTextarea}
                        />

                        {/* 发送按钮 */}
                        <Button
                            type="text"
                            onClick={handleMessageSubmit}
                            disabled={
                                (!messageText.trim() && attachments.length === 0) || uploading
                            }
                            icon={uploading ? <LoadingOutlined /> : <SendOutlined />}
                            className={styles.sendButton}
                            aria-label="发送消息"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ChatInput;
