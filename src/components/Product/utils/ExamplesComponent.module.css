/* src/Components/PodcastManager/ExamplesComponent.module.css */
.examplesContainer {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1px;
}

.exampleCard {
    width: 30%;
    cursor: pointer;
    text-align: center;
    padding: 1px; /* 确保文本周围有 1px 的内边距 */
    height: 40px; /* 固定高度 */
    overflow: hidden; /* 隐藏溢出内容 */
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中 */
}

/* 覆盖 ant-card-body 的宽度 */
.exampleCard :global(.ant-card-body) {
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1px; /* 确保文本周围有 1px 的内边距 */
}
