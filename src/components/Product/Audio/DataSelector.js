// import React, { useState, useEffect } from 'react';
// import { Table, message, Spin } from 'antd';
// import { fetchBulk } from '../../Routers/Router';
// import { useDispatch } from 'react-redux';
// import { setWorkflows } from '../../Workflow/workflowSlice';
// import { setAgents } from '../../Workflow/Agents/Agents/AgentSlice';
// import styles from './DataSelector.module.css';

// const DataSelector = ({ endpoint, dataType, onSelect, isGenerating }) => {
//     const [data, setData] = useState([]);
//     const [totalItems, setTotalItems] = useState(0);
//     const [loading, setLoading] = useState(false);
//     const dispatch = useDispatch();
//     const [sortField, setSortField] = useState('created_at');
//     const [sortOrder, setSortOrder] = useState('descend');
//     const [page, setPage] = useState(1);
//     const [pageSize, setPageSize] = useState(10);

//     const getData = async (roleFilter, page, pageSize) => {
//         setLoading(true);
//         try {
//             const skip = (page - 1) * pageSize;
//             const limit = pageSize;
//             let endpoint_api = `${endpoint}/bulk?skip=${skip}&limit=${limit}&sort_by=${sortField}&sort_order=${sortOrder}`;
//             if (roleFilter !== 'all') {
//                 endpoint_api += `&role=${roleFilter}`;
//             }
//             const result = await fetchBulk(endpoint_api);
//             setData(result.data);
//             setTotalItems(result.count);
//             if (dataType === 'workflows') {
//                 dispatch(setWorkflows(result.data));
//             } else if (dataType === 'agents') {
//                 dispatch(setAgents(result.data));
//             }
//         } catch (error) {
//             console.error(error);
//             message.error(`Failed to fetch ${dataType} data`);
//         } finally {
//             setLoading(false);
//         }
//     };

//     useEffect(() => {
//         getData('all', page, pageSize);
//     }, [page, pageSize, sortField, sortOrder]);

//     const handleTableChange = (pagination, filters, sorter) => {
//         setPage(pagination.current);
//         setPageSize(pagination.pageSize);
//         if (sorter.field) {
//             setSortField(sorter.field);
//             setSortOrder(sorter.order);
//         }
//     };

//     const columns = [
//         {
//             title: '名称',
//             dataIndex: 'name',
//             key: 'name',
//             sorter: true,
//         },
//         {
//             title: '描述',
//             dataIndex: 'description',
//             key: 'description',
//         },
//         // Add more columns as needed
//     ];

//     return (
//         <div className={styles.dataSelectorContainer}>
//             {loading ? (
//                 <div className={styles.loadingSpinner}>
//                     <Spin tip={`Loading ${dataType}...`} />
//                 </div>
//             ) : (
//                 <div className={styles.tableContainer}>
//                     <Table
//                         dataSource={data}
//                         columns={columns}
//                         rowKey="id"
//                         pagination={{
//                             total: totalItems,
//                             current: page,
//                             pageSize,
//                             showSizeChanger: true,
//                         }}
//                         onChange={handleTableChange}
//                         onRow={(record) => ({
//                             onClick: () => {
//                                 if (!isGenerating) {
//                                     onSelect(record);
//                                 }
//                             },
//                         })}
//                     />
//                 </div>
//             )}
//         </div>
//     );
// };

// export default DataSelector;
