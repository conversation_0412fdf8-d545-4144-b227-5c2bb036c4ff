/* src/Components/PodcastManager/WorkflowsComponent.module.css */

.workflowsContainer {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    background-color: #ffffff; /* White background for a clean look */
    padding-right: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
    border-radius: 8px; /* Rounded corners */
    box-sizing: border-box;
}

.header {
    flex: 0 0 auto; /* Prevent header from growing */
}

.mainContent {
    flex: 1 1 auto; /* Allow mainContent to grow and fill available space */
    overflow-y: hidden; /* Prevent mainContent from scrolling; let MarkdownDisplay handle scrolling */
    display: flex;
    flex-direction: column;
    gap: 16px; /* Space between elements */
}

.workflowsTitle {
    font-weight: 600; /* Bold font weight */
    color: #1890ff; /* Ant Design primary color */
    align-self: flex-start; /* Ensure title is left-aligned */
}

.description {
    margin-bottom: 24px; /* Space below description */
    font-size: 16px; /* Comfortable font size */
    color: #595959; /* Softer text color */
    background-color: #f6f6f6; /* Light grey background */
    padding: 4px; /* Padding inside the description box */
    border-radius: 4px; /* Rounded corners */
    border-left: 4px solid #1890ff; /* Accent border for emphasis */
}

.chatInputContainer {
    flex: 0 0 auto; /* Prevent chat input from growing */
    margin-top: 16px; /* Space above ChatInput */
    width: 100%; /* Ensure ChatInput spans full width */
}

/* MarkdownDisplay Styling */
.markdownDisplay {
    flex: 1 1 auto; /* Allow MarkdownDisplay to take up remaining space */
    overflow-y: auto; /* Ensure scrolling is handled within MarkdownDisplay */
    background-color: #fafafa; /* Light background */
    border: 1px solid #d9d9d9; /* Light border */
    border-radius: 8px; /* Rounded corners */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* Inner shadow for depth */
}

/* Responsive Design */
@media (max-width: 768px) {
    .workflowsContainer {
        padding: 16px; /* Reduce padding on smaller screens */
    }
    .description {
        font-size: 14px; /* Smaller text for description */
        padding: 12px;
    }

    .chatInputContainer {
        margin-top: 16px; /* Adjust margin for smaller screens */
    }
}
