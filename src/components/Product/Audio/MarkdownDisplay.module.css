.markdownDisplay {
    height: 100%;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative; /* 确保内部绝对定位相对于此容器 */
}

.mainLayout {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
}

.collapsibleSection {
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
}

.collapsibleHeader {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    user-select: none;
}

.collapsibleHeader:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.collapsibleContent {
    background-color: white;
    overflow-y: auto;
    transition: max-height 0.3s ease;
}

.collapsibleContent.expanded {
    max-height: 300px;
    padding: 2px;
    font-size: 14px;
}

.collapsibleContent.collapsed {
    max-height: 0;
    padding: 0 2px;
    overflow: hidden;
}

.contentContainer {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: white;
    position: relative; /* 确保子元素的绝对定位相对于此容器 */
}

.markdownContent {
    height: 100%;
}

.markdownContent > * {
    margin-bottom: 1rem;
}

.markdownContent h1 {
    font-size: 2rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.markdownContent h2 {
    font-size: 1.5rem;
    margin-top: 1.75rem;
    margin-bottom: 0.875rem;
}

.markdownContent h3 {
    font-size: 1.25rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.markdownContent h4, 
.markdownContent h5, 
.markdownContent h6 {
    font-size: 1.125rem;
    margin-top: 1.25rem;
    margin-bottom: 0.625rem;
}

.markdownContent p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

.markdownContent ul,
.markdownContent ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.markdownContent li {
    margin-bottom: 0.5rem;
}

.markdownContent code {
    background-color: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}

.markdownContent pre {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin-bottom: 1rem;
}

.loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
}

.emptyState {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #a0a0a0;
    font-size: 16px;
    text-align: center;
    padding: 32px 0;
}

/* 滚动条样式 */
.contentContainer::-webkit-scrollbar,
.collapsibleContent::-webkit-scrollbar {
    width: 6px;
}

.contentContainer::-webkit-scrollbar-track,
.collapsibleContent::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 3px;
}

.contentContainer::-webkit-scrollbar-thumb,
.collapsibleContent::-webkit-scrollbar-thumb {
    background: #d0d0d0;
    border-radius: 3px;
}

.contentContainer::-webkit-scrollbar-thumb:hover,
.collapsibleContent::-webkit-scrollbar-thumb:hover {
    background: #c0c0c0;
}

/* 更新后的复制按钮样式 */
.copyButton {
    position: absolute;
    bottom: 16px; /* 相对于 mainLayout 的 padding */
    right: 16px;
    display: flex;
    align-items: center;
    background-color: white; /* 白色背景 */
    border: 1px solid black; /* 黑色边框 */
    cursor: pointer;
    color: black; /* 黑色文字和图标 */
    font-size: 12px;
    padding: 4px 8px; /* 增加上下左右内边距，使按钮更宽 */
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
    transition: background-color 0.2s, color 0.2s;
    z-index: 10; /* 确保按钮位于内容之上 */
}

.copyButton:hover {
    background-color: #f0f0f0; /* 悬停时浅灰色背景 */
    /* color 保持黑色或根据需要调整 */
}

/* 可选：调整 copyText 的间距 */
.copyText {
    margin-left: 4px;
}
.markdownDisplay {
    height: 100%;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative; /* 确保内部绝对定位相对于此容器 */
}

.mainLayout {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
}

.collapsibleSection {
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
}

.collapsibleHeader {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    user-select: none;
}

.collapsibleHeader:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.collapsibleContent {
    background-color: white;
    overflow-y: auto;
    transition: max-height 0.3s ease;
}

.collapsibleContent.expanded {
    max-height: 300px;
    padding: 2px;
    font-size: 14px;
}

.collapsibleContent.collapsed {
    max-height: 0;
    padding: 0 2px;
    overflow: hidden;
}

.contentContainer {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: white;
    position: relative; /* 确保子元素的绝对定位相对于此容器 */
}

.markdownContent {
    height: 100%;
}

.markdownContent > * {
    margin-bottom: 1rem;
}

.markdownContent h1 {
    font-size: 2rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.markdownContent h2 {
    font-size: 1.5rem;
    margin-top: 1.75rem;
    margin-bottom: 0.875rem;
}

.markdownContent h3 {
    font-size: 1.25rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.markdownContent h4, 
.markdownContent h5, 
.markdownContent h6 {
    font-size: 1.125rem;
    margin-top: 1.25rem;
    margin-bottom: 0.625rem;
}

.markdownContent p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

.markdownContent ul,
.markdownContent ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
}

.markdownContent li {
    margin-bottom: 0.5rem;
}

.markdownContent code {
    background-color: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}

.markdownContent pre {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin-bottom: 1rem;
}

.loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
}

.emptyState {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #a0a0a0;
    font-size: 16px;
    text-align: center;
    padding: 32px 0;
}

/* 滚动条样式 */
.contentContainer::-webkit-scrollbar,
.collapsibleContent::-webkit-scrollbar {
    width: 6px;
}

.contentContainer::-webkit-scrollbar-track,
.collapsibleContent::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 3px;
}

.contentContainer::-webkit-scrollbar-thumb,
.collapsibleContent::-webkit-scrollbar-thumb {
    background: #d0d0d0;
    border-radius: 3px;
}

.contentContainer::-webkit-scrollbar-thumb:hover,
.collapsibleContent::-webkit-scrollbar-thumb:hover {
    background: #c0c0c0;
}

/* 更新后的复制按钮样式 */
.copyButton {
    position: absolute;
    bottom: 16px; /* 相对于 mainLayout 的 padding */
    right: 16px;
    display: flex;
    align-items: center;
    background-color: white; /* 白色背景 */
    border: 1px solid black; /* 黑色边框 */
    cursor: pointer;
    color: black; /* 黑色文字和图标 */
    font-size: 12px;
    padding: 4px 8px; /* 增加上下左右内边距，使按钮更宽 */
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
    transition: background-color 0.2s, color 0.2s;
    z-index: 10; /* 确保按钮位于内容之上 */
}

.copyButton:hover {
    background-color: #f0f0f0; /* 悬停时浅灰色背景 */
    /* color 保持黑色或根据需要调整 */
}

/* 可选：调整 copyText 的间距 */
.copyText {
    margin-left: 4px;
}
