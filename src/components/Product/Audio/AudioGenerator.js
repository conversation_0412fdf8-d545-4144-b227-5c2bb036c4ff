// src/components/Product/Audio/AudioGenerator.js
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spin, message, notification } from 'antd';
import AudioPlayer from './AudioPlayer';
import { MANAGER_API_BASE_URL, TTS_API_BASE_URL, ARTICLE_ENDPOINT } from '../../Configs/Config';
import { useSelector } from 'react-redux';
import { createData, updateData } from '../../Routers/Router';
import styles from './AudioGenerator.module.css';

const AudioGenerator = ({ script, onAudioLoadingChange, generateData, setIsGenerating }) => {
    const [loadingAudio, setLoadingAudio] = useState(false);
    const [audioUrl, setAudioUrl] = useState('');
    const { user_info } = useSelector((state) => state.user);

    // Notify parent about audio loading state
    useEffect(() => {
        if (onAudioLoadingChange) {
            onAudioLoadingChange(loadingAudio);
        }
        if (setIsGenerating) {
            setIsGenerating(loadingAudio);
        }
    }, [loadingAudio, onAudioLoadingChange, setIsGenerating]);

    // 构建 WebSocket URL
    let WEBSOCKET_URL = '';
    try {
        const managerApiUrl = new URL(MANAGER_API_BASE_URL);
        const protocol = managerApiUrl.protocol === 'https:' ? 'wss:' : 'ws:';
        WEBSOCKET_URL = `${protocol}//${managerApiUrl.host}/api/v1/chat/`;
        console.log('WebSocket URL:', WEBSOCKET_URL);
    } catch (error) {
        console.error('Invalid MANAGER_API_BASE URL:', error);
        message.error('Invalid API URL configuration');
    }

    const handleGenerateAudio = async () => {
        if (!script) {
            notification.warning({
                message: '生成音频失败',
                description: '请先生成脚本',
                duration: 3
            });
            return;
        }

        // 解析脚本
        let parsedScript;
        console.log(script, 'jsonString');
        try {
            // 正则表达式提取 ```json 和 ```
            const jsonRegex = /```json\s*([\s\S]*?)\s*```/;
            const match = script.match(jsonRegex);
            let jsonString;

            if (match && match[1]) {
                jsonString = match[1];
            } else {
                // 如果没有代码块，尝试解析整个脚本
                jsonString = script;
            }

            // 尝试解析 JSON 字符串
            parsedScript = JSON.parse(jsonString);

            // 验证 parsedScript 是否为包含 'role' 和 'content' 的对象数组
            if (
                !Array.isArray(parsedScript) ||
                !parsedScript.every(
                    (item) =>
                        typeof item.role === 'string' &&
                        typeof item.content === 'string'
                )
            ) {
                throw new Error('脚本格式不符合要求');
            }
        } catch (error) {
            console.error('Script parsing error:', error);

            // 使用 notification 提供更详细的错误信息
            notification.error({
                message: '脚本解析错误',
                description: `脚本格式无效，请重新生成内容。详细错误：${error.message}`,
                duration: 5 // 延长通知显示时间
            });
            return; // 解析失败，退出函数
        }

        setLoadingAudio(true);
        setAudioUrl('');

        try {
            const endpoint_api = `${TTS_API_BASE_URL}/predict`;
            console.log(parsedScript);
            const audioResponse = await createData(endpoint_api, parsedScript);
            console.log(audioResponse.text);
            setAudioUrl(audioResponse?.url);

            // 更新 generateData.url
            let updatedUrls = [];

            if (generateData.url && Array.isArray(generateData.url)) {
                updatedUrls = [...generateData.url, { url: audioResponse.url, type: "audio" }];
            } else {
                updatedUrls = [{ url: audioResponse.url, type: "audio" }];
            }

            const article_endpoint = `${ARTICLE_ENDPOINT}/${generateData.id}`;
            console.log(article_endpoint, { url: updatedUrls })
            await updateData(article_endpoint, { url: updatedUrls });
            message.success('音频生成成功！');
        } catch (error) {
            console.error('Audio generation error:', error);

            // 使用 notification 提供更详细的音频生成错误信息
            notification.error({
                message: '音频生成失败',
                description: `无法生成音频，请稍后重试。错误信息：${error.message}`,
                duration: 5
            });
        } finally {
            setLoadingAudio(false);
        }
    };

    return (
        <div className={styles.audioGenerator}>
            <Button
                type="primary"
                onClick={handleGenerateAudio}
                disabled={!script || loadingAudio}
                loading={loadingAudio}
                block
            >
                生成音频
            </Button>
            <div className={styles.audioContainer}>
                {loadingAudio ? (
                    <div className={styles.spinContainer}>
                        <Spin tip="生成音频中..." />
                    </div>
                ) : (
                    audioUrl && <AudioPlayer url={audioUrl} />
                )}
            </div>
        </div>
    );    
};

export default AudioGenerator;
