/* src/Components/PodcastManager/PodcastManager.module.css */

.podcastManagerContainer {
    display: flex;
    flex-direction: column; /* 垂直布局，包含头部和内容 */
    height: 100vh;
    padding: 16px 2.5%;
    box-sizing: border-box;
    position: relative;
}

.header {
    display: flex;
    flex-direction: row; /* 水平排列 */
    align-items: center; /* 垂直居中对齐 */
    justify-content: flex-start; /* 从左到右排列 */
    border-bottom: 1px solid #f0f0f0; /* 添加下边框分隔 */
    padding-bottom: 16px;
    gap: 16px; /* 在子元素之间添加间距 */
}

.backButton {
    padding: 8px 16px;
    border: none;
    background-color: hwb(204 5% 7%);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    color: hsl(180, 42%, 95%);
    gap: 8px;
    transition: background-color 0.2s;
}

.backButton:hover {
    background-color: hwb(97 42% 11%);
}

.backButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.mainContent {
    display: flex;
    flex: 1; /* 占据剩余空间 */
    gap: 24px;
    box-sizing: border-box;
    overflow: hidden; /* 防止溢出 */
}

.sidebar {
    width: 20%;
    border-right: 1px solid #f0f0f0;
    overflow-y: auto;
    box-sizing: border-box;
    padding-top: 60px;
}

.displayArea {
    flex: 1;
    /* padding-top: -30px; */
    overflow-y: auto;
    box-sizing: border-box;
}

.podcastPanel {
    width: 35%;
    border-left: 1px solid #f0f0f0;
    padding: 16px;
    overflow-y: auto;
    box-sizing: border-box;
}

.noSelection {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #a0a0a0;
}

.inputContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    padding: 10px 0;
}

.inputContainer h3 {
    margin-bottom: 16px;
    font-size: 1.2em;
    color: #333;
}

.contentInput {
    flex: 1;
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    resize: none;
    font-family: inherit;
    line-height: 1.5;
    box-sizing: border-box;
}

.contentInput:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* 专门针对该组件的 Tab 样式 */
.tabsContainer {
    flex: 1; /* 让 Tabs 占据剩余空间 */
}

.tabsContainer :global(.ant-tabs-nav) {
    height: 40px !important;
    margin: 0 !important;
    min-height: 40px !important;
    justify-content: center !important; /* 使 nav 居中 */
}

.tabsContainer :global(.ant-tabs-nav .ant-tabs-nav-wrap) {
    position: relative;
    display: flex;
    flex: unset !important; /* 取消 flex: auto */
    align-self: stretch;
    overflow: hidden;
    white-space: nowrap;
    transform: translate(0);
    justify-content: center; /* 居中对齐 */
}

.tabsContainer :global(.ant-tabs-nav-list) {
    height: 40px !important;
    padding: 0 !important;
    position: relative; /* 确保定位正确 */
}

.tabsContainer :global(.ant-tabs-tab) {
    padding: 8px 16px !important;
    margin: 0 !important;
    height: 40px !important;
    line-height: 24px !important;
}

.tabsContainer :global(.ant-tabs-content-holder) {
    display: none; /* 隐藏内容区域 */
}

.tabsContainer :global(.ant-tabs-content) {
    height: 0;
}

.tabsContainer :global(.ant-tabs-tabpane) {
    height: 0;
}

/* 针对 tab 内容区域的样式 */
.tabContent {
    height: 100%;
    overflow: auto;
    padding: 0px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mainContent {
        flex-direction: column;
    }

    .sidebar,
    .displayArea,
    .podcastPanel {
        width: 100%;
        border: none;
    }

    .header {
        flex-direction: column; /* 小屏幕下垂直排列 */
        align-items: flex-start;
    }

    .tabsContainer {
        width: 100%;
        margin-left: 0 !important;
        margin-top: 16px; /* 在小屏幕下 Tabs 与 backButton 之间添加间距 */
    }
}
