/* src/Components/PodcastManager/AgentComponent.module.css */

.agentContainer {
    display: flex;
    flex-direction: column;
    padding: 4px; /* 增加内边距以获得更好的间距 */
    background-color: #ffffff; /* 白色背景，干净整洁 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 细微阴影，增加深度感 */
    border-radius: 8px; /* 圆角 */
    width: 100%;
    height: 100%;
    box-sizing: border-box; /* 确保内边距和边框不会影响元素的总宽高 */
}

.header {
    flex: 0 0 auto; /* 固定头部，不随内容增长 */
    margin-bottom: 24px; /* 头部下方间距 */
}

.agentTitle {
    font-size: 1.8em; /* 更大的字体以突出显示 */
    font-weight: 600; /* 加粗字体 */
    margin-bottom: 16px; /* 增加下边距 */
    color: #52c41a; /* Ant Design 成功色 */
}

.selectContainer {
    width: 100%;  /* 确保下拉框占满全宽 */
    padding: 2px;
    /* align-self: start; */
}

.description {
    margin-bottom: 24px; /* 描述下方间距 */
    font-size: 16px; /* 舒适的字体大小 */
    color: #595959; /* 较柔和的文字颜色 */
    line-height: 1.5; /* 改善可读性 */
    background-color: #f6ffed; /* 浅绿色背景 */
    padding: 2px; /* 描述框内边距 */
    border-radius: 4px; /* 圆角 */
    border-left: 4px solid #52c41a; /* 左侧边框强调 */
}

.examplesContainer {
    margin-bottom: 24px; /* 示例下方间距 */
}

.mainContent {
    flex: 1 1 auto; /* 允许主内容区域增长并填充可用空间 */
    overflow-y: hidden; /* 防止主内容区域滚动，由 MarkdownDisplay 处理滚动 */
    display: flex;
    flex-direction: column;
    gap: 16px; /* 元素间距 */
}

.markdownDisplay {
    flex: 1 1 auto; /* 允许 MarkdownDisplay 占据剩余空间 */
    overflow-y: auto; /* 确保内部滚动 */
    background-color: #fafafa; /* 浅色背景 */
    border: 1px solid #d9d9d9; /* 浅色边框 */
    border-radius: 8px; /* 圆角 */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* 内部阴影，增加深度感 */
    padding: 16px; /* 可选内边距 */
}

.chatInputContainer {
    flex: 0 0 auto; /* 固定 ChatInput，不随内容增长 */
    margin-top: 16px; /* ChatInput 上方间距 */
    width: 100%; /* 确保 ChatInput 占满全宽 */
}

/* 响应式设计 */
@media (max-width: 768px) {
    .agentContainer {
        padding: 16px; /* 小屏幕减少内边距 */
    }

    .agentTitle {
        font-size: 1.5em; /* 略小的标题字体 */
    }

    .description {
        font-size: 14px; /* 较小的描述文本 */
        padding: 12px; /* 增加内边距 */
    }

    .chatInputContainer {
        margin-top: 16px; /* 调整小屏幕的上方间距 */
    }
}
