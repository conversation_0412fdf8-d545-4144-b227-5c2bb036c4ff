// src/Components/PodcastManager/PodcastManager.jsx

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Tabs } from 'antd';
import ArticleListSidebar from '../utils/ArticleListSidebar';
import PdfDisplay from '../../FileManager/PdfDisplay';
import PodcastGenerationPanel from './PodcastGenerationPanel';
import WorkflowsComponent from './WorkflowsComponent';
import AgentComponent from './AgentComponent';
import styles from './PodcastManager.module.css';
import { v4 as uuid } from 'uuid';

const PodcastManager = () => {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState(null);
  const [selectedSessionId, setSelectedSessionId] = useState(uuid());
  const [manualContent, setManualContent] = useState({ content: "" });
  const [isScriptGenerating, setIsScriptGenerating] = useState(false);
  const [isAudioGenerating, setIsAudioGenerating] = useState(false);
  const [isWorkflowGenerating, setIsWorkflowGenerating] = useState(false);
  const [isAgentGenerating, setIsAgentGenerating] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState('1');

  const isGenerating = isScriptGenerating || isAudioGenerating || isWorkflowGenerating || isAgentGenerating;

  const handleSelectFile = (file) => {
    setSelectedFile(file);
  };

  const handleContentChange = (e) => {
    setManualContent({ content: e.target.value });
  };
  const handleTabChange = (key) => {
    if (isGenerating) {
      window.alert('内容正在生成中，请稍后再尝试切换选项卡。');
      return;
    }
    
    // 生成新的 session ID
    setSelectedSessionId(uuid());
    setManualContent({ content: "" })
  
    if (key !== '1') {
      setSelectedFile(null);
    }
    setActiveTabKey(key);
  };
  
  const showSidebar = activeTabKey === '1';

  // 定义标签项，但不包含内容
  const tabItems = [
    {
      key: '1',
      label: 'PDF文件',
      disabled: isGenerating,
    },
    {
      key: '2',
      label: '自定义内容',
      disabled: isGenerating,
    },
    {
      key: '3',
      label: '工作流',
      disabled: isGenerating,
    },
    {
      key: '4',
      label: '智能体',
      disabled: isGenerating,
    },
  ];

  // 根据活动标签渲染内容
  const renderTabContent = () => {
    switch (activeTabKey) {
      case '1':
        return (
          <div className={styles.tabContent}>
            {selectedFile ? (
              <PdfDisplay data={selectedFile} show_edit={false} />
            ) : (
              <div className={styles.noSelection}>
                <p>选择左边PDF文件，开始播客内容生成之旅</p>
              </div>
            )}
          </div>
        );
      case '2':
        return (
          <div className={styles.noSelection}>
            <div className={styles.inputContainer}>
              <h3>在下方输入文本</h3>
              <textarea
                value={manualContent.content}
                onChange={handleContentChange}
                placeholder="复制文本用于生成内容，开始播客内容生成之旅..."
                className={styles.contentInput}
                disabled={isGenerating}
              />
            </div>
          </div>
        );
      case '3':
        return (
          <WorkflowsComponent
            isGenerating={isGenerating}
            setManualContent={setManualContent}
            setIsGenerating={setIsWorkflowGenerating}
            selectedSessionId={selectedSessionId}
            setSelectedSessionId={setSelectedSessionId}
          />
        );
      case '4':
        return (
          <AgentComponent
            isGenerating={isGenerating}
            setManualContent={setManualContent}
            setIsGenerating={setIsAgentGenerating}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.podcastManagerContainer}>
      <div className={styles.header}>
        <button
          className={styles.backButton}
          onClick={() => navigate(-1)}
          disabled={isGenerating}
        >
          ← 返回产品列表
        </button>
        {/* 将 Tabs 放在 header 中 */}
        <Tabs
          activeKey={activeTabKey}
          onChange={handleTabChange}
          className={styles.tabsContainer}
          items={tabItems}
          // 隐藏内容区域
          tabBarStyle={{ marginLeft: '16px' }} // 根据需要调整间距
        />
      </div>

      <div className={styles.mainContent}>
        {showSidebar && (
          <div className={styles.sidebar}>
            <ArticleListSidebar
              onSelectArticle={handleSelectFile}
              selectedDoc={selectedFile}
              disabled={isGenerating}
            />
          </div>
        )}

        <div className={styles.displayArea}>
          {renderTabContent()}
        </div>

        <div className={styles.podcastPanel}>
          <PodcastGenerationPanel
            selectedFile={selectedFile}
            manualContent={manualContent}
            onScriptLoadingChange={setIsScriptGenerating}
            onAudioLoadingChange={setIsAudioGenerating}
            isGenerating={isGenerating}
            setManualContent={setManualContent}
            setIsScriptGenerating={setIsScriptGenerating}
            setIsAudioGenerating={setIsAudioGenerating}
            selectedSessionId={selectedSessionId}
            setSelectedSessionId={setSelectedSessionId}
          />
        </div>
      </div>
    </div>
  );
};

export default PodcastManager;
