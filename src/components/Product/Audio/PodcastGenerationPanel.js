import React, { useState, useEffect } from 'react';
import { Button, message, Select } from 'antd';
import MarkdownDisplay from './MarkdownDisplay';
import AudioGenerator from './AudioGenerator';
import ReactMarkdown from 'react-markdown';
import {
  API_BASE_URL,
  MANAGER_API_BASE_URL,
  ARTICLE_ENDPOINT,
  WORKFLOW_ENDPOINT,
} from '../../Configs/Config';
import { useSelector } from 'react-redux';
import { createData, fetchBulk, fetchData } from '../../Routers/Router';
import AudioPlayer from './AudioPlayer';
import styles from './PodcastGenerationPanel.module.css';

const PodcastGenerationPanel = ({
  selectedFile,
  manualContent,
  onScriptLoadingChange,
  onAudioLoadingChange,
  isGenerating,
  setIsScriptGenerating,
  setIsAudioGenerating,
  selectedSessionId,
}) => {
  const [loadingScript, setLoadingScript] = useState(false);
  const [script, setScript] = useState('');
  const { user_info } = useSelector((state) => state.user);
  const endpoint = `${ARTICLE_ENDPOINT}`;
  const [hasCreatedData, setHasCreatedData] = useState(false);
  const [generateData, setGenerateData] = useState(null);
  const [originArticles, setOriginArticles] = useState([]);
  const [audioUrls, setAudioUrls] = useState([]);
  const [queryBody, setQueryBody] = useState(null);
  const [WEBSOCKET_URL, setWEBSOCKET_URL] = useState('');
  const [generating, setGenerating] = useState(false);

  // New state variables
  const [workflowsData, setWorkflowsData] = useState([]);
  const [selectedWorkflowId, setSelectedWorkflowId] = useState(null);
  const [loading, setLoading] = useState(false); // For loading workflows

  // Fetch the workflows
  const getData = async () => {
    setLoading(true);
    try {
      const endpoint_api = `${WORKFLOW_ENDPOINT}/bulk?username=${user_info.username}`;
      const result = await fetchBulk(endpoint_api);
      setWorkflowsData(result.data);
    } catch (error) {
      console.error(error);
      message.error('获取工作流数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  // Fetch the original articles
  useEffect(() => {
    const fetchOriginArticles = async () => {
      if (selectedFile?.id) {
        try {
          const fetch_article_endpoint = `${ARTICLE_ENDPOINT}/get_articles_with_origin_article_id/${selectedFile.id}`;
          const response = await fetchData(fetch_article_endpoint);
          setOriginArticles(response);
        } catch (error) {
          console.error('Error fetching origin articles:', error);
          message.error('获取原始文章失败');
          setOriginArticles([]);
        }
      } else {
        setOriginArticles([]);
      }
    };

    fetchOriginArticles();
  }, [selectedFile]);

  useEffect(() => {
    setScript('');
  }, [selectedFile, manualContent]);

  // Filter and set audio URLs whenever originArticles change
  useEffect(() => {
    if (originArticles && Array.isArray(originArticles)) {
      const filteredAudio = originArticles.flatMap((article) =>
        article.url
          ? article.url
              .filter((item) => item.type === 'audio')
              .map((item) => item.url) // Ensure it's a string URL
          : []
      );
      setAudioUrls(filteredAudio);
    } else {
      setAudioUrls([]);
    }
  }, [originArticles]);

  useEffect(() => {
    onScriptLoadingChange(loadingScript);
    if (setIsScriptGenerating) {
      setIsScriptGenerating(loadingScript);
    }
  }, [loadingScript, onScriptLoadingChange, setIsScriptGenerating]);

  const createPodcastData = async (responseData) => {
    const payload = {
      article_id: selectedFile?.id,
      content_id: responseData.id,
      source_text: manualContent?.content,
    };
    console.log(payload, 'payload');
    try {
      const response = await createData(endpoint, payload);
      setGenerateData(response);
      console.log('Data created successfully:', response);
      message.success('数据创建成功！');
    } catch (error) {
      console.error('Error creating data:', JSON.stringify(error, null, 2));
      message.error('数据创建失败。');
    }
    setHasCreatedData(false);
  };

  const handleStreamingComplete = (data) => {
    if (!hasCreatedData) {
      setHasCreatedData(true);
      createPodcastData(data);
    }
    setGenerating(false);
    setLoadingScript(false);
  };

  const handleGenerateScript = () => {
    const content = manualContent?.content || selectedFile?.content;
    if (!content) {
      message.warning('请输入手动内容或选择包含内容的文件。');
      return;
    }

    if (!selectedWorkflowId) {
      message.warning('请选择工作流。');
      return;
    }

    setLoadingScript(true);
    setGenerating(true);

    const workflow_id = selectedWorkflowId; // Use selected workflow ID
    const username = user_info.username;

    const queryBody = {
      selected_workflow: workflow_id,
      content: content,
      username: username,
      api_base_url: API_BASE_URL,
      manager_api_base_url: MANAGER_API_BASE_URL,
      attachments: [],
      session_id: selectedSessionId,
    };

    let WEBSOCKET_URL;
    try {
      const managerApiUrl = new URL(MANAGER_API_BASE_URL);
      const protocol = managerApiUrl.protocol === 'https:' ? 'wss:' : 'ws:';
      WEBSOCKET_URL = `${protocol}//${managerApiUrl.host}/api/v1/chat/`;
      setWEBSOCKET_URL(WEBSOCKET_URL);
    } catch (error) {
      console.error('Invalid MANAGER_API_BASE URL:', error);
      message.error('API URL 配置无效');
      setGenerating(false);
      setLoadingScript(false);
      return;
    }
    setQueryBody(queryBody);
    setScript('');
  };

  return (
    <div className={styles.podcastGenerationPanel}>
      {/* Workflow Selection */}
      <div className={styles.selectWorkflow}>
        <Select
          loading={loading}
          placeholder="请选择播客工作流"
          onChange={(value) => setSelectedWorkflowId(value)}
          value={selectedWorkflowId}
          style={{ width: '100%', marginBottom: '16px' }}
        >
          {workflowsData.map((workflow) => (
            <Select.Option key={workflow.id} value={workflow.id}>
              {workflow.name}
            </Select.Option>
          ))}
        </Select>
      </div>

      {/* Generate Script Button */}
      <Button
        type="primary"
        onClick={handleGenerateScript}
        disabled={
          (!manualContent.content && !selectedFile) ||
          loadingScript ||
          isGenerating || !selectedWorkflowId
        }
        loading={loadingScript}
        block
      >
        生成播客脚本
      </Button>

      <div className={styles.markdownContainer}>
        {(generating || script) && queryBody && WEBSOCKET_URL ? (
          <MarkdownDisplay
            queryBody={queryBody}
            websocket_url={WEBSOCKET_URL}
            setManualContent={setScript}
            onStreamingComplete={handleStreamingComplete}
          />
        ) : (
          <ReactMarkdown>{script}</ReactMarkdown>
        )}
      </div>

      {/* Display existing audio URLs */}
      <div className={styles.audioContainer}>
        {audioUrls.length > 0 ? (
          <>
            <h3>已有音频文件:</h3>
            {audioUrls.map((audio, index) => (
              <AudioPlayer key={index} url={audio} />
            ))}
          </>
        ) : (
          <p>暂无音频文件。</p>
        )}
      </div>

      {!loadingScript && !generating && script && (
        <AudioGenerator
          script={script.content}
          onAudioLoadingChange={onAudioLoadingChange}
          generateData={generateData}
          setIsGenerating={setIsAudioGenerating}
        />
      )}
    </div>
  );
};

export default PodcastGenerationPanel;
