/* .dataSelectorContainer {
    padding: 16px;
}

.tableContainer {
    margin-top: 16px;
}

.loadingSpinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}


.ant-table-row:hover {
    cursor: pointer;
    background-color: #fafafa !important;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: #e6f7ff !important;
}

.ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: bold;
} */
