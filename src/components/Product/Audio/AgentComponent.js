// src/Components/PodcastManager/AgentComponent.jsx
import React, { useState, useEffect } from 'react';
import { Select, message, Typography } from 'antd';
import { fetchBulk } from '../../Routers/Router';
import { useDispatch, useSelector } from 'react-redux';
import { setAgents } from '../../Workflow/Agents/Agents/AgentSlice';
import {
  API_BASE_URL,
  MANAGER_API_BASE_URL,
  AGENT_ENDPOINT,
} from '../../Configs/Config';
import MarkdownDisplay from './MarkdownDisplay';
import ExamplesComponent from '../utils/ExamplesComponent';
import ChatInput from '../utils/ChatInput'; // 引入 ChatInput 组件
import styles from './AgentComponent.module.css';

const { Paragraph } = Typography;

const AgentComponent = ({
  isGenerating,
  setManualContent,
  setIsGenerating,
}) => {
  const [agents, setAgentsData] = useState([]);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const [selectedAgent, setSelectedAgent] = useState(null);
  const { user_info } = useSelector((state) => state.user);
  const [examples, setExamples] = useState([]);
  const [generating, setGenerating] = useState(false);
  const [queryBody, setQueryBody] = useState(null);
  const [WEBSOCKET_URL, setWEBSOCKET_URL] = useState('');
  const [generatedContent, setGeneratedContent] = useState('');
  const [inputContent, setInputContent] = useState('');

  // 获取代理数据
  const getData = async () => {
    setLoading(true);
    try {
      const endpoint_api = `${AGENT_ENDPOINT}/bulk`;
      const result = await fetchBulk(endpoint_api);
      setAgentsData(result.data);
      dispatch(setAgents(result.data));
    } catch (error) {
      console.error(error);
      message.error('获取代理数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  // 选择代理处理
  const handleAgentSelect = (agentId) => {
    const agent = agents.find((agent) => agent.id === agentId);
    setSelectedAgent(agent);
    setExamples(agent.examples || []);
    setGeneratedContent('');
    setQueryBody(null);
    setWEBSOCKET_URL('');
  };

  // 处理示例点击
  const handleExampleClick = (exampleText) => {
    setInputContent(exampleText);
  };

  // 处理发送消息
  const handleSendMessage = async ({ message, files }) => {
    if (!selectedAgent) {
      message.error('请选择一个代理');
      return;
    }

    if (!message && files.length === 0) {
      message.warning('请输入内容或上传文件用于生成');
      return;
    }

    setGenerating(true);
    setIsGenerating(true);

    const username = user_info.username;

    const queryBodyData = {
      selected_agent: selectedAgent.id,
      content: message,
      username: username,
      api_base_url: API_BASE_URL,
      manager_api_base_url: MANAGER_API_BASE_URL,
      attachments: files, // 假设文件为 URL
    };

    let websocketURL;
    try {
      const managerApiUrl = new URL(MANAGER_API_BASE_URL);
      const protocol = managerApiUrl.protocol === 'https:' ? 'wss:' : 'ws:';
      websocketURL = `${protocol}//${managerApiUrl.host}/api/v1/chat/`;
      setWEBSOCKET_URL(websocketURL);
    } catch (error) {
      console.error('Invalid MANAGER_API_BASE URL:', error);
      message.error('API URL 配置无效');
      setGenerating(false);
      setIsGenerating(false);
      return;
    }

    setQueryBody(queryBodyData);
    setGeneratedContent('');
  };

  // 处理流式内容完成
  const handleStreamingComplete = (contentData) => {
    if (contentData && contentData.content) {
      setManualContent({content:contentData.content});
      setGeneratedContent(contentData.content);
    } else {
      setManualContent({content:''});
      setGeneratedContent('');
    }
    setGenerating(false);
    setIsGenerating(false);
  };

  // 渲染代理选项
  const renderAgentOptions = () => {
    return agents.map((agent) => ({
      value: agent.id,
      label: agent.name,
    }));
  };

  return (
    <div className={styles.agentContainer}>
      <div className={styles.header}>
        <Select
          className={styles.selectContainer}
          placeholder="选择智能体"
          options={renderAgentOptions()}
          onChange={handleAgentSelect}
          value={selectedAgent?.id}
          disabled={isGenerating || generating}
          loading={loading}
        />
        {/* 显示描述 */}
        {selectedAgent && selectedAgent.description && (
          <Paragraph className={styles.description}>
            Agent描述: {selectedAgent.description}
          </Paragraph>
        )}
        {/* 显示示例 */}
        {examples.length > 0 && (
          <ExamplesComponent
            className={styles.examplesContainer}
            examples={examples}
            onExampleClick={handleExampleClick}
          />
        )}
      </div>

      {/* 主要内容区域 */}
      <div className={styles.mainContent}>
        {/* 始终显示 Markdown 内容 */}
        <MarkdownDisplay
          queryBody={queryBody}
          websocket_url={WEBSOCKET_URL}
          setManualContent={setManualContent}
          onStreamingComplete={handleStreamingComplete}
          isGenerating={generating || isGenerating}
        />
      </div>

      {/* ChatInput 固定在底部 */}
      <div className={styles.chatInputContainer}>
        <ChatInput 
          query={inputContent}
          onSendMessage={handleSendMessage}
        />
      </div>
    </div>
  );
};

export default AgentComponent;
