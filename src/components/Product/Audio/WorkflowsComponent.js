// src/Components/PodcastManager/WorkflowsComponent.jsx

import React, { useState, useEffect } from 'react';
import { Select, message, Typography } from 'antd';
import { fetchBulk } from '../../Routers/Router';
import { useDispatch, useSelector } from 'react-redux';
import { setWorkflows } from '../../Workflow/workflowSlice';
import {
  API_BASE_URL,
  MANAGER_API_BASE_URL,
  WORKFLOW_ENDPOINT,
} from '../../Configs/Config';
import MarkdownDisplay from './MarkdownDisplay';
import ExamplesComponent from '../utils/ExamplesComponent';
import ChatInput from '../utils/ChatInput'; // Ensure correct import
import styles from './WorkflowsComponent.module.css';
import { v4 as uuid } from 'uuid';
const { Paragraph } = Typography;

const WorkflowsComponent = ({
  isGenerating,
  setManualContent,
  setIsGenerating,
  selectedSessionId,
  setSelectedSessionId

}) => {
  const [workflows, setWorkflowsData] = useState([]);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const [selectedWorkflow, setSelectedWorkflow] = useState(null);
  const { user_info } = useSelector((state) => state.user);
  const [examples, setExamples] = useState([]);
  const [generating, setGenerating] = useState(false);
  const [queryBody, setQueryBody] = useState(null);
  const [WEBSOCKET_URL, setWEBSOCKET_URL] = useState('');
  const [generatedContent, setGeneratedContent] = useState('');
  const [inputContent, setInputContent] = useState('');

  // Fetch workflows data
  const getData = async () => {
    setLoading(true);
    try {
      const endpoint_api = `${WORKFLOW_ENDPOINT}/bulk?username=${user_info.username}`;
      const result = await fetchBulk(endpoint_api);
      setWorkflowsData(result.data);
      dispatch(setWorkflows(result.data));
    } catch (error) {
      console.error(error);
      message.error('获取工作流数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getData();
  }, []);

  // Handle workflow selection
  const handleWorkflowSelect = (workflowId) => {
    const selected = workflows.find(
      (workflow) => workflow.id === workflowId
    );
    setSelectedSessionId(uuid());
    setSelectedWorkflow(selected);
    setExamples(selected.examples || []);
    setGeneratedContent('');
    setQueryBody(null);
    setWEBSOCKET_URL('');
  };

  // Handle example click
  const handleExampleClick = (exampleText) => {
    setInputContent(exampleText);
  };

  // Handle sending message from ChatInput
  const handleSendMessage = async ({ message, files }) => {
    if (!selectedWorkflow) {
      message.error('请选择一个工作流');
      return;
    }

    if (!message && files.length === 0) {
      message.warning('请输入内容或上传文件用于生成');
      return;
    }

    setGenerating(true);
    setIsGenerating(true);

    const username = user_info.username;

    const queryBodyData = {
      selected_workflow: selectedWorkflow.id,
      content: message,
      username: username,
      api_base_url: API_BASE_URL,
      manager_api_base_url: MANAGER_API_BASE_URL,
      attachments: files, // Assuming files are URLs
      session_id:selectedSessionId,
    };

    let websocketURL;
    try {
      const managerApiUrl = new URL(MANAGER_API_BASE_URL);
      const protocol = managerApiUrl.protocol === 'https:' ? 'wss:' : 'ws:';
      websocketURL = `${protocol}//${managerApiUrl.host}/api/v1/chat/`;
      setWEBSOCKET_URL(websocketURL);
    } catch (error) {
      console.error('Invalid MANAGER_API_BASE URL:', error);
      message.error('API URL 配置无效');
      setGenerating(false);
      setIsGenerating(false);
      return;
    }

    setQueryBody(queryBodyData);
    setGeneratedContent('');

    // Optionally, you can handle sending the queryBody to some service here
    // Since `MarkdownDisplay` listens to `queryBody` and `websocket_url` changes,
    // it will handle the WebSocket connection and content display
  };

  // Handle completion of streaming from MarkdownDisplay
  const handleStreamingComplete = (contentData) => {
    if (contentData && contentData.content) {
      setManualContent({content:contentData.content});
      setGeneratedContent(contentData.content);
    } else {
      setManualContent({content:''});
      setGeneratedContent('');
    }
    setGenerating(false);
    setIsGenerating(false);
  };

  // Render workflow options for the Select component
  const renderWorkflowOptions = () => {
    console.log(workflows, 'workflows');
    return workflows.map((workflow) => ({
      value: workflow.id,
      label: workflow.name,
    }));
  };

  return (
    <div className={styles.workflowsContainer}>
      <div className={styles.header}>
        <Select
          style={{ width: '100%', marginBottom: '16px',
          }}
          placeholder="请选择工作流"
          options={renderWorkflowOptions()}
          onChange={handleWorkflowSelect}
          value={selectedWorkflow?.id}
          disabled={isGenerating || generating}
          loading={loading}
        />
        {/* Display Description */}
        {selectedWorkflow && selectedWorkflow.description && (
          <Paragraph className={styles.description}>
            工作流描述: {selectedWorkflow.description}
            <span className={styles.smallRedText}>(点击下方example进行对话)</span>
          </Paragraph>
        )}
        {/* Display Examples */}
        {examples.length > 0 && (
          <ExamplesComponent
            examples={examples}
            onExampleClick={handleExampleClick}
          />
        )}
      </div>

      {/* Main Content Area */}
      <div className={styles.mainContent}>
        {/* Always Display MarkdownContent */}
        <MarkdownDisplay
          queryBody={queryBody}
          websocket_url={WEBSOCKET_URL}
          setManualContent={setManualContent}
          onStreamingComplete={handleStreamingComplete}
          isGenerating={generating || isGenerating}
        />
      </div>

      {/* ChatInput fixed at the bottom */}
      <div className={styles.chatInputContainer}>
        <ChatInput 
          query={inputContent} // Reset query or handle as needed
          onSendMessage={handleSendMessage} // Pass the handler as a prop
        />
      </div>
    </div>
  );
};

export default WorkflowsComponent;
