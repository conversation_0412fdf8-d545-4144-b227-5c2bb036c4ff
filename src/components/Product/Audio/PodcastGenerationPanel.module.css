/* src/Components/PodcastManager/PodcastGenerationPanel.module.css */

/* Existing styles */
.podcastGenerationPanel {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.markdownContainer {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    padding: 8px;
    border-radius: 4px;
    background-color: #fafafa;
    min-height: 400px;
}

.audioContainer {
    margin-top: 16px;
}

/* New styles for audio URLs container */
.audioUrlsContainer {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background-color: #fafafa;
}

.audioUrlsContainer h3 {
    margin-bottom: 10px;
}

.audioUrlsContainer p {
    color: #888;
}

.audioUrlsContainer .audioPlayer {
    margin-bottom: 10px;
}
