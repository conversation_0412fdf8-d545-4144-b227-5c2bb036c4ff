import React, { useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { Typography, message } from 'antd';
import { ChevronDown, ChevronUp, Clipboard } from 'lucide-react';
import { chatClient } from '../../services/WebSocketService';
import styles from './MarkdownDisplay.module.css';
import clsx from 'clsx';
import LoadingSpinner from '../utils/LoadingSpinner';

const { Text } = Typography;

const CollapsibleSection = ({ content, isCollapsed, setIsCollapsed }) => {
  return (
    <div className={styles.collapsibleSection}>
      <div 
        className={styles.collapsibleHeader}
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <Text className="flex-1 font-medium">中间结果</Text>
        {isCollapsed ? <ChevronDown size={12} /> : <ChevronUp size={12} />}
      </div>
      <div className={clsx(
        styles.collapsibleContent,
        isCollapsed ? styles.collapsed : styles.expanded
      )}>
        <ReactMarkdown>{content}</ReactMarkdown>
      </div>
    </div>
  );
};

const MarkdownDisplay = ({
  queryBody,
  websocket_url,
  setManualContent,
  onStreamingComplete,
  isGenerating,
}) => {
  const [intermediateContent, setIntermediateContent] = useState('');
  const [finalContent, setFinalContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true);

  useEffect(() => {
    if (intermediateContent) {
      setIsCollapsed(false);
    }
  }, [intermediateContent]);

  useEffect(() => {
    let isMounted = true;
    let ws;

    const fetchData = async () => {
      if (!queryBody || !websocket_url) {
        setIntermediateContent('');
        setFinalContent('');
        if (onStreamingComplete) {
          onStreamingComplete(null);
        }
        return;
      }

      setLoading(true);

      try {
        ws = await chatClient({
          queryBody,
          websocket_url,
          onStream: (responseData) => {
            if (responseData?.content) {
              const newContentChunk = responseData.content
                .map(item => item.content)
                .join('');

              if (responseData.is_final_node) {
                // 只在 IS_FINAL_NODE 为 true 时检查 ROLE
                if (responseData.role === 'user') {
                  return; // 如果是 user 角色，直接跳过内容拼接
                }
                
                setFinalContent(prev => {
                  const updatedContent = prev + newContentChunk;
                  if (responseData.is_completed && isMounted) {
                    setTimeout(() => {
                      setManualContent({ content: updatedContent });
                      onStreamingComplete?.({
                        id: responseData.id,
                        content: updatedContent
                      });
                    }, 0);
                  }
                  return updatedContent;
                });
              } else {
                // 非 final node 的情况，直接更新 intermediate content
                setIntermediateContent(prev => prev + newContentChunk);
              }
            }
          },
        });
      } catch (error) {
        console.error('Error during streaming:', error);
        onStreamingComplete?.(null);
      } finally {
        setLoading(false);
      }
    };

    setIntermediateContent('');
    setFinalContent('');
    fetchData();

    return () => {
      isMounted = false;
      if (ws?.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [queryBody, websocket_url]);

  const handleCopy = async () => {
    if (!finalContent) {
      message.warning('没有内容可复制');
      return;
    }
    try {
      await navigator.clipboard.writeText(finalContent);
      message.success('内容已复制到剪贴板');
    } catch (err) {
      console.error('Failed to copy!', err);
      message.error('复制失败，请手动复制');
    }
  };

  return (
    <div className={styles.markdownDisplay}>
      <div className={styles.mainLayout}>
        {loading && (
          <LoadingSpinner size="small" />
        )}
        
        {!loading && !intermediateContent && !finalContent && (
          <div className={styles.emptyState}>
            <span>暂无内容，请选择工作流并生成内容。</span>
          </div>
        )}
        
        {intermediateContent && (
          <CollapsibleSection 
            content={intermediateContent}
            isCollapsed={isCollapsed}
            setIsCollapsed={setIsCollapsed}
          />
        )}
        
        <div className={styles.contentContainer}>
          <div className={styles.markdownContent}>
            {finalContent && (
              <ReactMarkdown>{finalContent}</ReactMarkdown>
            )}
          </div>
        </div>

        {/* 固定在右下角的复制按钮 */}
        {finalContent && (
          <button 
            className={styles.copyButton} 
            onClick={handleCopy}
            aria-label="复制内容"
          >
            <Clipboard size={12} />
            <span className={styles.copyText}>复制</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default MarkdownDisplay;
