// TTSGenerator.js
import React, { useState, useEffect, useRef } from 'react';
import {
  Input,
  Button,
  Checkbox,
  message,
  Space,
  Radio
} from 'antd';
import styles from './TTSGenerator.module.css';
import { MANAGER_API_BASE_URL, TTS_CLONE_API_BASE_URL, USER_ENDPOINT } from '../../../Configs/Config';
// import ReferenceAudio from './ReferenceAudio';
import { onlyUploadFile, fetchData, updateData } from '../../../Routers/Router';
import LoadingSpinner from '../../../utils/LoadingSpinner';
import ExistingAudioSelector from './ExistingAudioSelector';
import NewAudioUploaderRecorder from './NewAudioUploaderRecorder';

const { TextArea } = Input;

const TTSGenerator = () => {
  // State Management
  const [referenceAudio, setReferenceAudio] = useState({
    uid: '',
    name: '',
    url: '',
    file: null,
    isExisting: false,
  });
  const [referenceText, setReferenceText] = useState('');
  const [inputText, setInputText] = useState('');
  const [generatedAudio, setGeneratedAudio] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [normalize, setNormalize] = useState(true);
  const [activeTabKey, setActiveTabKey] = useState('1');
  const [isLoading, setIsLoading] = useState(false); // Loading state

  // Additional State Management
  const [userAudios, setUserAudios] = useState([]);
  const [loadingUserAudios, setLoadingUserAudios] = useState(true);
  const [selectedOption, setSelectedOption] = useState('new'); // 'new' or 'existing'

  // Reference to audio element
  const audioRef = useRef(null);

  // Cleanup object URLs on unmount or when referenceAudio changes
  useEffect(() => {
    return () => {
      if (referenceAudio && !referenceAudio.isExisting) {
        URL.revokeObjectURL(referenceAudio.url);
      }
    };
  }, [referenceAudio]);

  // Reset referenceAudio when activeTabKey or selectedOption changes
  useEffect(() => {
    if (selectedOption !== 'existing') { // Ensure we don't reset when using existing
      // Clear previous reference audio
      if (referenceAudio && !referenceAudio.isExisting) {
        URL.revokeObjectURL(referenceAudio.url);
      }
      setReferenceAudio({
        uid: '',
        name: '',
        url: '',
        file: null,
        isExisting: false,
      });

      // Update reference text based on activeTabKey
      if (activeTabKey === '2') {
        setReferenceText('糖尿病如何治疗');
      } else if (activeTabKey === '1') {
        setReferenceText('');
      }
    }
  }, [activeTabKey, selectedOption]);

  // Fetch user audios on mount
  useEffect(() => {
    const getUserData = async () => {
      try {
        const endpoint_api = `${USER_ENDPOINT}/query_by_name`;
        const userData = await fetchData(endpoint_api);
        if (userData && userData.audios) {
          setUserAudios(userData.audios);
        }
      } catch (error) {
        console.error('Failed to fetch user data:', error);
        message.error('无法获取用户数据');
      } finally {
        setLoadingUserAudios(false);
      }
    };

    getUserData();
  }, []);

  // Delete Reference Audio
  const deleteReferenceAudio = () => {
    if (referenceAudio) {
      if (!referenceAudio.isExisting) {
        URL.revokeObjectURL(referenceAudio.url);
      }
      setReferenceAudio({
        uid: '',
        name: '',
        url: '',
        file: null,
        isExisting: false,
      });
      message.info('参考音频已删除。');
    }
    if (selectedOption === 'new') {
      if (activeTabKey === '2') {
        setReferenceText('糖尿病如何治疗');
      } else {
        setReferenceText('');
      }
    }
  };

  // Generate TTS Function
  const generateTTS = async () => {
    if (!inputText) {
      message.warning('请输入要合成的文本');
      return;
    }
    setGeneratedAudio("");
    setIsLoading(true); // Start loading

    const requestData = {
      text: inputText,
      format: 'wav',
      normalize: normalize,
      latency: 'normal',
      streaming: isStreaming,
      references: []
    };
    let uploadedFileUrl = null; // Variable to store the uploaded URL

    // Handle references based on whether the audio is existing or new
    if (referenceAudio) {
      if (referenceAudio.isExisting) {
        // Use existing audio URL directly
        requestData.references.push({
          text: referenceText || '',
          audio: referenceAudio.url, // Assuming audio_url is stored in referenceAudio.url
        });
      } else if (referenceAudio.file) {
        try {
          // Upload the new audio file
          const endpoint_api = `${MANAGER_API_BASE_URL}/api/v1/file-managers/only_upload_file/`;
          const fileUrl = await onlyUploadFile(referenceAudio.file, endpoint_api);
          requestData.references.push({
            text: referenceText || '',
            audio: fileUrl,
          });
          uploadedFileUrl = fileUrl; // Save the uploaded URL
          // Update referenceAudio with the uploaded fileUrl
          setReferenceAudio(prev => ({
            ...prev,
            url: fileUrl,
          }));
        } catch (error) {
          message.error('参考音频上传失败');
          setIsLoading(false); // Stop loading
          return;
        }
      }
    }

    try {
      // Call the TTS API
      // console.log(requestData, 'requestData');
      const response = await fetch(`${TTS_CLONE_API_BASE_URL}/v1/tts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', // Changed to JSON format
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        const contentType = response.headers.get('Content-Type');
        if (contentType && contentType.startsWith('audio/')) {
          const audioBlob = await response.blob();
          const audioUrl = URL.createObjectURL(audioBlob);
          setGeneratedAudio(audioUrl);
          message.success('音频生成成功');

          // If the reference audio is new, update user audios
          if (referenceAudio && !referenceAudio.isExisting && uploadedFileUrl) {
            // Proceed to update user audios
            const endpoint_api = `${USER_ENDPOINT}/audios`;
            const payload = {
              audio_url: uploadedFileUrl,
              text: referenceText || '',
            };
            console.log(payload, 'payload');
            await updateData(endpoint_api, payload);
          }
        } else {
          const errorText = await response.text();
          console.error('音频生成失败，返回的内容类型不正确:', contentType, errorText);
          message.error('音频生成失败，返回的内容类型不正确');
        }
      } else {
        const errorText = await response.text();
        console.error('音频生成失败:', errorText);
        message.error('音频生成失败');
      }
    } catch (error) {
      console.error('TTS 生成错误:', error);
      message.error('生成音频时出错');
    } finally {
      setIsLoading(false); // Stop loading
    }
  };

  return (
    <div className={styles.container}>
      <h2 className={styles.title}>文本转语音生成器</h2>

      {/* 输入文本 */}
      <div className={styles.synthesisTextContainer}>
        <TextArea
          placeholder="输入要合成的文本"
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          rows={4}
        />
      </div>

      {/* 选择已有音频或上传/录制新音频 */}
      <div className={styles.selectionContainer} style={{ marginTop: 16 }}>
        <Radio.Group
          onChange={(e) => {
            setSelectedOption(e.target.value);
            if (e.target.value === 'existing') {
              // Optionally, clear referenceAudio to force selection
            } else {
              // Clear selection
              setReferenceAudio({
                uid: '',
                name: '',
                url: '',
                file: null,
                isExisting: false,
              });
              setReferenceText('');
            }
          }}
          value={selectedOption}
        >
          <Radio value="existing">使用已有的克隆音频</Radio>
          <Radio value="new">上传或录制新的音频</Radio>
        </Radio.Group>
      </div>

      {/* 选择已有音频 */}
      {selectedOption === 'existing' && (
        <ExistingAudioSelector
          userAudios={userAudios}
          loadingUserAudios={loadingUserAudios}
          onSelect={(value) => {
            const selected = userAudios.find(audio => audio.audio_url === value);
            if (selected) {
              // Set referenceAudio as existing audio
              const newReferenceAudio = {
                uid: Date.now().toString(),
                name: `Existing_${selected.text || 'Audio'}.wav`,
                url: selected.audio_url,
                file: null,
                isExisting: true,
              };
              setReferenceAudio(newReferenceAudio);
              setReferenceText(selected.text || '');
              message.success('已选择已有的参考音频。');
            }
          }}
        />
      )}

      {/* 上传/录制新音频的组件 */}
      {selectedOption === 'new' && (
        <div style={{ marginTop: 16 }}>
          <NewAudioUploaderRecorder
            referenceAudio={referenceAudio}
            setReferenceAudio={setReferenceAudio}
            activeTabKey={activeTabKey}
            setActiveTabKey={setActiveTabKey} // Pass the setter function here
            setReferenceText={setReferenceText}
          />
        </div>
      )}

      {/* 新音频的参考文本输入 */}
      {selectedOption === 'new' && (
        <div className={styles.referenceTextContainer} style={{ marginTop: 16 }}>
          <Input
            placeholder="参考文本"
            value={referenceText}
            onChange={(e) => setReferenceText(e.target.value)}
            disabled={selectedOption === 'existing'}
          />
        </div>
      )}

      {/* 选项：归一化和流式处理 */}
      <Space style={{ marginTop: 16 }}>
        <Checkbox
          checked={normalize}
          onChange={(e) => setNormalize(e.target.checked)}
        >
          归一化
        </Checkbox>
        <Checkbox
          checked={isStreaming}
          onChange={(e) => setIsStreaming(e.target.checked)}
        >
          流式处理
        </Checkbox>
      </Space>

      {/* 生成和播放音频 */}
      <div className={styles.generateContainer}>
        <Button
          type="primary"
          onClick={generateTTS}
          style={{ marginTop: 16 }}
          disabled={isLoading || (selectedOption === 'existing' && !referenceAudio)}
        >
          生成语音
        </Button>

        {isLoading && (
          <div style={{ marginTop: 16 }}>
            <LoadingSpinner />
          </div>
        )}

        {!isLoading && generatedAudio && (
          <Space style={{ marginTop: 16 }}>
            <audio ref={audioRef} src={generatedAudio} controls />
          </Space>
        )}
      </div>
    </div>
  );
};

export default TTSGenerator;
