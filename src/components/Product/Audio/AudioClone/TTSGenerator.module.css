.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .title {
    text-align: center;
    margin-bottom: 24px;
  }
  
  .synthesisTextContainer {
    margin-bottom: 16px;
  }
  
  .dragger {
    padding: 24px;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
  }
  
  .audioPreviewContainer {
    margin-top: 16px;
  }
  
  .audioPreview {
    margin-bottom: 16px;
  }
  
  .recordedAudioContainer {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .generateContainer {
    text-align: center;
  }
  