// NewAudioUploaderRecorder.js
import React, { useState, useRef } from 'react';
import { Upload, Button, Tabs, message } from 'antd';
import { AudioMutedOutlined, AudioOutlined, UploadOutlined } from '@ant-design/icons';
import ReferenceAudio from './ReferenceAudio';
import styles from './TTSGenerator.module.css';
import PropTypes from 'prop-types';

const { Dragger } = Upload;

const NewAudioUploaderRecorder = ({
  referenceAudio,
  setReferenceAudio,
  activeTabKey,
  setActiveTabKey, // Destructure the setter function
  setReferenceText
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);

  // Start Recording
  const startRecording = async () => {
    try {
      // Clear previous reference audio
      if (referenceAudio && !referenceAudio.isExisting) {
        URL.revokeObjectURL(referenceAudio.url);
        setReferenceAudio({
          uid: '',
          name: '',
          url: '',
          file: null,
          isExisting: false,
        });
      }

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        const newRecordedAudio = {
          uid: Date.now().toString(),
          name: 'Recording.wav',
          url: audioUrl,
          file: audioBlob,
          isExisting: false, // Mark as new
        };

        setReferenceAudio(newRecordedAudio);
        setIsRecording(false);
        setReferenceText(''); // Optionally, set a default or clear
        message.success('录音完成。');
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      message.info('录音开始...');
    } catch (error) {
      console.error('录音错误:', error);
      message.error('无法开始录音');
    }
  };

  // Stop Recording
  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }
  };

  // Handle Reference Audio Upload
  const handleReferenceAudioUpload = (file) => {
    // Clear previous reference audio
    if (referenceAudio && !referenceAudio.isExisting) {
      URL.revokeObjectURL(referenceAudio.url);
    }

    const audioUrl = URL.createObjectURL(file);

    const newAudio = {
      uid: file.uid,
      name: file.name,
      url: audioUrl,
      file: file,
      isExisting: false, // Mark as new
    };

    setReferenceAudio(newAudio);
    setReferenceText(''); // Optionally, set a default or clear
    message.success(`${file.name} 文件上传成功。`);
    return false; // Prevent automatic upload
  };

  // Define the tabs using the `items` prop
  const tabItems = [
    {
      key: '1',
      label: '上传参考音频',
      children: (
        <Dragger
          multiple={false}
          accept="audio/*"
          beforeUpload={handleReferenceAudioUpload}
          className={styles.dragger}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域进行上传</p>
          <p className="ant-upload-hint">
            仅支持单个音频文件上传。
          </p>
        </Dragger>
      ),
    },
    {
      key: '2',
      label: '录制参考音频',
      children: (
        <Button
          type={isRecording ? 'danger' : 'primary'}
          onClick={isRecording ? stopRecording : startRecording}
          icon={isRecording ? <AudioMutedOutlined /> : <AudioOutlined />}
          disabled={false}
        >
          {isRecording ? '停止录音' : '开始录音'}
        </Button>
      ),
    },
  ];

  return (
    <div>
      <Tabs
        activeKey={activeTabKey}
        onChange={(key) => {
          setActiveTabKey(key); // Use the setter function passed as a prop
          // Reset reference audio and reference text when switching tabs
          setReferenceAudio({
            uid: '',
            name: '',
            url: '',
            file: null,
            isExisting: false,
          });
          setReferenceText(key === '2' ? '糖尿病如何治疗' : '');
        }}
        style={{ marginTop: 16 }}
        type="card"
        items={tabItems}
      />

      {/* Display ReferenceAudio component if audio is selected/uploaded */}
      {referenceAudio && referenceAudio.url && (
        <ReferenceAudio
          referenceAudio={referenceAudio}
          onDelete={() => {
            // Handle deletion
            URL.revokeObjectURL(referenceAudio.url);
            setReferenceAudio({
              uid: '',
              name: '',
              url: '',
              file: null,
              isExisting: false,
            });
            setReferenceText(activeTabKey === '2' ? '糖尿病如何治疗' : '');
            message.info('参考音频已删除。');
          }}
        />
      )}
    </div>
  );
};

// Define PropTypes for better type checking
NewAudioUploaderRecorder.propTypes = {
  referenceAudio: PropTypes.shape({
    uid: PropTypes.string,
    name: PropTypes.string,
    url: PropTypes.string,
    file: PropTypes.object,
    isExisting: PropTypes.bool,
  }),
  setReferenceAudio: PropTypes.func.isRequired,
  activeTabKey: PropTypes.string.isRequired,
  setActiveTabKey: PropTypes.func.isRequired, // Add PropTypes for the setter
  setReferenceText: PropTypes.func.isRequired,
};

export default NewAudioUploaderRecorder;
