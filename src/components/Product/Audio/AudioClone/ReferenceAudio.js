import React, { useRef } from 'react';
import { Button, Space } from 'antd';
import { PlayCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import styles from './TTSGenerator.module.css';

const ReferenceAudio = ({ referenceAudio, onDelete }) => {
  const audioRef = useRef(null);

  return (
    <div className={styles.audioPreviewContainer}>
      <audio ref={audioRef} src={referenceAudio.url} controls />
      <Space style={{ marginTop: 8 }}>
        <Button
          icon={<DeleteOutlined />}
          danger
          onClick={onDelete}
        >
          删除
        </Button>
      </Space>
    </div>
  );
};

export default ReferenceAudio;
