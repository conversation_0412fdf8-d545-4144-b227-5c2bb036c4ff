// ExistingAudioSelector.js
import React from 'react';
import { Select } from 'antd';
import LoadingSpinner from '../../../utils/LoadingSpinner';

const { Option } = Select;

const ExistingAudioSelector = ({ userAudios, loadingUserAudios, onSelect }) => {
  return (
    <div style={{ marginTop: 16 }}>
      {loadingUserAudios ? (
        <LoadingSpinner />
      ) : userAudios.length > 0 ? (
        <Select
          placeholder="选择一个参考音频"
          style={{ width: '100%' }}
          onChange={(value) => onSelect(value)}
        >
          {userAudios.map((audio, index) => (
            <Option key={index} value={audio.audio_url}>
              {audio.audio_url}
            </Option>
          ))}
        </Select>
      ) : (
        <p>您还没有克隆的音频。请上传或录制新的音频。</p>
      )}
    </div>
  );
};

export default ExistingAudioSelector;
