.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.card {
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.addButton {
  margin-bottom: 16px;
}

/* 表格样式 */
:global(.ant-table-thead > tr > th) {
  background-color: #f7f7f7;
  font-weight: bold;
}

:global(.ant-table-tbody > tr:hover > td) {
  background-color: #f0f7ff;
}

/* 按钮间距 */
:global(.ant-space) {
  flex-wrap: wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  :global(.ant-table-wrapper) {
    overflow-x: auto;
  }
} 