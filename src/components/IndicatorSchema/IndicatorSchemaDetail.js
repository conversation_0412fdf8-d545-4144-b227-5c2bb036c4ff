import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, Table, Button, message, Popconfirm, Space, Input, 
  Form, Modal, Typography, Breadcrumb 
} from 'antd';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, 
  ArrowLeftOutlined, SaveOutlined
} from '@ant-design/icons';
import styles from './IndicatorSchemaDetail.module.css';
import { INDICATOR_SCHEMA_ENDPOINT } from '../Configs/Config';
import {fetchData, updateData } from '../Routers/Router';
const { Title } = Typography;

const IndicatorSchemaDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [indicatorSchema, setIndicatorSchema] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [schemaElements, setSchemaElements] = useState([]);
  const [currentElement, setCurrentElement] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [elementForm] = Form.useForm();

  // 获取指标Schema详情
  const fetchIndicatorSchemaDetail = async () => {
    try {
      const data = await fetchData(`${INDICATOR_SCHEMA_ENDPOINT}/${id}`);
      setIndicatorSchema(data);
      setSchemaElements(data.indicator_schema || []);
      // 清空选择
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('获取指标Schema详情失败:', error);
      message.error('获取指标Schema详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchIndicatorSchemaDetail();
  }, [id]);

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 批量删除选中的指标元素
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的指标');
      return;
    }

    if (!indicatorSchema || !indicatorSchema.indicator_schema) return;
    
    const newIndicatorSchema = { ...indicatorSchema };
    const newElements = [...indicatorSchema.indicator_schema];
    
    // 按索引降序排列，这样删除时不会影响前面元素的索引
    const sortedKeys = [...selectedRowKeys].sort((a, b) => b - a);
    
    sortedKeys.forEach(index => {
      newElements.splice(index, 1);
    });
    
    newIndicatorSchema.indicator_schema = newElements;
    
    setDeleteLoading(true);
    saveIndicatorSchema(newIndicatorSchema).then(() => {
      setSelectedRowKeys([]);
      setDeleteLoading(false);
    }).catch(() => {
      setDeleteLoading(false);
    });
  };

  // 保存指标Schema
  const saveIndicatorSchema = async (updatedSchema) => {
    if (updatedSchema.indicator_schema.length === 0) {
      message.warning('请至少添加一个指标元素');
      return;
    }
    
    try {
      setLoading(true);
      await updateData(`${INDICATOR_SCHEMA_ENDPOINT}/${id}`, updatedSchema);
      message.success('更新成功');
      await fetchIndicatorSchemaDetail();
    } catch (error) {
      console.error('更新指标Schema失败:', error);
      message.error('更新指标Schema失败');
    } finally {
      setLoading(false);
    }
  };

  // 编辑指标元素
  const handleEdit = (item, index) => {
    setCurrentElement({ ...item, index });
    elementForm.setFieldsValue(item);
    setIsModalVisible(true);
  };

  // 新增
  const handleAdd = () => {
    elementForm.resetFields();
    setIsModalVisible(true);
  };

  // 删除指标元素
  const handleDelete = (index) => {
    if (!indicatorSchema || !indicatorSchema.indicator_schema) return;
    
    const newElements = [...schemaElements];
    newElements.splice(index, 1);
    
    // 更新本地状态
    setSchemaElements(newElements);
    
    // 保存到后端
    const updatedSchema = {
      name: indicatorSchema.name,
      description: indicatorSchema.description,
      indicator_schema: newElements
    };
    saveIndicatorSchema(updatedSchema);
    
    message.success('删除成功');
  };

  // 保存元素
  const handleSaveElement = () => {
    elementForm.validateFields().then(values => {
      const now = new Date().toISOString();
      const newElements = [...schemaElements];
      
      if (currentElement !== null) {
        // 编辑现有元素，保留原来的name
        newElements[currentElement.index] = {
          ...values,
          name: currentElement.name, // 保留原来的name
          created_at: currentElement.created_at || now,
          updated_at: now
        };
        message.success('编辑成功');
      } else {
        // 添加新元素
        const isDuplicate = schemaElements.some(item => item.name === values.name);
        
        if (isDuplicate) {
          message.error(`指标名称"${values.name}"已存在，请使用其他名称`);
          return;
        }
        
        newElements.push({
          ...values,
          created_at: now,
          updated_at: now
        });
        message.success('添加成功');
      }
      
      // 更新本地状态
      setSchemaElements(newElements);
      
      // 保存到后端
      const updatedSchema = {
        name: indicatorSchema.name,
        description: indicatorSchema.description,
        indicator_schema: newElements
      };
      saveIndicatorSchema(updatedSchema);
      
      setIsModalVisible(false);
    });
  };

  // 过滤指标元素并排序
  const getFilteredElements = () => {
    if (!indicatorSchema || !indicatorSchema.indicator_schema) {
      return [];
    }
    
    let elements = [...indicatorSchema.indicator_schema];
    
    // 按更新时间倒序排序
    elements.sort((a, b) => {
      if (!a.updated_at) return 1;
      if (!b.updated_at) return -1;
      return new Date(b.updated_at) - new Date(a.updated_at);
    });
    
    if (searchText) {
      elements = elements.filter(item => 
        item.name.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    
    return elements;
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_, __, index) => index + 1,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record, index) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record, index)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除吗?"
            onConfirm={() => handleDelete(index)}
            okText="确认"
            cancelText="取消"
          >
            <Button danger icon={<DeleteOutlined />}>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  if (loading && !indicatorSchema) {
    return <div className={styles.loading}>加载中...</div>;
  }

  return (
    <div className={styles.container}>
      <Breadcrumb className={styles.breadcrumb}>
        <Breadcrumb.Item onClick={() => navigate('/indicator-schema')}>列表</Breadcrumb.Item>
        <Breadcrumb.Item>详情</Breadcrumb.Item>
      </Breadcrumb>

      <Card className={styles.card}>
        <div className={styles.header}>
          <Button 
            type="primary" 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/indicator-schema')}
            className={styles.backButton}
          >
            返回列表
          </Button>
          <Title level={4}>{indicatorSchema?.name} - 指标详情</Title>
        </div>

        <div className={styles.infoSection}>
          <p>领域: <span>{indicatorSchema?.domain}</span></p>
          <p>创建时间: <span>{indicatorSchema?.created_at}</span></p>
          <p>更新时间: <span>{indicatorSchema?.updated_at}</span></p>
        </div>

        <div className={styles.tableHeader}>
          <Title level={5}>指标列表</Title>
          <div className={styles.tableActions}>
            <Input.Search
              placeholder="搜索指标名称"
              allowClear
              onSearch={value => setSearchText(value)}
              onChange={e => setSearchText(e.target.value)}
              className={styles.searchInput}
              style={{ width: 250, marginRight: 16 }}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              className={styles.addButton}
            >
              添加
            </Button>
          </div>
        </div>

        <div className={styles.batchActions}>
          <Popconfirm
            title={`确定要删除选中的 ${selectedRowKeys.length} 项吗?`}
            onConfirm={handleBatchDelete}
            okText="确认"
            cancelText="取消"
            disabled={selectedRowKeys.length === 0}
          >
            <Button 
              type="primary" 
              danger
              disabled={selectedRowKeys.length === 0}
              loading={deleteLoading}
              icon={<DeleteOutlined />}
            >
              {selectedRowKeys.length}
            </Button>
          </Popconfirm>
        </div>

        <Table
          rowSelection={rowSelection}
          dataSource={getFilteredElements()}
          columns={columns}
          rowKey={(record, index) => index}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* 添加/编辑元素的对话框 */}
      <Modal
        title={currentElement !== null ? "编辑" : "添加"}
        open={isModalVisible}
        onOk={handleSaveElement}
        onCancel={() => setIsModalVisible(false)}
        okText={currentElement !== null ? "保存" : "添加"}
        cancelText="取消"
      >
        <Form
          form={elementForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input placeholder="请输入指标名称" disabled={currentElement !== null} />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <Input.TextArea placeholder="请输入指标描述" rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default IndicatorSchemaDetail; 