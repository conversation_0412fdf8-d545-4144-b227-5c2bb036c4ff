.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.card {
  margin-bottom: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.breadcrumb {
  margin-bottom: 16px;
}

.breadcrumb :global(.ant-breadcrumb-item) {
  cursor: pointer;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.backButton {
  flex-shrink: 0;
}

.infoSection {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.infoSection p {
  margin-bottom: 8px;
  color: #666;
}

.infoSection span {
  color: #333;
  font-weight: 500;
  margin-left: 8px;
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tableActions {
  display: flex;
  align-items: center;
}

.searchInput {
  margin-right: 16px;
}

.addButton {
  margin-left: auto;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #1890ff;
}

.batchActions {
  margin-bottom: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .tableHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .tableActions {
    flex-direction: column;
    width: 100%;
    gap: 12px;
  }
  
  .searchInput {
    width: 100% !important;
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .addButton {
    margin-left: 0;
    width: 100%;
  }
  
  .batchActions {
    margin-top: 12px;
    margin-bottom: 16px;
  }
} 