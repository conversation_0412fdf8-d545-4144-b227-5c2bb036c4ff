import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Card, Button, message, Form, Input, Typography, 
  Breadcrumb, Space, Table, Modal, Popconfirm 
} from 'antd';
import { 
  ArrowLeftOutlined, PlusOutlined, 
  EditOutlined, DeleteOutlined, SaveOutlined 
} from '@ant-design/icons';
import styles from './IndicatorSchemaCreate.module.css';
import { INDICATOR_SCHEMA_ENDPOINT } from '../Configs/Config';
import { createData } from '../Routers/Router';
const { Title } = Typography;

const IndicatorSchemaCreate = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [schemaElements, setSchemaElements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentElement, setCurrentElement] = useState(null);
  const [elementForm] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 批量删除选中的指标元素
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的指标');
      return;
    }
    
    setDeleteLoading(true);
    const newElements = [...schemaElements];
    
    // 按索引降序排列，这样删除时不会影响前面元素的索引
    const sortedKeys = [...selectedRowKeys].sort((a, b) => b - a);
    
    sortedKeys.forEach(index => {
      newElements.splice(index, 1);
    });
    
    setSchemaElements(newElements);
    setSelectedRowKeys([]);
    setDeleteLoading(false);
    message.success(`成功删除 ${selectedRowKeys.length} 项`);
  };

  // 创建指标Schema
  const handleCreate = async (values) => {
    if (schemaElements.length === 0) {
      message.warning('请至少添加一个指标元素');
      return;
    }
    
    try {
      setLoading(true);
      const data = {
        ...values,
        indicator_schema: schemaElements
      };
      await createData(INDICATOR_SCHEMA_ENDPOINT, data);
      message.success('创建成功');
      navigate('/indicator-schema');
    } catch (error) {
      console.error('创建指标Schema失败:', error);
      message.error('创建指标Schema失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加元素
  const showAddModal = () => {
    elementForm.resetFields();
    setCurrentElement(null);
    setIsModalVisible(true);
  };

  // 编辑元素
  const handleEdit = (element, index) => {
    setCurrentElement({ ...element, index });
    elementForm.setFieldsValue(element);
    setIsModalVisible(true);
  };

  // 删除元素
  const handleDelete = (index) => {
    const newElements = [...schemaElements];
    newElements.splice(index, 1);
    setSchemaElements(newElements);
    message.success('删除成功');
  };

  // 保存元素
  const handleSaveElement = () => {
    elementForm.validateFields().then(values => {
      // 只在添加新元素时检查是否有重名指标
      if (currentElement === null) {
        const isDuplicate = schemaElements.some(item => item.name === values.name);
        
        if (isDuplicate) {
          message.error(`指标名称"${values.name}"已存在，请使用其他名称`);
          return;
        }
      }

      const now = new Date().toISOString();
      const newElements = [...schemaElements];
      
      if (currentElement !== null) {
        // 编辑现有元素，保留原来的name
        newElements[currentElement.index] = {
          ...values,
          name: currentElement.name, // 保留原来的name
          created_at: currentElement.created_at || now,
          updated_at: now
        };
        message.success('编辑成功');
      } else {
        // 添加新元素
        newElements.push({
          ...values,
          created_at: now,
          updated_at: now
        });
        message.success('添加成功');
      }
      
      setSchemaElements(newElements);
      setIsModalVisible(false);
    });
  };

  // 过滤指标元素并排序
  const getFilteredElements = () => {
    let elements = [...schemaElements];
    
    // 按更新时间倒序排序
    elements.sort((a, b) => {
      if (!a.updated_at) return 1;
      if (!b.updated_at) return -1;
      return new Date(b.updated_at) - new Date(a.updated_at);
    });
    
    if (searchText) {
      elements = elements.filter(item => 
        item.name.toLowerCase().includes(searchText.toLowerCase())
      );
    }
    
    return elements;
  };

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (_, __, index) => index + 1,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record, index) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record, index)}
          >
            编辑
          </Button>
          <Button 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(index)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <Breadcrumb className={styles.breadcrumb}>
        <Breadcrumb.Item onClick={() => navigate('/indicator-schema')}>列表</Breadcrumb.Item>
        <Breadcrumb.Item>创建</Breadcrumb.Item>
      </Breadcrumb>

      <Card className={styles.card}>
        <div className={styles.header}>
          <Button 
            type="primary" 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/indicator-schema')}
            className={styles.backButton}
          >
            返回列表
          </Button>
          <Title level={4}>创建</Title>
        </div>

        <Form
          layout="vertical"
          form={form}
          onFinish={handleCreate}
          className={styles.form}
        >
          <Form.Item
            name="name"
            label="指标名称"
            rules={[{ required: true, message: '请输入指标名称' }]}
          >
            <Input placeholder="请输入指标名称" />
          </Form.Item>

          <Form.Item
            name="domain"
            label="领域"
            rules={[{ required: true, message: '请输入领域' }]}
          >
            <Input placeholder="请输入领域" />
          </Form.Item>

          <div className={styles.elementsSection}>
            <div className={styles.tableHeader}>
              <Title level={5}>列表</Title>
              <div className={styles.tableActions}>
                <Input.Search
                  placeholder="搜索指标名称"
                  allowClear
                  onSearch={value => setSearchText(value)}
                  onChange={e => setSearchText(e.target.value)}
                  className={styles.searchInput}
                  style={{ width: 250, marginRight: 16 }}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={showAddModal}
                  className={styles.addButton}
                >
                  添加
                </Button>
              </div>
            </div>

            <div className={styles.batchActions}>
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 项吗?`}
                onConfirm={handleBatchDelete}
                okText="确认"
                cancelText="取消"
                disabled={selectedRowKeys.length === 0}
              >
                <Button 
                  type="primary" 
                  danger
                  disabled={selectedRowKeys.length === 0}
                  loading={deleteLoading}
                  icon={<DeleteOutlined />}
                >
                  {selectedRowKeys.length}
                </Button>
              </Popconfirm>
            </div>

            <Table
              rowSelection={rowSelection}
              dataSource={getFilteredElements()}
              columns={columns}
              rowKey={(record, index) => index}
              pagination={{ pageSize: 10 }}
              className={styles.elementsTable}
            />
          </div>

          <Form.Item className={styles.submitButton}>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
              disabled={schemaElements.length === 0}
            >
              创建
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 添加/编辑元素的对话框 */}
      <Modal
        title={currentElement !== null ? "编辑" : "添加"}
        open={isModalVisible}
        onOk={handleSaveElement}
        onCancel={() => setIsModalVisible(false)}
        okText={currentElement !== null ? "保存" : "添加"}
        cancelText="取消"
      >
        <Form
          form={elementForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input placeholder="请输入指标名称" disabled={currentElement !== null} />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <Input.TextArea placeholder="请输入指标描述" rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default IndicatorSchemaCreate; 