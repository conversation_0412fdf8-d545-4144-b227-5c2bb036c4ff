import React, { useState, useEffect } from 'react';
import { Table, Button, message, Popconfirm, Space, Card, Input } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import styles from './IndicatorSchemaList.module.css';
import { INDICATOR_SCHEMA_ENDPOINT } from '../Configs/Config';
import SidebarList from '../SidebarList';
import { fetchBulk, deleteData } from '../Routers/Router';

const IndicatorSchemaList = () => {
  const [indicatorSchemas, setIndicatorSchemas] = useState([]);
  const [filteredSchemas, setFilteredSchemas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const navigate = useNavigate();

  // 获取指标Schema列表
  const fetchIndicatorSchemas = async () => {
    setLoading(true);
    try {
      console.log(INDICATOR_SCHEMA_ENDPOINT,'INDICATOR_SCHEMA_ENDPOINT');
      const data = await fetchBulk(`${INDICATOR_SCHEMA_ENDPOINT}/bulk`);
      console.log(data,'data');
      setIndicatorSchemas(data.data);
      setFilteredSchemas(data.data);
    } catch (error) {
      console.error('获取指标Schema列表失败:', error);
      message.error('获取指标Schema列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchIndicatorSchemas();
  }, []);

  // 搜索指标Schema
  const handleSearch = (value) => {
    setSearchText(value);
    if (!value) {
      setFilteredSchemas(indicatorSchemas);
    } else {
      const filtered = indicatorSchemas.filter(item => 
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredSchemas(filtered);
    }
  };

  // 删除指标Schema
  const handleDelete = async (id) => {
    try {
      await deleteData(`${INDICATOR_SCHEMA_ENDPOINT}/${id}`);
      message.success('删除成功');
      fetchIndicatorSchemas();
    } catch (error) {
      console.error('删除指标Schema失败:', error);
      message.error('删除指标Schema失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '领域',
      dataIndex: 'domain',
      key: 'domain',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            onClick={() => navigate(`/indicator-schema/edit/${record.id}`)}
          >
            编辑
          </Button>
          <Button 
            type="primary" 
            onClick={() => navigate(`/indicator-schema/detail/${record.id}`)}
          >
            详情
          </Button>
          <Popconfirm
            title="确定要删除吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确认"
            cancelText="取消"
          >
            <Button danger icon={<DeleteOutlined />}>删除</Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <div className='content-container'>
        <SidebarList />
      </div>
      <Card title="指标Schema管理" className={styles.card}>
        <div className={styles.tableHeader}>
          <Input.Search
            placeholder="搜索指标名称"
            allowClear
            onSearch={handleSearch}
            onChange={e => handleSearch(e.target.value)}
            style={{ width: 250, marginRight: 16 }}
            className={styles.searchInput}
          />
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            className={styles.addButton}
            onClick={() => navigate('/indicator-schema/create')}
          >
            新增
          </Button>
        </div>
        <Table 
          dataSource={filteredSchemas}
          columns={columns}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>
    </div>
  );
};

export default IndicatorSchemaList; 