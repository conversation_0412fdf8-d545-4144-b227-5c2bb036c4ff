import { configureStore } from '@reduxjs/toolkit';
import dashboardReducer from './components/Dashboard/dashboardSlice';
import userReducer from './components/Login/userSlice';
import workflowReducer from './components/Workflow/workflowSlice';
import knowledgeBaseReducer from './components/Workflow/Agents/KnowledgeBase/KnowledgeBaseSlice';
import agentReducer from './components/Workflow/Agents/Agents/AgentSlice';
import tabReducer  from './components/Workflow/Agents/workflows/tabSlice';
export const  store = configureStore(
    {
        reducer: {
            dashboard: dashboardReducer,
            user: userReducer,  // 添加 user reducer
            workflow: workflowReducer,
            knowledgeBases:knowledgeBaseReducer,
            agent:agentReducer,
            tab:tabReducer
        },

     }
);