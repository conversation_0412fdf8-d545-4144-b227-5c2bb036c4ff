// import { API_BASE_URL } from '../Configs/Config';
// export function subscribeToWebSocketStream(queryObject,sendQuery = false) {
//     // 打开一个WebSocket连接
//     const ws = new WebSocket(`ws://${API_BASE_URL}/ws/rewrite_query_stream`);

//     ws.onopen = function(event) {
//         console.log("WebSocket is open now.");
    
//         // 将查询对象分块发送
//         if (sendQuery){
//             const queryJson = JSON.stringify(queryObject);
//             const chunkSize = 1024; // 每个块的大小
//             for (let i = 0; i < queryJson.length; i += chunkSize) {
//               const chunk = queryJson.slice(i, i + chunkSize);
//               ws.send(chunk);
//             }
//         }
//     };

//     ws.onmessage = function(event) {
//         console.log("Received message: ", event.data);
//         // 处理接收到的消息，比如更新DOM
//     };

//     ws.onerror = function(event) {
//         console.error("WebSocket error observed:", event);
//     };

//     ws.onclose = function(event) {
//         console.log("WebSocket is closed now.");
//     };

//     // 提供一种方法来手动关闭WebSocket连接
//     return ws
// }
