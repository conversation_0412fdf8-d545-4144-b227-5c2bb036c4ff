// import { io } from 'socket.io-client';
// import {store } from '../store';
// import { setConversations, setConversationHistory } from '../components/Dashboard/dashboardSlice';
// import { fetchSession } from '../components/Dashboard/Chat/utils/apiUtil';


// let socket;

// export const sessionConversations = async () =>{

//     const user = store.getState().user;
//     // user_info = 
//     const user_info = user.user_info
//     // console.log(user_info,'useruseruseruseruser')
//     const conversations = await fetchSession(user_info.username)
//     store.dispatch(setConversations(conversations));
//     // const workflows = await fetchWorkflows()
//     // store.dispatch(setConversations(workflows))
//     // store.dispatch(setConversationHistory(conversations));

// }
// export const connectWithSocketServer = () => {
//     socket = io("http://localhost:5020");

//     socket.on('connect', () => {
//         console.log("connect with socket.io server");
//         console.log(socket.id);

//         // get session history 
//         socket.emit('session-history',{
//             sessionId: localStorage.getItem('session_id'),
//         });

//         socket.on("session-details",(data) => {
//             const {session_id,conversations } = data;
//             localStorage.setItem('session_id', session_id)
//             store.dispatch(setConversations(conversations));
//         });
 
//         socket.on('conversation-details',(conversation) =>{
            
//             store.dispatch(setConversationHistory(conversation))
//         })
//     });
// };

// export const sendConversationMessage = (message, conversation_id, assistant_message, file_urls) => {
//     socket.emit('conversation-message',{
//         session_id:localStorage.getItem("session_id"),
//         message,
//         conversation_id,
//         assistant_message,
//         file_urls
//     });
// };

// export const deleteConversations = (conversation_id) => {
//     socket.emit("conversation-delete", {
//         session_id: localStorage.getItem("session_id"),
//         conversation_id:conversation_id,
//     });
// };


// export const updateConversationTitle = (selected_conversation_id,title) => {
//     socket.emit("update-conversation-title", {
//         session_id: localStorage.getItem("sessionId"),
//         conversation_id: selected_conversation_id,
//         title:title
//     });
// };
