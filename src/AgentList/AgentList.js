import React from 'react';
import AgentListSidebar from './AgentListSidebar';
import ToolList from './ToolList';
// import Logout from '../Login/Logout';
import SidebarList from '../components/SidebarList';
import './AgentList.css';

const AgentList = () => {
  return (
    <div className="agent-list">
      <div className="agent-list-content">
        <div className="agent-list-header">
          <div className="toolbar">
            <span className="toolbar-icon">
              <i className="fa fa-wrench"></i> {/* 使用 Font Awesome 的 wrench 图标 */}
            </span>
            <span className="toolbar-text">工具栏</span>
          </div>
          <SidebarList />
        </div>
        <div className="agent-list-main">
          <AgentListSidebar />
          <ToolList />
        </div>
      </div>
    </div>
  );
};

export default AgentList;
