/* Sidebar.css */
.sidebar {
    width: 10%; /* 将宽度设置为视口宽度的20% */
    height: 100vh; /* 设置为视口高度的100% */
    overflow: auto; /* 如果内容超出高度，则可滚动 */
    background-color: black; /* 侧边栏背景颜色设置为黑色 */
  }
  
  .sidebar-item {
    padding: 10px;
    border-bottom: 1px solid #ccc; /* 每项之间的分割线 */
    color: azure; /* 项文本颜色设置为天蓝色 */
  }
  
  /* 最后一个sidebar-item没有边框 */
  .sidebar-item:last-child {
    border-bottom: none;
  }
  