import React from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import './ToolList.css';

const tools = [
    { description: "Quiz me on world capitals Quiz me on world capitals Quiz me on world capitals", data: [{ database_name: "literature", agent_count: 1, database_name_total: 200 }] },
    { description: "Plan a trip", data: [{ database_name: "travel", agent_count: 5, database_name_total: 500 }] },
    { description: "Write a thank-you note", data: [{ database_name: "courtesy", agent_count: 2, database_name_total: 100 }] },
    { description: "Write a text", data: [{ database_name: "communications", agent_count: 3, database_name_total: 300 }] }
];

const ToolCard = ({ tool }) => {
    return (
        <Grid item xs={12} sm={6} md={3}>
            <Card>
                <CardContent>
                    <Typography variant="body2" title={tool.description}>
                        {tool.description.substring(0, 50) + (tool.description.length > 50 ? '...' : '')}
                    </Typography>
                    <div>
                    {tool.data.map((item, index) => (
                        <Typography key={index} variant="body2">
                            总数: <span className="number-highlight"> {item.database_name_total}</span><br/>
                            Agent数量:<span className="number-highlight"> {item.agent_count}</span>
                        </Typography>
                    ))}
                    </div>
                </CardContent>
            </Card>
        </Grid>
    );
};

const ToolList = () => {
    return (
        <Grid container spacing={2} className="tool-list">
            {tools.map((tool, index) => (
                <ToolCard key={index} tool={tool} />
            ))}
        </Grid>
    );
};

export default ToolList;
