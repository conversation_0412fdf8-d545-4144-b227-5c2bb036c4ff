// src/App.js
import React, { useState } from "react";
// import Dashboard from "./components/Dashboard/Dashboard";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate,
  useLocation,
} from "react-router-dom";
import FileManagement from "./components/FileManager/FileManager";
import CreateKnowledgeBase from "./components/Workflow/Agents/KnowledgeBase/KnowledgeBaseCreate";
import KnowledgeBaseList from "./components/Workflow/Agents/KnowledgeBase/KnowledgeBaseList";
import KnowledgeBaseEdit from "./components/Workflow/Agents/KnowledgeBase/KnowledgeBaseEdit";
import LLMList from "./components/Workflow/Agents/llms/LLMList";
import CreateLLM from "./components/Workflow/Agents/llms/LLMCreate";
import EditLLM from "./components/Workflow/Agents/llms/LLMEdit";
import PromptList from "./components/Workflow/Agents/prompt/PromptList";
import CreatePrompt from "./components/Workflow/Agents/prompt/PromptCreate";
import EditPrompt from "./components/Workflow/Agents/prompt/PromptEdit";
import WorkflowList from "./components/Workflow/Agents/workflows/WorkflowList";
import EditWorkflow from "./components/Workflow/Agents/workflows/WorkflowEdit";
import CreateWorkflow from "./components/Workflow/Agents/workflows/WorkflowCreate";
import LlmDialogueList from "./components/Workflow/Agents/LlmDialogue/LlmDialogueList";
// import EditLlmDialogue from "./components/Workflow/Agents/LlmDialogue/LlmDialogueEdit";
import UserAssociationList from "./components/User/UserAssociation/UserAssociationList";
import UserAssociationCreate from "./components/User/UserAssociation/UserAssociationCreate";
import UserList from "./components/User/UserList";
import CreateUser from "./components/User/UserCreate";
import EditUser from "./components/User/UserEdit";
import AgentList from "./components/Workflow/Agents/Agents/AgentList";
import CreateAgent from "./components/Workflow/Agents/Agents/AgentCreate";
import AgentEdit from "./components/Workflow/Agents/Agents/AgentEdit";
import FileManagerList from './components/FileManager/FileManagerList';
import RecordViewer from './components/FileManager/RecordViewer';
import Home from "./components/Home";
import UserArticleList from "./components/ArticleManager/UserAriticleList";
import UserArticleDetail from "./components/ArticleManager/UserArticleDetail";
import TTSGenerator from "./components/Product/Audio/AudioClone/TTSGenerator";
import McpServerList from "./components/McpServer/McpServerList";
// 导入指标Schema组件
import {
  REGISTER_API_NAME,
  LOGIN_API_NAME,
  FILE_MANAGER_API_NAME,
  KNOWLEDGE_BASES_NAME,
  KNOWLEDGE_BASE_EDIT_NAME,
  KNOWLEDGE_BASE_NEW_NAME,
  LLMS_NAME,
  LLM_EDIT_NAME,
  LLM_NEW_NAME,
  PROMPTS_NAME,
  PROMPT_EDIT_NAME,
  PROMPT_NEW_NAME,
  WORKFLOWS_NAME,
  WORKFLOW_EDIT_NAME,
  WORKFLOW_NEW_NAME,
  USERS_NAME,
  USER_NEW_NAME,
  USER_EDIT_NAME,
  LLM_DIALOGUES_NAME,
  // LLM_DIALOGUE_EDIT_NAME,
  USER_ASSOCIATIONS_NAME,
  USER_ASSOCIATION_NEW_NAME,
  AGENTS_NAME,
  AGENT_EDIT_NAME,
  AGENT_NEW_NAME,
  FILE_MANAGERS_NAME,
  ARTICLE_NAME,
  ARTICLE_EDIT_NAME,
  LOGOUT_NAME,
  WORKSPACES_NAME,
  WORKSPACE_NEW_NAME,
  WORKSPACE_EDIT_NAME,
  TOOLS_NAME,
  TOOL_NEW_NAME,
  TOOL_EDIT_NAME,
  PRODUCT_AUDIO_PODCAST_NAME,
  PRODUCT_NAME,
  PRODUCT_AUDIO_CLONE_NAME,
  MCP_SERVER_NAME,
} from "./components/Configs/Config";
import LoginForm from "./components/Login/loginForm";
import RegistrationForm from "./components/Login/RegistrationForm";
import { UserProvider } from "./components/Login/UserContext"; // 确保从正确的位置导入
import FileUpload from './components/Workflow/Agents/KnowledgeBase/FileUpload';
import Logout from "./components/Login/Logout";
import WorkspaceList from "./components/Workflow/Agents/tools/workspaces/WorkspaceList";
import CreateWorkspace from "./components/Workflow/Agents/tools/workspaces/CreateWorkspace";
import EditWorkspace from "./components/Workflow/Agents/tools/workspaces/EditWorkspace";
import ToolList from "./components/Workflow/Agents/tools/tools/ToolList";
import CreateTool from "./components/Workflow/Agents/tools/tools/CreateTool";
import EditTool from "./components/Workflow/Agents/tools/tools/EditTool";
// ProtectedRoute 组件
// product 组件
import PodcastManager from "./components/Product/Audio/PodcastManager";
import ProductList from './components/Product/ProductList';
import ChatInput from "./components/Product/utils/ChatInput";
// 导入IndicatorSchema组件
import IndicatorSchemaList from "./components/IndicatorSchema/IndicatorSchemaList";
import IndicatorSchemaDetail from "./components/IndicatorSchema/IndicatorSchemaDetail";
import IndicatorSchemaCreate from "./components/IndicatorSchema/IndicatorSchemaCreate";
import IndicatorSchemaEdit from "./components/IndicatorSchema/IndicatorSchemaEdit";
const ProtectedRoute = ({ children }) => {
  const token = localStorage.getItem("token");
  const location = useLocation();
  if (!token) {
    // 重定向到登录页面，并传递当前路径
    return (
      <Navigate
        to={LOGIN_API_NAME}
        state={{ from: location.pathname }}
        replace
      />
    );
  }
  return children;
};

function App() {
  const [credentials, setCredentials] = useState({});

  const setLoginCredentials = (username = "", password = "") => {
    setCredentials({ username, password });
  };

  return (
    <UserProvider>
      <Router>
        <div className="App">
          <Routes>
          <Route
              path="file_upload"
              element={
                <ProtectedRoute>
                  <FileUpload />
                </ProtectedRoute>
              }
            />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Home />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${KNOWLEDGE_BASES_NAME}`}
              element={
                <ProtectedRoute>
                  {/* <MainLayout><KnowledgeBaseList /></MainLayout> */}
                  <KnowledgeBaseList />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${KNOWLEDGE_BASE_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreateKnowledgeBase />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${KNOWLEDGE_BASE_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <KnowledgeBaseEdit />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${LLMS_NAME}`}
              element={
                <ProtectedRoute>
                  <LLMList />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${LLM_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreateLLM />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${LLM_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <EditLLM />
                </ProtectedRoute>
              }
            />

            <Route
              path={`${PROMPTS_NAME}`}
              element={
                <ProtectedRoute>
                  <PromptList />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${PROMPT_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreatePrompt />
                </ProtectedRoute>
              }
            />
            
            <Route
              path={`${PROMPT_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <EditPrompt />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${WORKFLOWS_NAME}`}
              element={
                <ProtectedRoute>
                  <WorkflowList />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${WORKFLOW_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreateWorkflow />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${WORKFLOW_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <EditWorkflow />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${USERS_NAME}`}
              element={
                <ProtectedRoute>
                  <UserList />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${USER_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreateUser />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${USER_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <EditUser />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${LLM_DIALOGUES_NAME}`}
              element={
                <ProtectedRoute>
                  <LlmDialogueList />
                </ProtectedRoute>
              }
            />
            {/* <Route
              path={`${LLM_DIALOGUE_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <EditLlmDialogue />
                </ProtectedRoute>
              }
            /> */}
            <Route
              path={`${USER_ASSOCIATIONS_NAME}`}
              element={
                <ProtectedRoute>
                  <UserAssociationList />
                </ProtectedRoute>
              }
            />

            <Route
              path={`${USER_ASSOCIATION_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <UserAssociationCreate />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${AGENTS_NAME}`}
              element={
                <ProtectedRoute>
                  <AgentList />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${AGENT_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreateAgent />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${AGENT_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <AgentEdit />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${FILE_MANAGERS_NAME}`}
              element={
                <ProtectedRoute>
                  <FileManagerList />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${LOGIN_API_NAME}`}
              element={
                <LoginForm
                  prefillUsername={credentials.username}
                  prefillPassword={credentials.password}
                />
              }
            />
            <Route
              path={`${REGISTER_API_NAME}`}
              element={
                <RegistrationForm setLoginCredentials={setLoginCredentials} />
              }
            />
            {/* <Route
              path={`${DASHBOARD_API_NAME}`}
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route
            path="/dashboard/conversation/:conversationId"
            element={
              <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
            }
          /> */}
            <Route
              path={`${FILE_MANAGER_API_NAME}`}
              element={
                <ProtectedRoute>
                  <FileManagement />
                </ProtectedRoute>
              }
            />
            <Route
             path="/record-viewer/:id"
              element={
                <ProtectedRoute>
                  <RecordViewer />
                </ProtectedRoute>
              }
            />
            
            <Route
             path={`${ARTICLE_NAME}`}
              element={
                <ProtectedRoute>
                  <UserArticleList />
                </ProtectedRoute>
              }
            /> 
            <Route
            path={`${ARTICLE_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <UserArticleDetail />
                </ProtectedRoute>
              }
            /> 
            <Route
            path={`${LOGOUT_NAME}/`}
              element={
                  <Logout />
              }
            /> 
            
            <Route
             path={`${WORKSPACES_NAME}`}
              element={
                <ProtectedRoute>
                  <WorkspaceList />
                </ProtectedRoute>
              }
            /> 
            
            <Route
             path={`${WORKSPACE_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreateWorkspace />
                </ProtectedRoute>
              }
            /> 
            <Route
             path={`${WORKSPACE_EDIT_NAME}/:id`}
              element={
                <ProtectedRoute>
                  <EditWorkspace />
                </ProtectedRoute>
              }
            /> 
            <Route
             path={`${TOOLS_NAME}`}
              element={
                <ProtectedRoute>
                  <ToolList />
                </ProtectedRoute>
              }
            /> 
            <Route
             path={`${TOOL_NEW_NAME}`}
              element={
                <ProtectedRoute>
                  <CreateTool />
                </ProtectedRoute>
              }
            /> 
            <Route
             path={`${TOOL_EDIT_NAME}/:workspace_id/:id`}
              element={
                <ProtectedRoute>
                  <EditTool />
                </ProtectedRoute>
              }
            /> 
            
            <Route
             path={`${PRODUCT_AUDIO_PODCAST_NAME}`}
              element={
                <ProtectedRoute>
                  <PodcastManager />
                </ProtectedRoute>
              }
            /> 
            <Route
             path={`${PRODUCT_NAME}`}
              element={
                <ProtectedRoute>
                  <ProductList />
                </ProtectedRoute>
              }
            /> 
            <Route
             path={`${PRODUCT_AUDIO_CLONE_NAME}`}
              element={
                <ProtectedRoute>
                  <TTSGenerator />
                </ProtectedRoute>
              }
            /> 
            <Route
              path="/indicator-schema"
              element={
                <ProtectedRoute>
                  <IndicatorSchemaList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/indicator-schema/detail/:id"
              element={
                <ProtectedRoute>
                  <IndicatorSchemaDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/indicator-schema/create"
              element={
                <ProtectedRoute>
                  <IndicatorSchemaCreate />
                </ProtectedRoute>
              }
            />
            <Route
              path="/indicator-schema/edit/:id"
              element={
                <ProtectedRoute>
                  <IndicatorSchemaEdit />
                </ProtectedRoute>
              }
            />
            <Route
              path={`${MCP_SERVER_NAME}`}
              element={
                <ProtectedRoute>
                  <McpServerList />
                </ProtectedRoute>
              }
            />
          </Routes>
        </div>
      </Router>
    </UserProvider>
  );
}

export default App;
